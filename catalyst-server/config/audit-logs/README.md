# Audit Logs Configuration

This directory contains the configuration for the system's audit logging functionality. The `audit-logs.json` file defines which events should be tracked and logged in the system.

## Purpose

The audit logs configuration serves to:

1. Define which entity changes should be tracked
2. Specify what type of events should be logged for each entity
3. Configure which fields should trigger logging for update operations

## File Structure

The `audit-logs.json` file is organized by entity type, with each entity having an array of event configurations. Each event configuration has the following properties:

- `eventType`: A unique identifier for the type of event (e.g., "user.creation")
- `entityType`: The type of entity this event applies to
- `op`: The operation type that triggers the event ("create", "update", or "delete")
- `fields`: (Optional) For update operations, specifies which fields should trigger the event

## Important Notes

1. **Server Restart Required**: Changes to this configuration file require a server restart to take effect. The configuration is loaded at server startup.

2. **ChangeEventType Enum Synchronization**: The `eventType` values in this configuration must match the values defined in the `ChangeEventType` enum (`src/core/contracts/enums/ChangeEventType.ts`). This is because:
   - The `ChangeEvent` entity uses this enum for its `eventType` field
   - The system validates events against this enum
   - Any mismatch will cause the event tracking to fail

## Example Configuration

```json
{
  "User": [
    {
      "eventType": "user.creation",
      "op": "create",
      "entityType": "User"
    },
    {
      "eventType": "user.passwordChange",
      "op": "update",
      "fields": ["password"],
      "entityType": "User"
    }
  ]
}
```

## Related Components

- `ChangeEvent` entity: Stores the actual audit log entries
- `ChangeEventSubscriber`: Handles the creation of audit log entries
- `ChangeEventType` enum (`src/core/contracts/enums/ChangeEventType.ts`): Defines the valid event types that can be tracked
