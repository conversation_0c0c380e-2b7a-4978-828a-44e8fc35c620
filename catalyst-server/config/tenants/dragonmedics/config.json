{"applicationTitle": "44TH MEDICAL BRIGADE INNOVATION", "company": "44TH MEDICAL BRIGADE", "division": "44TH MEDICAL BRIGADE", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/dragonmedics/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/dragonmedics/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/dragonmedics/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/dragonmedics/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/dragonmedics/backdropImage.png", "submission": {"applicationTitle": "44TH MEDICAL BRIGADE INNOVATION", "welcomeTitle": "44TH MEDICAL BRIGADE\nINNOVATION CENTER", "text1": "We connect people, ideas and resources that empower Soldiers to develop creative solutions.", "text2": "The 44th Medical Brigade Innovation Dashboard is the central point of connection for Soldiers and innovators across the Brigade to submit, monitor, and advance problem statements through the innovation process to produce results. The 44th Medical Brigade Innovation Dashboard links innovators to the resources and networks needed to enable warfighter-centered innovations to solve Soldier problems.\n\nHow We Can Help You and Your Unit:\n- Connect you to available resources, SME’s, academic and research partners, and information to help you develop innovative solutions.\n- Offer opportunities to partake in specialized training to advance innovation efforts across the Brigade.\n- Advocate for additional support to advance your ideas.", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "44TH MED BDE SARC: 910-929-0747\n24/7 DOD Safe Helpline Sexual Assault Hotline: 1-877-995-5247\nFort Liberty SHARP Hotline: 910-584-4267\n", "text5": "Your problem statement will be reviewed by your Battalion or Brigade Innovation Officer. If you have any questions, contact <NAME_EMAIL>\n\n", "company": "44th MED", "division": "44th MED", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "campaign", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRules", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"campaign": {"fieldLabel": "Associated Event", "fieldDescription": "If you are submitting a problem for a specific event please select the appropriate event name in the dropdown. However, if your submission is not associated with an event, simply leave the leave the selection as “None” and proceed with your submission.", "fieldCurationDescription": "OPTIONAL: Use this field (as applicable) to associate the problem with an associated event.", "values": [{"label": "None"}, {"label": "Dragon's Lair 11"}]}, "function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "261st Multifunctional Medical Battalion", "children": [{"label": "HHD"}, {"label": "36th MCAS"}, {"label": "550th MCAS"}, {"label": "601st MCAS"}, {"label": "602nd MCAS"}, {"label": "690th MCAS"}, {"label": "51st MLC"}, {"label": "155th PM DET"}, {"label": "172nd PM DET"}, {"label": "24th OPTO DET"}]}, {"label": "HHC BDE"}, {"label": "16th Hospital Center", "children": [{"label": "247th FRSD"}, {"label": "541st FRSD"}, {"label": "248th MDVSS"}, {"label": "759th FRSD"}, {"label": "528th COSC"}, {"label": "14th CSH"}]}, {"label": "531st Hospital Center", "children": [{"label": "39th FRSD"}, {"label": "772nd FRSD"}, {"label": "501st MCAS"}, {"label": "72nd MDVSS"}]}, {"label": "257th Medical Company Dental Area Support"}]}