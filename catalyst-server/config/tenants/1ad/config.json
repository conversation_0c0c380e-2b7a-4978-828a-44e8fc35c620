{"applicationTitle": "IRON FORGE INNOVATION", "company": "IRON FORGE", "division": "IRON FORGE", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/1ad/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/1ad/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/1ad/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/1ad/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/1ad/backdropImage.png", "submission": {"applicationTitle": "IRON FORGE INNOVATION", "welcomeTitle": "IRON\nINNOVATION CENTER", "text1": "", "text2": "", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "", "text5": "Your problem statement will be reviewed by you Brigade or Division Innovation Officer. If you have any questions, contact <NAME_EMAIL>.", "company": "IRON FORGE", "division": "IRON FORGE", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRules", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "1st Brigade Combat Team (1/1AD)"}, {"label": "2nd Brigade Combat Team (2/1AD)"}, {"label": "3rd Brigade Combat Team (3/1AD)"}, {"label": "Division Artillery (DIVARTY)"}, {"label": "Combat Aviation Brigade (CAB)"}, {"label": "Division Sustainment Brigade (DSB)"}, {"label": "Headquarters and Headquarters Battalion (HHBn)"}]}