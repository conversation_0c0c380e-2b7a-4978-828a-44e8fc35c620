{"applicationTitle": "<name> INNOVATION", "company": "<name>", "division": "", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/<handle>/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/<handle>/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/<handle>/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/<handle>/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/<handle>/backdropImage.png", "submission": {"applicationTitle": "<name> INNOVATION", "welcomeTitle": "<name>\nINNOVATION CENTER", "text1": "We connect people, ideas and resources that empower Soldiers to develop creative solutions.", "text2": "<main_blurb>/n<next_line>/n/n<next_paragraph>", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "<phone_number_1>\n<phone_number_2>\n<etc>", "text5": "Your problem statement will be reviewed by your Battalion or Brigade Innovation Officer. If you have any questions, contact us at <email_address>\n\n", "company": "<name>", "division": "", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "campaign", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRules", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"campaign": {"fieldLabel": "Associated Event", "fieldDescription": "If you are submitting a problem for a specific event please select the appropriate event name in the dropdown. However, if your submission is not associated with an event, simply leave the leave the selection as “None” and proceed with your submission.", "fieldCurationDescription": "OPTIONAL: Use this field (as applicable) to associate the problem with an associated event.", "values": [{"label": "None"}, {"label": "<event_name>"}]}, "function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "<value_1>", "children": [{"label": "<value_1_1>"}, {"label": "<value_1_2>"}]}, {"label": "<value_2>", "children": [{"label": "<value_2_1>"}, {"label": "<value_2_2>"}]}, {"label": "<value_3>"}]}