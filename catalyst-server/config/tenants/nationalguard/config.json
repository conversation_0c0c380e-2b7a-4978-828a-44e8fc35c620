{"applicationTitle": "NATIONAL GUARD INNOVATION", "company": "NATIONAL GUARD", "division": "NATIONAL GUARD", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/nationalguard/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/nationalguard/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/nationalguard/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/nationalguard/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/nationalguard/backdropImage.png", "submission": {"applicationTitle": "NATIONAL GUARD INNOVATION", "welcomeTitle": "NATIONAL GUARD\nINNOVATION CENTER", "text1": "We connect people, ideas and resources that empower Soldiers to develop creative solutions.", "text2": "", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "", "text5": "Your problem statement will be reviewed by your region Pathfinder Warfighter Innovation Chief.\n\nIf you have any questions, please contact your region’s PWIC.\n\n", "company": "NATIONAL GUARD", "division": "NATIONAL GUARD", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRules", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "West Virginia National Guard", "children": [{"label": "111th Engineer BDE"}, {"label": "77th BDE Troop Command"}, {"label": "Medical Command"}, {"label": "Recruiting & Retention Command"}, {"label": "Special Operations Detachment - Europe"}, {"label": "197th Regional Training Institute"}, {"label": "130th Airlift Wing (C-130H)"}, {"label": "167th Airlift Wing (C-17 Globemaster III)"}, {"label": "Army Interagency Training & Education Center"}, {"label": "35th Civil Support Team"}, {"label": "35th CBRN Enhanced Response Force"}, {"label": "1st BN, 201st Field Artillery Regiment"}, {"label": "Counter Drug"}]}, {"label": "Montana National Guard", "children": [{"label": "95th Troop Command"}, {"label": "1889th RSG"}, {"label": "1-163 INF"}, {"label": "190th CSSB"}, {"label": "495th CSSB"}, {"label": "189 AVN"}, {"label": "MT Regional Training Institute"}, {"label": "JFHQ"}, {"label": "Training Site"}]}]}