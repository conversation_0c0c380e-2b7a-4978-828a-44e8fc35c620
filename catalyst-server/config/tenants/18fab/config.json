{"applicationTitle": "Steel Innovation", "company": "Steel", "division": "Steel Innovation", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/18fab/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/18fab/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/18fab/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/18fab/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/18fab/backdropImage.png", "submission": {"applicationTitle": "Steel Innovation", "welcomeTitle": "Steel\nInnovation Center", "text1": "We connect people, ideas, and resources to empower all Steel Soldiers to develop creative solutions to solve tactical problems.", "text2": "Welcome to the Steel Innovation Portal!\n\nDo you have any ideas for new equipment, technology, or programs to make the Brigade better? Please submit your ideas here; the Brigade innovation team is looking for Soldier feedback on how to improve our equipment and organization. As part of the Corps innovation program, 18th FA Brigade has access to innovation partners and resources to try and make good ideas a reality.", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "SHARP contacts:\nBDE VA: 910-709-1858\nBDE SARC: 910-689-7233\nDoD Safe Helpline: 877-995-5247", "text5": "Your problem statement will be reviewed by your Brigade or Division Innovation Officer.\n\n", "company": "Steel", "division": "Steel Innovation", "favicon": "favicon"}, "curation": {"applicationTitle": "Steel Innovation", "curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "campaign", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRules", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"campaign": {"fieldLabel": "Associated Event", "fieldDescription": "If you are submitting a problem for a specific event please select the appropriate event name in the dropdown. However, if your submission is not associated with an event, simply leave the leave the selection as “None” and proceed with your submission.", "fieldCurationDescription": "OPTIONAL: Use this field (as applicable) to associate the problem with an associated event.", "values": [{"label": "None"}, {"label": "Dragon's Lair 11"}]}, "function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "188th BSB", "children": [{"label": "HHB BDE"}, {"label": "HSC"}, {"label": "206th Signal Company"}]}, {"label": "3-321 FA", "children": [{"label": "A Battery"}, {"label": "B Battery"}, {"label": "C Battery"}, {"label": "583rd FSC"}, {"label": "HHB"}]}, {"label": "3-27 FA", "children": [{"label": "A Battery"}, {"label": "B Battery"}, {"label": "C Battery"}, {"label": "135th FSC"}, {"label": "HHB"}]}]}