{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module",
    "project": "tsconfig.json"
  },
  "ignorePatterns": ["migrations/", "tests/"],
  "plugins": ["@typescript-eslint", "prettier"],
  "extends": [
    "plugin:@typescript-eslint/recommended", // Uses the recommended rules from the @typescript-eslint/eslint-plugin
    "plugin:prettier/recommended" // Enables eslint-plugin-prettier and eslint-config-prettier. This will display prettier errors as ESLint errors. Make sure this is always the last configuration in the extends array.
  ],
  "rules": {
    "prettier/prettier": [
      "error",
      {
        "tabWidth": 2,
        "semi": true,
        "singleQuote": true,
        "trailingComma": "all",
        "printWidth": 120,
        "endOfLine": "auto"
      }
    ],
    "@typescript-eslint/no-unused-vars": "off"
  }
}
