
GraphQL
// get tenant
curl -X POST http://localhost:8080/curator -H 'Content-Type: application/json' -d '{ "query" : "{ getTenant(handleOrAlias: \"monumentsMen\") { id name handle meta { config theme } } }" }'
// login
curl -X POST http://localhost:8080/curator -H 'Content-Type: application/json' -d '{ "query": "mutation { login ( userName: \"<EMAIL>\", password: \"passcurator\", tenantHandle: \"monumentsMen\",){ user { id } token } }" }'

