#!/bin/sh

. $(dirname "$(readlink -f "$0")")/config.sh


echo "\n*************************************************************************************"
echo "    Logging in to Azure repository..."
echo "*************************************************************************************\n"
echo az acr login --name catalystinnovation
az acr login --name catalystinnovation
echo "\n*************************************************************************************"
echo "    Pushing images $PACKAGE_VERSION to repository..."
echo "*************************************************************************************\n"
echo docker push catalystinnovation.azurecr.us/catalyst-server:$PACKAGE_VERSION
docker push catalystinnovation.azurecr.us/catalyst-server:$PACKAGE_VERSION
echo docker push catalystinnovation.azurecr.us/catalyst-api-server:$PACKAGE_VERSION
docker push catalystinnovation.azurecr.us/catalyst-api-server:$PACKAGE_VERSION
