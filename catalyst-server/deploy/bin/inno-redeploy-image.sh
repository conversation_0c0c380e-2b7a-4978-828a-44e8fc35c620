#!/bin/sh

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
. $SCRIPT_DIR/config.sh

if [ -z ${1+x} ]; then
    echo 'PLATFORM arg required! Valid values are test | production | staging'
    exit 1
fi
PLATFORM=$1

echo "\n*************************************************************************************"
echo "    Updating ${PLATFORM} deployment secrets..."
echo "*************************************************************************************\n"
if [ $PLATFORM = "production" ]; then
    echo kubectl --context catalystServerCluster delete secret prod-secrets
    kubectl --context catalystServerCluster delete secret prod-secrets
    echo kubectl --context catalystServerCluster create secret generic prod-secrets --from-env-file=.env-prod
    kubectl --context catalystServerCluster create secret generic prod-secrets --from-env-file=.env-prod
elif [ $PLATFORM = "test" ]; then
    echo kubectl --context catalystServerTestCluster delete secret test-secrets
    kubectl --context catalystServerTestCluster delete secret test-secrets
    echo kubectl --context catalystServerTestCluster create secret generic test-secrets --from-env-file=.env-test
    kubectl --context catalystServerTestCluster create secret generic test-secrets --from-env-file=.env-test
elif [ $PLATFORM = "staging" ]; then
    echo kubectl --context catalystServerTestCluster delete secret staging-secrets
    kubectl --context catalystServerTestCluster delete secret staging-secrets
    echo kubectl --context catalystServerTestCluster create secret generic staging-secrets --from-env-file=.env-staging
    kubectl --context catalystServerTestCluster create secret generic staging-secrets --from-env-file=.env-staging
else
    echo "No platform found for $PLATFORM"
fi

echo "\n*************************************************************************************"
echo "    Updating ${PLATFORM} deployment yaml file..."
echo "*************************************************************************************\n"
if [ $PLATFORM = "production" ]; then
    echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-server-prod.yml
    sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-server-prod.yml
    echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-api-server-prod.yml
    sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-api-server-prod.yml
elif [ $PLATFORM = "test" ]; then
    echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-server-test.yml
    sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-server-test.yml
    echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-api-server-test.yml
    sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-api-server-test.yml
elif [ $PLATFORM = "staging" ]; then
    echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-server-staging.yml
    sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-server-staging.yml
    echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-api-server-staging.yml
    sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$PACKAGE_VERSION/g" $SCRIPT_DIR/../catalyst-api-server-staging.yml
else
    echo "No platform found for $PLATFORM"
fi

echo "\n*************************************************************************************"
echo "    Updating ${PLATFORM} k8s nodes with images $PACKAGE_VERSION..."
echo "*************************************************************************************\n"

if [ $PLATFORM = "production" ]; then
    echo kubectl --context catalystServerCluster apply -f $SCRIPT_DIR/../catalyst-server-prod.yml
    kubectl --context catalystServerCluster apply -f $SCRIPT_DIR/../catalyst-server-prod.yml
    echo kubectl --context catalystServerCluster apply -f $SCRIPT_DIR/../catalyst-api-server-prod.yml
    kubectl --context catalystServerCluster apply -f $SCRIPT_DIR/../catalyst-api-server-prod.yml
elif [ $PLATFORM = "test" ]; then
    echo kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-server-test.yml
    kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-server-test.yml
    echo kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-api-server-test.yml
    kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-api-server-test.yml
elif [ $PLATFORM = "staging" ]; then
    echo kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-server-staging.yml
    kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-server-staging.yml
    echo kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-api-server-staging.yml
    kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../catalyst-api-server-staging.yml
else
    echo "No platform found for $PLATFORM"
fi