#!/bin/sh

. $(dirname "$(readlink -f "$0")")/config.sh

echo "\n*************************************************************************************"
echo "    Compiling and building server..."
echo "*************************************************************************************\n"
npm run build
# temp until web artifacts are deployed
npm run copy-web-build

echo "\n*************************************************************************************"
echo "    Rebuilding server image $PACKAGE_VERSION..."
echo "*************************************************************************************\n"
echo docker build -f deploy/prod.Dockerfile -t catalyst-server:$PACKAGE_VERSION -t catalystinnovation.azurecr.us/catalyst-server:$PACKAGE_VERSION .
docker build -f deploy/prod.Dockerfile -t catalyst-server:$PACKAGE_VERSION -t catalystinnovation.azurecr.us/catalyst-server:$PACKAGE_VERSION .

echo "\n*************************************************************************************"
echo "    Rebuilding api server image $PACKAGE_VERSION..."
echo "*************************************************************************************\n"
echo docker build -f deploy/prod.api.Dockerfile -t catalyst-api-server:$PACKAGE_VERSION -t catalystinnovation.azurecr.us/catalyst-api-server:$PACKAGE_VERSION .
docker build -f deploy/prod.api.Dockerfile -t catalyst-api-server:$PACKAGE_VERSION -t catalystinnovation.azurecr.us/catalyst-api-server:$PACKAGE_VERSION .