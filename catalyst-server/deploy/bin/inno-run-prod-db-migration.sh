#!/bin/sh

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
. $SCRIPT_DIR/config.sh

echo "\n*************************************************************************************"
echo "    Creating Migrations..."
echo "*************************************************************************************\n"
echo NODE_ENV=production npm run dbCli create-migrations
NODE_ENV=production npm run dbCli create-migrations
echo "\n*************************************************************************************"
echo "    Running Any Pending Migrations..."
echo "*************************************************************************************\n"
echo NODE_ENV=production npm run dbCli run-migrations
NODE_ENV=production npm run dbCli run-migrations