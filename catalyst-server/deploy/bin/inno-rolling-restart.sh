#!/bin/sh

. $(dirname "$(readlink -f "$0")")/config.sh

if [ -z ${1+x} ]; then
    echo 'PLATFORM arg required! Valid values are test | production | staging'
    exit 1
fi
PLATFORM=$1

echo "\n*************************************************************************************"
echo "    Rolling restart of $PLATFORM deployment/catalyst-server..."
echo "*************************************************************************************\n"


if [ $PLATFORM = 'production' ]; then
    echo kubectl --context catalystServerCluster rollout restart deployment/catalyst-server
    kubectl --context catalystServerCluster rollout restart deployment/catalyst-server
    echo kubectl --context catalystServerCluster rollout restart deployment/catalyst-api-server
    kubectl --context catalystServerCluster rollout restart deployment/catalyst-api-server
elif [ $PLATFORM = 'test' ]; then
    echo kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-server
    kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-server
    echo kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-api-server
    kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-api-server
elif [ $PLATFORM = 'staging' ]; then
    echo kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-server-staging
    kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-server-staging
    echo kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-api-server-staging
    kubectl --context catalystServerTestCluster rollout restart deployment/catalyst-api-server-staging
else
    echo "No platform found for $PLATFORM"
fi

