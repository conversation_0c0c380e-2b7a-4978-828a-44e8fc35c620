#!/bin/sh

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
. $SCRIPT_DIR/config.sh

if [ -z ${1+x} ]; then
    echo 'PLATFORM arg required! Valid values are test | production'
    exit 1
fi
PLATFORM=$1

echo "\n*************************************************************************************"
echo "    Updating ${PLATFORM} ingress configutation"
echo "*************************************************************************************\n"

if [ $PLATFORM = "production" ]; then
    echo kubectl --context catalystServerCluster apply -f $SCRIPT_DIR/../ingress-prod.yml
    kubectl --context catalystServerCluster apply -f $SCRIPT_DIR/../ingress-prod.yml
elif [ $PLATFORM = "test" ]; then
    echo kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../ingress-test.yml
    kubectl --context catalystServerTestCluster apply -f $SCRIPT_DIR/../ingress-test.yml
else
    echo "No platform found for $PLATFORM"
fi