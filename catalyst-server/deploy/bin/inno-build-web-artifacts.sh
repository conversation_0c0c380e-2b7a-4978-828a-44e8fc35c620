#!/bin/sh

. $(dirname "$(readlink -f "$0")")/config.sh


# temp until we have web artifacts deployed
echo "\n*************************************************************************************"
echo "    Building catalyst-innovation-client..."
echo "*************************************************************************************\n"
cd ../catalyst-innovation-client/; npm install; npm run prod-build-web
echo "\n*************************************************************************************"
echo "    Building catalyst-curator-client..."
echo "*************************************************************************************\n"
cd ../catalyst-curator-client/; npm install; npm run prod-build-web
