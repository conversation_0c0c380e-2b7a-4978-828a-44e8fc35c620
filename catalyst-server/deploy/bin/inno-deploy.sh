#!/bin/sh

# NOTE: inc package version and tag with git tag catalyst-server-1.0.x

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")

. $SCRIPT_DIR/config.sh

echo "\n*************************************************************************************"
echo "    Building and deploying version $PACKAGE_VERSION"
echo "*************************************************************************************\n"
$SCRIPT_DIR/inno-build-web-artifacts.sh "$@" && \
$SCRIPT_DIR/inno-build-server-image.sh "$@" && \
# @TODO - should automate schema migrations here
$SCRIPT_DIR/inno-push-server-image.sh "$@" && \
$SCRIPT_DIR/inno-redeploy-image.sh "$@"
# This is only necessary when the image version is unchanged (i.e. redeploying w/ same version number)
# $SCRIPT_DIR/inno-rolling-restart.sh "$@"
# This is only necessary when the ingress config has been updated
# $SCRIPT_DIR/inno-redeploy-ingress.sh "$@"