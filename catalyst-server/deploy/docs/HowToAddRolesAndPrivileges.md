# How to manage Roles and Privileges

_Note: Roles will simply become 'privilegeGroups', but are currently used as a convenience to control access to parts of the ui_  
_Right now roles must be managed separately from privileges and privilegeGroups_

## Super tenants are privileged tenants that allow access to other tenants data via the Analytics features

### How to set up a Tenant as a 'super' Tenant (assuming the tenant is 'testTenant'):

```
// create a privilege group in testTenant for access to multiple tenants (returns the new group's id)
npm run dbCli tenant-privilege-group testTenant 'Analyst Access' testTenant monumentsMen starshipTroopers
```

### Add the new privilege group to all testTenant users

```
npm run dbCli add-privilege-groups-to-all testTenant 67a091e0-819b-4ea3-94ed-adac55074127
```

### Add an analyst role to all users

```
npm run dbCli add-roles-to-all testTenant analyst
```

### How to add a tenant to an already existing Super Tenant's privilege group

```
// add rebelAlliance and theTenThousand to super Tenant testTenant
npm run dbCli add-to-tenant-privilege-group testTenant 67a091e0-819b-4ea3-94ed-adac55074127 rebelAlliance theTenThousand
```

### _Note: there are remove operations available for these scenarios as well as operations that apply to individual users._