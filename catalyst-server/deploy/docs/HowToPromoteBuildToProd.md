
Make sure that package.json has required version and that all local build files (.env, etc.) are compatible with this version.

The safest way to do this is:

1) Checkout the version tag to a new branch
   
   *git checkout catalyst-server-1.0.x -b catalyst-server-1.0.x_branch*

2) Make sure your .env is set to the proper environment (i.e. run 'npm run test-env' for test or copy .env-test to .env)  For prod or test, use the NODE_ENV=production flag
   
3) Make sure the schema is up to date: **See HowMigrateSchema.md**

4) Add a new tenant if necessary **See HowToAddANewTenant.md**

5) Run *deploy/bin/inno-redeploy-image.sh production*

6) Commit any changes created (specifcally updated .yml and migration files) and note the commit hash
   
7) Need to carry this build files forward so merge these updates into main w/ 
   
   *git checkout main*
   
   *git merge --no-commit --no-ff hash_from_tag_branch_commit*

   *git diff --cached*

If the merge looks good commit add and commit:

   *git add .; git commit -m "merged build files from prod deployment (version)"*