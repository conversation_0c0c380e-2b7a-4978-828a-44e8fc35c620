# How to setup and add a new Tenant

## Tenant prerequisites

*   <PERSON> has notes [here](https://acmegeneral.atlassian.net/wiki/spaces/AI/pages/1992916993/New+Tenant+configuration+template)
*   Determine tenant 'name', 'handle', and 'label'
*   Create sLogo, mLogo, lLogo, logo, and backdropImage
*   Deploy images to blob storage 'public/' folder named with the tenant handle
*   Add a directory under 'config/tenants/' using the new tenant handle
*   Create (or copy) config.json and theme.json  file and add to the new directory
*   Currently in theme.json, only the value for 'primary' is required for a new tenant

## Add the new Tenant to test for QA

Make sure your .env is set to the proper environment (i.e. run 'npm run test-env' for test or copy .env-test to .env)  
_**Note**: For prod or test, prefix with the NODE\_ENV=production flag  i.e.  'NODE\_ENV=production npm run dbCli tenant ...'_

*   Add the tenant as an alias for 'monumentsMen' on test
*   Add the new tenants config, theme, and content files to test

### Example new tenant with tenant handle 'testTenant':

```
NODE_ENV=production npm run dbCli tenant-alias monumentsMen testTenant "Test Tenant"

NODE_ENV=production npm run dbCli update-config monumentsMen config/tenants/testTenant/config.json testTenant

NODE_ENV=production npm run dbCli update-theme monumentsMen config/tenants/testTenant/theme.json testTenant

NODE_ENV=production npm run dbCli update-serverConfig monumentsMen config/tenants/testTenant/serverConfig.json testTenant

NODE_ENV=production npm run dbCli update-content monumentsMen config/content/content.json testTenant
```

## DNS Setup

Dns currently directs all 'wildcard' matches to the test environment, so when testing a new tenant alias on test, theres no need to make a change.

Once approved on test and deployed to production, a cname entry for the tenant handle needs to be added using the domain manager on godaddy.com that points to our production IP address

## Add the new Tenant to production

Make sure your .env is set to the proper environment (i.e. run 'npm run test-env' for test or copy .env-test to .env)  
_**Note**: For prod or test, prefix with the NODE\_ENV=production flag  i.e.  'NODE\_ENV=production npm run dbCli tenant ...'_

*   Add the new tenant and then add it's 'content' metadata

### Example new tenant with tenant hanlde 'testTenant'

```
NODE_ENV=production npm run dbCli tenant "Test Tenant" testTenant settset-p@ss! \
config/tenants/testTenant/config.json config/tenants/testTenant/theme.json "Test Label"

NODE_ENV=production npm run dbCli update-content testTenant config/content/content.json
```