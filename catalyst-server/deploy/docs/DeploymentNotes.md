# Azure Setup

### In Portal - Create container repository (catalystinnovation) (test is same)

### In Portal - Create resource group (catalyst-server) (catalyst-server-test)

### In Portal - Create db w/ catalyst_admin (standard size) (catalyst-db) (catalyst-db-test)

### In Portal - create blob storage (catalystserverstorage) (test is same)

### In Portal - setup cors for blob storage

### In Portal - Create a vnet (test includes '-test' after server in each name):

```
az network vnet create
--resource-group catalyst-server
--name catalyst-server-vnet
--address-prefixes ***********/16
--subnet-name catalyst-server-subnet
--subnet-prefix ***********/24
```

### In Portal - Enable Sql.Database Service Endpoint in virtual network (go to the vnet then the subnet)

### In Portal - Add virtual network to postgres endpoints security configuration

### create a managed identity:

```
az identity create --name catalyst_identity --resource-group catalyst-server (catalyst-test_identity, catalyst-test-server)
```

### assign the "Network Contibutor" role to the identity

### NOTE: one of these assignees is not necessary (figure out which id is required)

### The first assignee is the identity's 'principleId" and the second is the identity's 'clientId' (which one?)

```
VNET_ID=$(az network vnet show --resource-group catalyst-server --name catalyst-server-vnet --query id -o tsv)
SUBNET_ID=$(az network vnet subnet show --resource-group catalyst-server --vnet-name catalyst-server-vnet --name catalyst-server-subnet --query id -o tsv)
az role assignment create --assignee 69bc8dc7-ad3e-4b6c-a035-8ad98f4925cd --scope $VNET_ID --role "Network Contributor"
az role assignment create --assignee ac7f92f5-22c1-4cd7-b421-35d2ea409d57 --scope $VNET_ID --role "Network Contributor"
```

### Create cluster and attach container registry

### the --assign-identity is the 'resourceId' (id field) of the catavolt_identity

```
az aks create 
--resource-group catalyst-server 
--name catalystServerCluster 
--node-count 1 
--enable-addons monitoring 
--ssh-key-value ~/.ssh/catalyst.pub 
--attach-acr catalystinnovation 
--network-plugin kubenet 
--service-cidr 10.0.0.0/16 
--dns-service-ip ********* 
--pod-cidr **********/16 
--docker-bridge-address **********/16 
--vnet-subnet-id $SUBNET_ID 
--enable-managed-identity 
--assign-identity /subscriptions/4895b9e8-c898-47d8-908f-849383793207/resourcegroups/catalyst-server/providers/Microsoft.ManagedIdentity/userAssignedIdentities/catalyst_identity
```

```
# added these later
--node-count 1 \
--vm-set-type VirtualMachineScaleSets \
--enable-cluster-autoscaler \
--min-count 1 \
--max-count 1000
```

### (test)

```
az aks create 
--resource-group catalyst-server-test 
--name catalystServerTestCluster 
--node-count 1 
--enable-addons monitoring 
--ssh-key-value ~/.ssh/catalyst.pub 
--attach-acr catalystinnovation 
--network-plugin kubenet 
--service-cidr 10.0.0.0/16 
--dns-service-ip ********* 
--pod-cidr **********/16 
--docker-bridge-address **********/16 
--vnet-subnet-id $SUBNET_ID 
--enable-managed-identity 
--assign-identity /subscriptions/4895b9e8-c898-47d8-908f-849383793207/resourcegroups/catalyst-server-test/providers/Microsoft.ManagedIdentity/userAssignedIdentities/catalyst-test_identity
```

### Get credentials

```
az aks get-credentials --resource-group catalyst-server --name catalystServerCluster
az aks get-credentials --resource-group catalyst-server-test --name catalystServerTestCluster
```

### Deploy services (after Gateway is setup)

```
kubectl --context catalystServerCluster apply -f docker/catalyst-server.yml
```

### test switch context to catalystServerTestCluster

```
kubectl --context catalystServerTestCluster apply -f docker/catalyst-server-test.yml
```

## Gateway setup

### create the ip (catalyst-server-test-ip, catalyst-server-test)

```
az network public-ip create -n catalyst-server-ip -g catalyst-server --allocation-method Static --sku Standard
```

### add another subnet (catalyst-test-gateway-subnet, catalyst-server-test, catalyst-server-test-vnet)

```
az network vnet subnet create 
--name catalyst-gateway-subnet 
--resource-group catalyst-server 
--vnet-name catalyst-server-vnet 
--address-prefix ***********/24
```

### create the application gateway

```
az network application-gateway create 
-n catalyst-server-gateway 
-l usgovvirginia 
-g catalyst-server 
--sku Standard_v2 
--public-ip-address catalyst-server-ip 
--vnet-name catalyst-server-vnet 
--subnet catalyst-gateway-subnet
```

### (test)

```
az network application-gateway create 
-n catalyst-server-test-gateway 
-l usgovvirginia 
-g catalyst-server-test 
--sku Standard_v2 
--public-ip-address catalyst-server-test-ip 
--vnet-name catalyst-server-test-vnet 
--subnet catalyst-test-gateway-subnet
```

### In Portal - Set up TLS 'Listeners' in the Azure admin interface for the Gateways (choose the preconfigured SSL recommendations)

### set up the Application Gateway add-on for k8s for the cluster (substitute appropriate 'test' values)

```
appgwId=$(az network application-gateway show -n catalyst-server-gateway -g catalyst-server -o tsv --query "id")
az aks enable-addons -n catalystServerCluster -g catalyst-server -a ingress-appgw --appgw-id $appgwId
```

### set up TLS for the Ingress controller (using same secret for test)

### these are the godaddy ssl assets. See https://acmegeneral.atlassian.net/wiki/spaces/AI/pages/1929609217/Deployment+How+To+s

### NOTE: the site crt and the intermediate crt are appended together in one file (combined_certs.crt) for use here (however, don't include very last cert (the root ca cert) as it's not necessary)

```
kubectl --context catalystServerCluster create secret tls catalyst-server-tls-secret --cert combined_certs.crt --key generated-private-key-utf8.txt
```

### (run this on the test cluster also - catalystServerTestCuster)

```
kubectl --context catalystServerTestCluster create secret tls catalyst-server-tls-secret --cert combined_certs.crt --key generated-private-key-utf8.txt
```

### make sure the k8s yaml file is set up to use Azure Application Gateway for https Ingress

### https://github.com/Azure/application-gateway-kubernetes-ingress/blob/master/docs/tutorials/tutorial.general.md#deploy-guestbook-application

## \*\*\*\*\*\*\*\*\*\*\*\*\* Additional workflows \*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*

```
get the current cluster (context)
kubectl config current-context
```

### set the current context

```
kubectl config use-context catalystServerTestCluster
```

### get the service

```
kubectl get service catalyst-server
```

### Stop the cluster

```
az aks stop --name catalystServerCluster --resource-group catalyst-server
```

### start the cluster

```
az aks start --name catalystServerCluster --resource-group catalyst-server
```

### Show the cluster

```
az aks show --name catalystServerCluster --resource-group catalyst-server
```

### Scale

```
kubectl scale --replicas=3 deployment/catalyst-server
```

### Update a deployment

```
kubectl set image deployment catalyst-server catalyst-server=catalystinnovation.azurecr.us/catalyst-server:x.x.x
```

### View pods

```
kubectl get pods
```

### Tail logs

```
kubectl logs --follow -l app=catalyst-server
kubectl -n default logs -f deployment/catalyst-server --all-containers=true --since=10m
```

### Login and debug a service (default namespace)

```
kubectl run -it --rm --image=curlimages/curl curly -- sh
# on the instance
kubctrl get pods (with optional -n )
kubectrl describe <pod_name>
curl POST http://:8080/curator -H 'Content-Type: application/json' -d '{ "key": "value........" }'
#see curl examples in curl-examples.txt
```

### \*\*\*\*\*\*\*\*\*\*\*\* Connect to a running container on a node

```
kubectl exec --stdin --tty catalyst-server-5f58f7d5f5-tlzjq -- /bin/sh
```

### get the node name

```
kubectl get nodes -o wide
```

### create a debugger priviliedged pod

```
kubectl debug node/aks-nodepool1-17547434-vmss000003 -it --image=mcr.microsoft.com/dotnet/runtime-deps:6.0
chroot /host
```

### see the kublet log

```
journalctl -u kubelet -o cat
```

### when finished

```
kubectl delete pod node-debugger-aks-nodepool1-17547434-vmss000003-25cv5
```

catalyst_identity:  
{  
"clientId": "ac7f92f5-22c1-4cd7-b421-35d2ea409d57",  
"clientSecretUrl": "https://control-usgovvirginia.identity.usgovcloudapi.net/subscriptions/4895b9e8-c898-47d8-908f-849383793207/resourcegroups/catalyst-server/providers/Microsoft.ManagedIdentity/userAssignedIdentities/catalyst_identity/credentials?tid=959df200-7301-429c-bca6-b20bfe92f7f5&oid=69bc8dc7-ad3e-4b6c-a035-8ad98f4925cd&aid=ac7f92f5-22c1-4cd7-b421-35d2ea409d57",  
"id": "/subscriptions/4895b9e8-c898-47d8-908f-849383793207/resourcegroups/catalyst-server/providers/Microsoft.ManagedIdentity/userAssignedIdentities/catalyst_identity",  
"location": "usgovvirginia",  
"name": "catalyst_identity",  
"principalId": "69bc8dc7-ad3e-4b6c-a035-8ad98f4925cd",  
"resourceGroup": "catalyst-server",  
"tags": {},  
"tenantId": "959df200-7301-429c-bca6-b20bfe92f7f5",  
"type": "Microsoft.ManagedIdentity/userAssignedIdentities"  
}
