
# This example uses the 'test' environment

Make sure your .env is set to the proper environment (i.e. run 'npm run test-env' for test or copy .env-test to .env)  
For prod or test, use the NODE_ENV=production flag


1. **npx run test-env**
2. **NODE_ENV=production npx mikro-orm migration:create** (creates the migration in the 'migrations' directory)
3. **NODE_ENV=production npx mikro-orm migration:up** (this will apply any pending migrations)
4. **commit the migrations to the repository**