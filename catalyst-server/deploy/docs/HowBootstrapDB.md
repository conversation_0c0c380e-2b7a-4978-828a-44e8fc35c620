
# Required Bootstrap - From scratch
1) Create new database server and create db 'catalyst_db'
2) Use (or create) the coresponding .env file (i.e. .env-test) with the required info
3) Make that .env file active (i.e. cp .env-test .env)
4) run "NODE_ENV=production npm run dbCli createSchema"
5) run "NODE_ENV=production npm run dbCli bootstrap"
# Add a new Tenant 
6) run NODE_ENV=production npm run dbCli tenant <name> <handle> <admin_pass>

# If the db exists and schema (or data) cannot be migrated:
Before performing the above stops, drop the db 
1) Stop cluster: az aks stop --name catalystServerTestCluster --resource-group catalyst-server-test
2) Drop and recreate db (catalyst-db)
3) Start the cluster: az aks start --name catalystServerTestCluster --resource-group catalyst-server-test
# To seed the database with test data when there is an existing tenant
See the comments in bootstrapDb.ts and loadFixtures.ts  
You'll have to comment and uncomment some code (and tenant must set correctly in loadFixtures.ts)
then run NODE_ENV=production npm run bootstrap-db