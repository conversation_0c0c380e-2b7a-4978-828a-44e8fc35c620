apiVersion: apps/v1
kind: Deployment
metadata:
  name: catalyst-api-server
spec:
  replicas: 3 
  selector:
    matchLabels:
      app: catalyst-api-server
  strategy: {}
  template:
    metadata:
      labels:
        app: catalyst-api-server
    spec:
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: catalyst-api-server
        image: catalystinnovation.azurecr.us/catalyst-api-server:1.25.7
        envFrom:
        - secretRef:
            name: test-secrets
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: api-server
        resources: {}
      restartPolicy: Always
status: {}

---

apiVersion: v1
kind: Service
metadata:
  name: catalyst-api-server
spec:
  ports:
  - port: 8080
  selector:
    app: catalyst-api-server