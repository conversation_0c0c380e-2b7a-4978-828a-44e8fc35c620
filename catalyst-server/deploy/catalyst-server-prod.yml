apiVersion: apps/v1
kind: Deployment
metadata:
  name: catalyst-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: catalyst-server
  strategy: {}
  template:
    metadata:
      labels:
        app: catalyst-server
    spec:
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: catalyst-server
        image: catalystinnovation.azurecr.us/catalyst-server:1.25.7
        envFrom:
        - secretRef:
            name: prod-secrets
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: catalyst-server
        resources: {}
      restartPolicy: Always
status: {}

---

apiVersion: v1
kind: Service
metadata:
  name: catalyst-server
spec:
  ports:
  - port: 8080
  selector:
    app: catalyst-server