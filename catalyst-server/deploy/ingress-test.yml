apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: catalyst-server-ingress
  annotations:
    kubernetes.io/ingress.class: azure/application-gateway
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
    - secretName: catalyst-server-tls-secret
  rules:
  - host: "api.soldierinnovation.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: catalyst-api-server
            port:
              number: 8080
  - host: "monumentsmen.soldierinnovation.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: catalyst-server-staging
            port:
              number: 8080
  - host: "staging.soldierinnovation.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: catalyst-server-staging
            port:
              number: 8080
  - host: "staging-home.soldierinnovation.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: catalyst-server-staging
            port:
              number: 8080
  - host: "api-staging.soldierinnovation.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: catalyst-api-server-staging
            port:
              number: 8080
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: catalyst-server
            port:
              number: 8080
