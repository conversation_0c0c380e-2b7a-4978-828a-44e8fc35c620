
IMPORTANT: make sure the .env-<env> matches the format/values of the version you are going to deploy!

If the package needs to be built and published:

* Increment the catalyst-server package version in package.json
* Run ./bin/inno-deploy.sh <environment>  (where environment is test or production)
* Tag with version
  > git tag catalyst-server-1.0.x
* Add and commit all
  > git add . git commit -m "chore: inc'd release"

If the package has been built and published and only needs to be deployed to an environment:
* Make sure the catalyst-server yml file is updated with the correct version
* Run ./bin/inno-redeploy-image.sh <environment>  (where environment is test or production)
