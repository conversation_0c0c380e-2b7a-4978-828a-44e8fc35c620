Also see 'HowToDeploy' document

To use these scripts the prerequisites are:

Install:
1) **Azure CLI**
2) **docker**
3) **kubectl**
4) **npm (and yarn for the client)**

Run:   
1) You need to be logged into Azure CLI via **az login**
2) You'll need to authorize your local kubectl to access the Azure k8s cluster  
   > az aks get-credentials --resource-group catalyst-server --name catalystServerCluster
   > az aks get-credentials --resource-group catalyst-server-test --name catalystServerTestCluster