version: "3"
services:
  postgres:
    container_name: catalyst-postgres 
    image: postgres:latest
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql
    environment:
      - POSTGRES_USER=dev
      - POSTGRES_PASSWORD=dev
      - POSTGRES_DB=catalyst-db
  
  catalyst-server:
    container_name: catalyst-server
    image: catalyst-server:latest
    ports:
      - "8080:8080"
    depends_on:
      - "postgres"


volumes:
  db_data: ~
