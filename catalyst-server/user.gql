# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

type Query {
  """Get the Tenant"""
  getTenant(handleOrAlias: String, id: String): Tenant

  """Get the TenantInfo"""
  getTenantInfo(handleOrAlias: String): TenantInfo

  """Get a page of matching Tenants"""
  queryTenants(searchSortInput: SearchSortInput, pagingInput: PagingInput): TenantPage!

  """Returns matching Tenants and Tenant Aliases"""
  findTenantInfoByName(name: String!): [TenantInfo!]!

  """Get the current User account"""
  getCurrentUser: User

  """Get the current User's Submissions"""
  getSubmissions: [Submission!]!

  """Get the Submission (if owner)"""
  getSubmission(id: String!): Submission
}

type Tenant {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  handle: String!
  label: String
  meta: TenantMeta
}

"""
The javascript `Date` as string. Type represents date and time as the ISO Date string.
"""
scalar DateTime

type TenantMeta {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  config: JSONObject
  theme: JSONObject
  content: JSONObject
  filterOtherPrivateOpportunities: Boolean!
  serverConfiguration: JSONObject
}

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type TenantInfo {
  tenantId: String!
  name: String!
  handle: String!
  label: String
  meta: TenantMeta
  serverVersion: String!
}

type TenantPage {
  results: [Tenant!]!
  pageInfo: PageInfo!
}

type PageInfo {
  hasNext: Boolean!
  hasPrevious: Boolean!
  lastCursor: String!
  lastPageSize: Int!
  retrievedCount: Int!
  totalCount: Int!
}

input SearchSortInput {
  searchFields: [SearchField!]
  jsonSearchGroups: [JSONObject!]
  sortFields: [SortField!]
}

input SearchField {
  fieldNames: [String!]!
  operator: SearchOperator = MATCH
  searchValue: AnyScalar
}

"""The search operation to apply to a field value"""
enum SearchOperator {
  EQ
  NE
  MATCH
  GT
  LT
  GTE
  LTE
  IN
  NIN
}

"""
Any scalar value (int, string, boolean, <date iso string>, or primitive array)
"""
scalar AnyScalar

input SortField {
  fieldName: String!
  ascending: Boolean = true
}

"""Controls list paging"""
input PagingInput {
  """The number of rows to request, per page.  Defaults to 100. Max 500"""
  pageSize: Int

  """How many results to skip or the 'zero-based' starting index"""
  cursor: String
}

type User {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  emailAddress: String!
  status: VerifiedStatus!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  options: UserOptions!
  appMeta: ApplicationMeta
  submissions: [Submission!]!
  curationEvent: [CurationEvent!]!
  roles: [Role!]!
  privilegeGroups: [PrivilegeGroup!]!
}

"""User or opportunity's verification status"""
enum VerifiedStatus {
  UNVERIFIED
  VERIFIED
}

type UserOptions {
  lastUsedServerVersion: String
  cookieAcceptance: Boolean
  submissionEmailOptOut: Boolean
  optOutOtherContact: Boolean
  optOutTeamUpdates: Boolean
  optOutAll: Boolean
}

type ApplicationMeta {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  curationMeta: JSONObject
}

type Submission {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  title: String!
  statement: String!
  context: String!
  benefits: String
  solutionConcepts: String
  campaign: String
  function: String
  user: User
  opportunities: [Opportunity!]!
}

type Opportunity {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  lastCurated: DateTime
  title: String!
  statement: String!
  context: String!
  status: OpportunityStatus!
  visibility: OpportunityVisibility!
  isTiCLOE: Boolean!
  org1: String
  org2: String
  org3: String
  org4: String
  benefits: String
  solutionConcepts: String
  additionalNotes: String
  campaign: String
  campaignNotes: String
  function: String
  statusNotes: String
  priority: Int
  priorityNotes: String
  solutionPathway: String
  solutionPathwayDetails: String
  permission: String
  attachmentNotes: String
  initiatives: String
  endorsements: String
  armyModernizationPriority: String
  echelonApplicability: String
  transitionInContactLineOfEffort: String
  operationalRules: String
  capabilityArea: String
  feasibilitySummary: String
  materielSolutionType: String
  DOTMLPFPPChange: [String!]
  tenant: Tenant
  user: User
  owners: [OpportunityOwner!]!
  attachments: [Attachment!]!
  existingSolutions: [ExistingSolution!]!
  links: [Link!]!
  submissions: [Submission!]!
  projects: [Project!]!
  opportunities: [Opportunity!]!
  categories: [Category!]!
  stakeholders: [Stakeholder!]!
  ownedOpportunities: [RelatedOpportunity!]!
  owningOpportunities: [RelatedOpportunity!]!
  relatedOpportunityCount: Int
  linkedOpportunityCount: Int
  childOpportunityCount: Int
  parentOpportunityCount: Int
}

"""Status of the Opportunity"""
enum OpportunityStatus {
  PENDING
  APPROVED
  ARCHIVED
  DELETED
}

"""Sets the visibility status of an opportunity"""
enum OpportunityVisibility {
  PRIVATE
  ALL
}

type OpportunityOwner {
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime
  owner: Owner!
  opportunity: Opportunity!
  addedAt: DateTime!
  madePreviousAt: DateTime
  status: OpportunityOwnerStatus!
  removedAt: DateTime
  isRemoved: Boolean!
}

type Owner {
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime
  emailAddress: String!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  organizationRole: String
}

"""The status of the opportunity owner"""
enum OpportunityOwnerStatus {
  CURRENT
  PREVIOUS
  INITIAL
}

type Attachment {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  displayName: String
  notes: String
  encoding: String
  mimetype: String
  createdBy: User
}

type ExistingSolution {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  source: String!
  title: String
  organization: String
  needsModification: Boolean!
  opportunity: Opportunity!
}

type Link {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  url: String!
  notes: String
  createdBy: User
}

type Project {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  lastCurated: DateTime
  title: String!
  status: ProjectStatus!
  summary: String
  background: String
  startDate: DateTime
  endDate: DateTime
  goals: String
  type: String
  otherType: String
  statusNotes: String
  tenant: Tenant
  creator: User
  attachments: [Attachment!]!
  opportunities: [Opportunity!]!
  categories: [Category!]!
  projectStakeholders: [ProjectStakeholder!]!
}

"""The status of the project"""
enum ProjectStatus {
  PENDING
  ACTIVE
  ARCHIVED
  COMPLETED
  DELETED
}

type Category {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  opportunities: [Opportunity!]!
  projects: [Project!]!
}

type ProjectStakeholder {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  project: Project!
  stakeholder: Stakeholder!
  type: ProjectStakeholderType!
}

type Stakeholder {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String
  org: String
  opportunities: [Opportunity!]!
}

"""The type of project stakeholder"""
enum ProjectStakeholderType {
  DIVISION
  PERFORMER
  TRANSITION
}

type RelatedOpportunity {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  source: Opportunity!
  target: Opportunity!
  type: RelatedOpportunityType!
}

"""The type of related opportunity"""
enum RelatedOpportunityType {
  LINKED
  CHILD
}

type CurationEvent {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  user: User
  type: CurationEventType!
  entityType: EntityType!
  entityId: String
  data: CurationEventData
}

"""The type of curation event"""
enum CurationEventType {
  CREATE
  UPDATE
  DELETE
}

"""The type of entity targeted"""
enum EntityType {
  OPPORTUNITY
  USER
  PROJECT
  LINK
  ATTACHMENT
}

type CurationEventData {
  fields: [String!]
}

type Role {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: RoleNames!
}

"""User role"""
enum RoleNames {
  CURATOR
  ADMIN
  ANALYST
}

type PrivilegeGroup {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  privileges: [Privilege!]
  users: [User!]
}

type Privilege {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  resourceId: String
  resourceType: ResourceType!
}

"""The type of resource targeted"""
enum ResourceType {
  TENANT
}

type Mutation {
  """Create a new Tenant"""
  createTenant(adminPass: String!, input: CreateTenantInput!): Tenant!

  """Update an existing Tenant"""
  updateTenant(id: String!, input: UpdateTenantInput!): Tenant!

  """Delete an existing Tenant"""
  deleteTenant(id: String!): Boolean!

  """(Unauthenticated) Register a new user with a password"""
  register(input: RegisterUserInput!): AuthResponse!

  """Authenticate a User and receive a token"""
  login(tenantHandle: String!, password: String!, userName: String!): AuthResponse!

  """Authenticate a User and receive a token"""
  renew: AuthResponse!

  """Update the current User account"""
  updateCurrentUser(links: UpdateCurrentUserLinks, input: UpdateCurrentUserInput!): User!

  """Delete the current User account"""
  deleteCurrentUser: Boolean!

  """(Unauthenticated) Submit an unverified Submission"""
  newSubmission(links: NewSubmissionLinks!, input: CreateSubmissionInput!): Submission!

  """Submit a verfied Submission from a logged-in User"""
  createSubmission(input: CreateSubmissionInput!): Submission!

  """Update the Submission (if owner)"""
  updateSubmission(id: String!, input: UpdateSubmissionInput!): Submission!

  """Delete the Submission (if owner)"""
  deleteSubmission(id: String!): Boolean!

  """(Unauthenicated) Submit an unverified user"""
  submitUser(input: SubmitUserInput!): User!
}

input CreateTenantInput {
  name: String!
  handle: String!
  label: String
  config: JSONObject
  theme: JSONObject
  content: JSONObject
}

input UpdateTenantInput {
  name: String
  handle: String
  label: String
  config: JSONObject
  theme: JSONObject
  content: JSONObject
}

type AuthResponse {
  user: User!
  token: String!
  expiresAt: DateTime!
}

input RegisterUserInput {
  emailAddress: String!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  options: UserOptionsInput
  tenantHandle: String!
  password: String!
}

input UserOptionsInput {
  lastUsedServerVersion: String
  cookieAcceptance: Boolean
  submissionEmailOptOut: Boolean
  optOutOtherContact: Boolean
  optOutTeamUpdates: Boolean
  optOutAll: Boolean
}

input UpdateCurrentUserLinks {
  appMetaId: String
}

input UpdateCurrentUserInput {
  password: String
  firstName: String
  lastName: String
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  options: UserOptionsInput
}

input NewSubmissionLinks {
  userId: String!
}

input CreateSubmissionInput {
  title: String!
  statement: String!
  context: String!
  benefits: String
  solutionConcepts: String
  campaign: String
  function: String
}

input UpdateSubmissionInput {
  title: String
  statement: String
  context: String
  benefits: String
  solutionConcepts: String
  campaign: String
  function: String
}

input SubmitUserInput {
  emailAddress: String!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  options: UserOptionsInput
  tenantHandle: String!
}
