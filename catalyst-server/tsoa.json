{"entryFile": "src/api/index.ts", "noImplicitAdditionalProperties": "throw-on-extras", "controllerPathGlobs": ["src/api/controllers/AuthController.ts", "src/api/controllers/OpportunityController.ts", "src/api/controllers/AttachmentController.ts", "src/api/controllers/**/*Controller.ts"], "spec": {"outputDirectory": "src/api", "specVersion": 3, "securityDefinitions": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "routes": {"routesDir": "src/api/routes", "authenticationModule": "src/api/authentication.ts"}, "compilerOptions": {"baseUrl": ".", "paths": {"*": ["./src/*", "./tests/*"]}}}