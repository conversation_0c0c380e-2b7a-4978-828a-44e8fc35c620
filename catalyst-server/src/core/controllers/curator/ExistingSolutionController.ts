import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  CreateExistingSolutionInput,
  UpdateExistingSolutionInput,
} from 'core/contracts/input/curator/ExistingSolutionInput';
import { ExistingSolution } from 'core/entities/ExistingSolution';
import { Opportunity } from 'core/entities/Opportunity';

export class ExistingSolutionController {
  public static async createExistingSolution(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    input: CreateExistingSolutionInput;
    opportunityId: string;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<ExistingSolution> {
    const { em, input, opportunityId, tenantId, relationPaths = [] } = params;

    // Verify the opportunity exists and belongs to the tenant
    const opportunity = await em.getRepository(Opportunity).findOne({
      id: opportunityId,
      tenant: { id: tenantId },
    });

    if (!opportunity) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    const existingSolution = ExistingSolution.newExistingSolution({
      ...input,
      opportunity,
    });

    await em.persist(existingSolution).flush();

    if (relationPaths.length > 0) {
      await em.populate(existingSolution, relationPaths as never);
    }

    return existingSolution;
  }

  public static async getExistingSolution(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<ExistingSolution | null> {
    const { em, id, tenantId, relationPaths = [] } = params;

    return em.getRepository(ExistingSolution).findOne(
      {
        id,
        opportunity: { tenant: { id: tenantId } },
      },
      { populate: relationPaths as never },
    );
  }

  public static async getExistingSolutionsByOpportunity(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    opportunityId: string;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<ExistingSolution[]> {
    const { em, opportunityId, tenantId, relationPaths = [] } = params;

    return em.getRepository(ExistingSolution).find(
      {
        opportunity: {
          id: opportunityId,
          tenant: { id: tenantId },
        },
      },
      { populate: relationPaths as never },
    );
  }

  public static async updateExistingSolution(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    input: UpdateExistingSolutionInput;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<ExistingSolution> {
    const { em, id, input, tenantId, relationPaths = [] } = params;

    const existingSolution = await em.getRepository(ExistingSolution).findOne(
      {
        id,
        opportunity: { tenant: { id: tenantId } },
      },
      { populate: relationPaths as never },
    );

    if (!existingSolution) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    existingSolution.modify(input);
    await em.persist(existingSolution).flush();

    if (relationPaths.length > 0) {
      await em.populate(existingSolution, relationPaths as never);
    }

    return existingSolution;
  }

  public static async deleteExistingSolution(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    tenantId: string;
  }): Promise<boolean> {
    const { em, id, tenantId } = params;

    const existingSolution = await em.getRepository(ExistingSolution).findOne({
      id,
      opportunity: { tenant: { id: tenantId } },
    });

    if (!existingSolution) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    await em.remove(existingSolution).flush();
    return true;
  }
}
