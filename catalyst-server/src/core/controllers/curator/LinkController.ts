import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { AddLinkInput, UpdateLinkInput } from 'core/contracts/input/curator/LinkInput';
import { AppContext } from 'core/core';
import { Link } from 'core/entities/Link';
import { Opportunity } from 'core/entities/Opportunity';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import { Id } from 'core/utils/Id';
import { CurationEventController } from './CurationEventController';

export class LinkController {
  public static async addLink(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    input: AddLinkInput;
    tenantId: string;
    userId: string;
    opportunityId?: string;
    projectId?: string;
  }): Promise<Link> {
    const { em, input, tenantId, userId, opportunityId, projectId } = params;
    const id = Id.newId();
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    const link = Link.newLink({
      id,
      name: input.name,
      url: input.url,
      notes: input.notes,
    });

    // Set the user who created this link
    const user = em.getRepository(User).getReference(userId);
    link.createdBy = user;

    // a link cannot be attached to both an opportunity and a project, but requires one or the other
    if ((!opportunityId && !projectId) || (opportunityId && projectId))
      throw new Error(errorKeys.ARGUMENT_VALIDATION_ERROR);

    if (opportunityId) {
      const opportunity = await em
        .getRepository(Opportunity)
        .findOne({ id: opportunityId, tenant: { id: tenantId } }, { populate: ['links'] });
      if (!opportunity) throw Error(errorKeys.OBJECT_NOT_FOUND);
      opportunity.links.add(link);
      await em.persist(opportunity).flush();
    }

    // Record the user action
    await CurationEventController.updateCurationEvent(em, user, EntityType.LINK, link.id, CurationEventType.CREATE);

    return link;
  }

  public static async updateLink(
    em: EntityManager,
    input: UpdateLinkInput,
    tenantId: string,
    userId: string,
  ): Promise<Link> {
    const link = await em.getRepository(Link).findOne({
      id: input.id,
      opportunity: { tenant: { id: tenantId } },
    });

    if (!link) throw Error(errorKeys.OBJECT_NOT_FOUND);

    // Update only the provided fields
    if (input.name !== undefined) link.name = input.name;
    if (input.url !== undefined) link.url = input.url;
    if (input.notes !== undefined) link.notes = input.notes;

    await em.persist(link).flush();

    // Record the user action
    const user = em.getRepository(User).getReference(userId);
    await CurationEventController.updateCurationEvent(em, user, EntityType.LINK, link.id, CurationEventType.UPDATE);

    return link;
  }

  public static async deleteLink(ctx: AppContext, id: string): Promise<boolean> {
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!userId) throw Error(errorKeys.UNAUTHORIZED);

    const link = await ctx.em.getRepository(Link).findOne({ id });
    if (!link) throw Error(errorKeys.OBJECT_NOT_FOUND);

    const linkId = link.id;
    await ctx.em.remove(link).flush();

    // Record the user action
    const user = ctx.em.getRepository(User).getReference(userId);
    await CurationEventController.updateCurationEvent(
      ctx.em,
      user,
      EntityType.LINK,
      linkId,
      CurationEventType.DELETE,
      new Date(),
    );

    return true;
  }
}
