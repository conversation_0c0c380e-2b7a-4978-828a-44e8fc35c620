import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { AddAttachmentInput, UpdateAttachmentInput } from 'core/contracts/input/curator/AttachmentInput';
import { Location } from 'core/contracts/output/Location';
import { AppContext } from 'core/core';
import { Attachment } from 'core/entities/Attachment';
import { Opportunity } from 'core/entities/Opportunity';
import { Project } from 'core/entities/Project';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import { BlobStorage } from 'core/storage/BlobStorage';
import { Id } from 'core/utils/Id';
import { getTenantFilterForScope } from '../controllers';
import { getScopeForUser } from 'core/auth/authUtils';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { CurationEventController } from './CurationEventController';

export class AttachmentController {
  public static async addAttachment(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    input: AddAttachmentInput;
    tenantId: string;
    userId?: string;
    opportunityId?: string;
    projectId?: string;
    noUpload?: boolean;
    attachmentId?: string;
  }): Promise<Attachment> {
    const { em, input, tenantId, userId, opportunityId, projectId, noUpload, attachmentId } = params;
    const id = attachmentId || Id.newId();
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    const blobName = this.getBlobName(input.filename, id);
    if (!noUpload) await BlobStorage.uploadBlob(input, tenant.handle, blobName);
    const attachment = Attachment.newAttachment({
      id,
      name: input.filename,
      location: blobName,
      mimetype: input.mimetype,
      encoding: input.encoding,
      displayName: input.displayName,
      notes: input.notes,
    });
    attachment.tenant = tenant;

    // Set the user who created this attachment if userId is provided
    let user: User | undefined;
    if (userId) {
      user = em.getRepository(User).getReference(userId);
      attachment.createdBy = user;
    }

    // an attachment cannot be attached to both an opportunity and a project, but requires one or the other
    if ((!opportunityId && !projectId) || (opportunityId && projectId))
      throw new Error(errorKeys.ARGUMENT_VALIDATION_ERROR);

    if (opportunityId) {
      const opportunity = await em
        .getRepository(Opportunity)
        .findOne({ id: opportunityId, tenant: { id: tenantId } }, { populate: ['attachments'] });
      if (!opportunity) throw Error(errorKeys.OBJECT_NOT_FOUND);
      opportunity.attachments.add(attachment);
      await em.persist(opportunity).flush();
    }

    if (projectId) {
      const project = await em
        .getRepository(Project)
        .findOne({ id: projectId, creator: { tenant: { id: tenantId } } }, { populate: ['attachments'] });
      if (!project) throw Error(errorKeys.OBJECT_NOT_FOUND);
      project.attachments.add(attachment);
      await em.persist(project).flush();
    }

    // Record the user action if userId is provided
    if (userId && user) {
      await CurationEventController.updateCurationEvent(
        em,
        user,
        EntityType.ATTACHMENT,
        attachment.id,
        CurationEventType.CREATE,
      );
    }

    // uncomment to write to filesystem for testing
    /*
    await new Promise(async (resolve, reject) =>
      input
        .createReadStream()
        .pipe(createWriteStream(__dirname + `/../../../images/${input.filename}`))
        .on('finish', () => resolve(true))
        .on('error', (e) => reject(e)),
    );*/
    return attachment;
  }

  public static async getAttachmentLocation(ctx: AppContext, id: string): Promise<Location | null> {
    const scope = await getScopeForUser({ context: ctx, resourceType: ResourceType.TENANT });
    const tenantFilter = await getTenantFilterForScope(ctx, scope);
    const attachment = await ctx.em
      .getRepository(Attachment)
      .findOne({ id, tenant: tenantFilter }, { populate: ['tenant'] });
    if (!attachment) throw Error(errorKeys.OBJECT_NOT_FOUND);
    const url = await BlobStorage.getBlobUrl(attachment.tenant.handle, attachment.location);
    return { location: url };
  }

  public static async updateAttachment(
    em: EntityManager,
    input: UpdateAttachmentInput,
    tenantId: string,
    userId: string,
  ): Promise<Attachment> {
    const attachment = await em.getRepository(Attachment).findOne({
      id: input.id,
      tenant: { id: tenantId },
    });

    if (!attachment) throw Error(errorKeys.OBJECT_NOT_FOUND);

    // Update only the provided fields
    if (input.displayName !== undefined) attachment.displayName = input.displayName;
    if (input.notes !== undefined) attachment.notes = input.notes;

    await em.persist(attachment).flush();

    // Record the user action
    const user = em.getRepository(User).getReference(userId);
    await CurationEventController.updateCurationEvent(
      em,
      user,
      EntityType.ATTACHMENT,
      attachment.id,
      CurationEventType.UPDATE,
    );

    return attachment;
  }

  public static async deleteAttachment(ctx: AppContext, id: string): Promise<boolean> {
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!userId) throw Error(errorKeys.UNAUTHORIZED);

    const tenant = await ctx.em
      .getRepository(Tenant)
      .findOne({ id: ctx.token?.[PayloadKeys.TENANT_KEY] }, { fields: ['id', 'handle'] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const attachment = await ctx.em
      .getRepository(Attachment)
      .findOne({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } });
    if (!attachment) throw Error(errorKeys.OBJECT_NOT_FOUND);

    const attachmentId = attachment.id;

    // find any remaining references
    const numberOfRefs = await ctx.em
      .getRepository(Attachment)
      .count({ location: attachment.location, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } });

    // delete the remote artifact if we are the only ref holder
    if (numberOfRefs < 2) {
      await BlobStorage.deleteBlob(tenant.handle, attachment.location);
    }
    await ctx.em.remove(attachment).flush();

    // Record the user action
    const user = ctx.em.getRepository(User).getReference(userId);
    await CurationEventController.updateCurationEvent(
      ctx.em,
      user,
      EntityType.ATTACHMENT,
      attachmentId,
      CurationEventType.DELETE,
      new Date(),
    );

    return true;
  }

  public static async shallowCopyAttachment(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    attachmentId: string;
    tenantId: string;
  }): Promise<Attachment> {
    const { em, attachmentId, tenantId } = params;

    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    const attachment = await em.getRepository(Attachment).findOne({ id: attachmentId, tenant: { id: tenantId } });
    if (!attachment) throw Error(errorKeys.OBJECT_NOT_FOUND);

    const { name, location, mimetype, encoding } = attachment;
    const newAttachment = Attachment.newAttachment({ name, location, mimetype, encoding });
    newAttachment.tenant = tenant;

    await em.persist(newAttachment).flush();

    return newAttachment;
  }

  private static getBlobName(filename: string, blobName: string) {
    const extension = filename.indexOf('.') > -1 ? filename.split('.').pop() : undefined;
    return `${blobName}${extension ? '.' + extension : ''}`;
  }
}
