import { Connection, EntityManager, FilterQuery, IDatabaseDriver, QueryOrderMap } from '@mikro-orm/core';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { UpdateOperator } from 'core/contracts/enums/UpdateOperator';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, Scope, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { UpdateOpportunityInput, UpdateOpportunityLinks } from 'core/contracts/input/curator/OpportunityInput';
import { OpportunityPage } from 'core/contracts/output/Page';
import { AppContext } from 'core/core';
import { Category } from 'core/entities/Category';
import { Opportunity } from 'core/entities/Opportunity';
import { RelatedOpportunity } from 'core/entities/RelatedOpportunity';
import { Stakeholder } from 'core/entities/Stakeholder';
import { User } from 'core/entities/User';
import {
  combineSortFields,
  getEmptyPageInfo,
  getJsonSearchGroupFilter,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
  handleUpdateLinks,
} from 'core/storage/queryUtils';
import { getTenantFilterForScope, getTenantOpportunityFilterForScope, isEmptyScope } from '../controllers';
import { CurationEventController } from './CurationEventController';
import { getScopeForUser, verifyScope } from 'core/auth/authUtils';
import { ResourceType } from 'core/contracts/enums/ResourceType';

export class OpportunityController {
  // get page of opportunities
  static async getOpportunityPage(
    em: EntityManager<IDatabaseDriver<Connection>>,
    filter: FilterQuery<Opportunity>,
    relationPaths: string[],
    sortFilter: QueryOrderMap<Opportunity>,
    limit: number,
    offset: number,
  ): Promise<OpportunityPage> {
    const [results, totalCount] = await em
      .getRepository(Opportunity)
      .findAndCount(filter, { populate: relationPaths as never, orderBy: sortFilter, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  static async queryOpportunities({
    ctx,
    pagingInput,
    searchSortInput,
    relationPaths = [],
    scope,
  }: {
    ctx: AppContext;
    pagingInput?: PagingInput;
    searchSortInput?: SearchSortInput;
    relationPaths?: string[];
    scope?: Scope;
  }): Promise<OpportunityPage> {
    //const start = Date.now();
    if (isEmptyScope(scope)) return { results: [], pageInfo: getEmptyPageInfo() }; // empty scope returns empty page
    if (scope && !(await verifyScope({ scope, context: ctx }))) throw new Error(errorKeys.UNAUTHORIZED);
    const { limit, offset } = getQueryBounds(pagingInput);
    const opportunityVisiblityFilter = await getTenantOpportunityFilterForScope(ctx, scope);
    const filter: FilterQuery<Opportunity> = {
      $and: [
        opportunityVisiblityFilter,
        ...getSearchFilter<Opportunity>(searchSortInput?.searchFields),
        ...getJsonSearchGroupFilter<Opportunity>(searchSortInput?.jsonSearchGroups),
      ],
    };
    const sortFilter = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, [
        { fieldName: 'lastCurated', ascending: false },
        { fieldName: 'id', ascending: true },
      ]),
    );
    const result = await OpportunityController.getOpportunityPage(
      ctx.em,
      filter,
      relationPaths,
      sortFilter,
      limit,
      offset,
    );
    //console.log(`perf:DB:queryOpportunities took: ${Date.now() - start}ms`);
    return result;
  }

  static async getOpportunity({
    id,
    ctx,
    relationPaths = [],
  }: {
    id: string;
    ctx: AppContext;
    relationPaths: string[];
  }): Promise<Opportunity | null> {
    const scope = await getScopeForUser({ context: ctx, resourceType: ResourceType.TENANT });
    const opportunityVisiblityFilter = await getTenantOpportunityFilterForScope(ctx, scope);
    // We include this filter because we always want to be able to get opportunities the primary tenant owns
    const tenantFilter = await getTenantFilterForScope(ctx);
    const filter = {
      $or: [{ tenant: tenantFilter }, opportunityVisiblityFilter],
    };
    // exclude curationInfo and include derived fields
    const result = await ctx.em
      .getRepository(Opportunity)
      .findOne({ id, ...filter }, { populate: relationPaths as never[] });
    // console.log(`perf:DB:getOpportunity took ${Date.now() - start}ms`);
    return result;
  }

  /*
    @TODO add ability to modify 'user' relationship
  */
  static async updateOpportunity(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    tenantId: string,
    userId: string,
    input: UpdateOpportunityInput,
    relationPaths: string[],
    categoryPaths: string[],
    stakeholderPaths: string[],
    opportunityPaths: string[],
    links?: UpdateOpportunityLinks,
  ): Promise<Opportunity> {
    const additionalPaths = [];
    if (links?.relatedOpportunities?.length) additionalPaths.push('ownedOpportunities');
    if (links?.relatedOpportunities?.length) additionalPaths.push('owningOpportunities');
    if (links?.opportunities?.length) additionalPaths.push('opportunities');
    if (links?.categories?.length) additionalPaths.push('categories');

    const lastCurated = new Date();
    const opportunity = await em
      .getRepository(Opportunity)
      .findOne({ id, tenant: { id: tenantId } }, { populate: [...relationPaths, ...additionalPaths] as never[] });
    if (!opportunity) throw new Error(errorKeys.OBJECT_NOT_FOUND);
    opportunity.modify({ ...input, lastCurated });
    let updatedOpportunity = await handleUpdateLinks<Opportunity, Category>(
      em,
      opportunity,
      'categories',
      categoryPaths,
      Category,
      links?.categories,
    );
    updatedOpportunity = await handleUpdateLinks<Opportunity, Stakeholder>(
      em,
      opportunity,
      'stakeholders',
      stakeholderPaths,
      Stakeholder,
      links?.stakeholders,
    );
    updatedOpportunity = await handleUpdateLinks<Opportunity, Opportunity>(
      em,
      opportunity,
      'opportunities',
      opportunityPaths,
      Opportunity,
      links?.opportunities,
      true,
    );

    if (links?.relatedOpportunities) {
      await Promise.all(
        links.relatedOpportunities.map(async (updateLink) => {
          const { items, operator } = updateLink;
          const existingingRelatedOpportunities = updatedOpportunity.ownedOpportunities.getItems();
          const targetOpportunities = await em.getRepository(Opportunity).find(items.map((item) => item.id));
          if (operator === UpdateOperator.ADD) {
            items.forEach((item) => {
              if (
                !existingingRelatedOpportunities.find(
                  (relatedOpportunity) =>
                    relatedOpportunity.target.id === item.id && relatedOpportunity.type === item.type,
                )
              ) {
                const targetOpportunity = targetOpportunities.find((opportunity) => item.id === opportunity.id);
                if (!targetOpportunity) throw Error(errorKeys.OBJECT_NOT_FOUND);
                const relatedOpportunity = RelatedOpportunity.newRelatedOpportunity({
                  type: item.type,
                  orderBy: updatedOpportunity.ownedOpportunities.length,
                });
                relatedOpportunity.target = targetOpportunity;
                targetOpportunity.owningOpportunities.add(relatedOpportunity);
                updatedOpportunity.ownedOpportunities.add(relatedOpportunity);
              }
            });
          } else if (operator === UpdateOperator.RM) {
            items.forEach((item) => {
              const targetOpportunity = targetOpportunities.find((opportunity) => item.id === opportunity.id);
              if (!targetOpportunity) throw Error(errorKeys.OBJECT_NOT_FOUND);
              const existingItem = existingingRelatedOpportunities.find(
                (relatedOpportunity) =>
                  relatedOpportunity.target.id === targetOpportunity.id && relatedOpportunity.type === item.type,
              );
              if (existingItem) {
                updatedOpportunity.ownedOpportunities.remove(existingItem);
              }
            });
          } else if (operator === UpdateOperator.SET) {
            const realtedOpportunitySet: RelatedOpportunity[] = [];
            items.forEach((item, orderBy) => {
              const targetOpportunity = targetOpportunities.find((opportunity) => item.id === opportunity.id);
              if (!targetOpportunity) throw Error(errorKeys.OBJECT_NOT_FOUND);
              const newRelatedOpportunity = RelatedOpportunity.newRelatedOpportunity({
                type: item.type,
                orderBy,
              });
              newRelatedOpportunity.target = targetOpportunity;
              targetOpportunity.owningOpportunities.add(newRelatedOpportunity);
              realtedOpportunitySet.push(newRelatedOpportunity);
            });
            updatedOpportunity.ownedOpportunities.set(realtedOpportunitySet);
          }
        }),
      );
    }

    await em.persist(updatedOpportunity).flush();
    await em.populate(updatedOpportunity, [...relationPaths, ...additionalPaths] as never[]);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const user = em.getRepository(User).getReference(userId);
    await CurationEventController.updateCurationEvent(
      em,
      user,
      EntityType.OPPORTUNITY,
      opportunity.id,
      CurationEventType.UPDATE,
      lastCurated,
    );
    return updatedOpportunity;
  }
}
