import { Collection, Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { AppContext } from 'core/core';
import { checkRoles } from 'core/auth/authUtils';
import { ProjectStakeholderType } from 'core/contracts/enums/ProjectStakeholderType';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { SearchField, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { Category } from 'core/entities/Category';
import { Project } from 'core/entities/Project';
import { ProjectStakeholder } from 'core/entities/ProjectStakeholder';
import { User } from 'core/entities/User';
import { Excel } from 'io/excel';
import { combineSortFields, defaultSecondarySortFields, getSearchFilter, getSortFilter } from 'core/storage/queryUtils';
import { DownloadController, FieldSpec } from './DownloadController';
import { ProjectController } from './ProjectController';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';

export class ProjectDownloadController {
  // create the projects delimited file
  static async downloadProjects(ctx: AppContext, id?: string): Promise<void> {
    if (!checkRoles(ctx.token, [RoleNames.CURATOR])) throw new Error(errorKeys.UNAUTHORIZED);
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    if (id) return this.writeProject(ctx.em, ctx.token![PayloadKeys.TENANT_KEY], ctx.res, id);
    let searchSortInput: SearchSortInput = ctx.req.body['searchSortInput'];
    if (!searchSortInput) {
      searchSortInput = {
        searchFields: [
          {
            fieldNames: ['status'],
            operator: SearchOperator.NE,
            searchValue: 'Deleted',
          } as SearchField,
        ],
      };
    } else if (searchSortInput.searchFields) {
      searchSortInput.searchFields?.push({
        fieldNames: ['status'],
        operator: SearchOperator.NE,
        searchValue: 'Deleted',
      } as SearchField);
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this.writeProjects(ctx.em, ctx.token![PayloadKeys.TENANT_KEY], ctx.res, searchSortInput);
  }
  static async writeProject(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    outStream: NodeJS.WritableStream,
    id: string,
  ): Promise<void> {
    try {
      const fieldSpecs = this.getProjectFieldSpecs();
      const _fields = Object.keys(fieldSpecs);
      const excel = new Excel(outStream, DownloadController.getHeaders(fieldSpecs, _fields));
      const project = await em
        .getRepository(Project)
        .findOne(
          { id, creator: { tenant: { id: tenantId } } },
          { populate: ['creator', 'categories', 'projectStakeholders.stakeholder'] },
        );
      const rows = DownloadController.mapRows([project], fieldSpecs, _fields);
      excel.writeItems(rows);
      await excel.close();
    } catch (error) {
      console.error('📌 Failed write Project file', error);
      throw new Error(errorKeys.FAILED_TO_GENERATE_FILE);
    }
  }

  // create the projects delimited file
  static async writeProjects(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    outStream: NodeJS.WritableStream,
    searchSortInput?: SearchSortInput,
  ): Promise<void> {
    try {
      const fieldSpecs = this.getProjectFieldSpecs();
      const _fields = Object.keys(fieldSpecs);
      const excel = new Excel(outStream, DownloadController.getHeaders(fieldSpecs, _fields));

      // create the query
      const limit = 100;
      let offset = 0;
      const filter = {
        $and: [{ creator: { tenant: { id: tenantId } } }, ...getSearchFilter<Project>(searchSortInput?.searchFields)],
      };
      const sortFilter = getSortFilter(combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields()));
      let page = null;

      //do the query and write the rows
      do {
        page = await ProjectController.getProjectPage(
          em,
          filter,
          ['creator', 'categories', 'projectStakeholders.stakeholder'],
          sortFilter,
          limit,
          offset,
        );
        const rows = DownloadController.mapRows(page.results, fieldSpecs, _fields);
        excel.writeItems(rows);
        offset = offset + limit;
      } while (page.pageInfo.hasNext);

      await excel.close();
    } catch (error) {
      console.error('📌 Failed write Projects file', error);
      throw new Error(errorKeys.FAILED_TO_GENERATE_FILE);
    }
  }

  private static getProjectFieldSpecs(): Record<string, FieldSpec> {
    return {
      id: { fieldName: 'id', header: 'ID', width: 3 },
      createdAt: { fieldName: 'createdAt', header: 'Created Date' },
      title: { fieldName: 'title', header: 'Project Title' },
      summary: { fieldName: 'summary', header: 'Project Summary' },
      status: { fieldName: 'status', header: 'Project Status' },
      background: { fieldName: 'background', header: 'Project Background' },
      startDate: { fieldName: 'startDate', header: 'Start Date' },
      endDate: { fieldName: 'endDate', header: 'End Date' },
      goals: { fieldName: 'goals', header: 'Project Goals' },
      type: { fieldName: 'type', header: 'Project Type' },
      otherType: { fieldName: 'otherType', header: 'Other Type' },
      statusNotes: { fieldName: 'statusNotes', header: 'Status Notes' },
      creator: {
        fieldName: 'creator',
        header: 'Project Creator',
        transform: (value) => `${(value as User).firstName} ${(value as User).lastName}`,
      },
      categories: {
        fieldName: 'categories',
        header: 'Categories',
        transform: (value) => {
          const categories = (value as Collection<Category>).toArray();
          return categories.reduce(
            (accum, category, index) => `${accum}${category.name || ''}${index !== categories.length - 1 ? ', ' : ''}`,
            '',
          );
        },
      },
      divisionStakeHolders: {
        fieldName: 'projectStakeholders',
        header: 'Division Stakeholders',
        transform: (value) => {
          const divisionStakeholders = (value as Collection<ProjectStakeholder>)
            .getItems()
            .filter((projectStakeholder) => projectStakeholder.type === ProjectStakeholderType.DIVISION);
          return divisionStakeholders.reduce((accum, projectStakeholder, index) => {
            const { name = '', org = '' } = projectStakeholder.stakeholder;
            return `${accum}${name || ''}${name && org ? ' - ' : ''}${org || ''}${
              index !== divisionStakeholders.length - 1 ? ', ' : ''
            }`;
          }, '');
        },
      },
      performerStakeholders: {
        fieldName: 'projectStakeholders',
        header: 'Performer Stakeholders',
        transform: (value) => {
          const performerStakeholder = (value as Collection<ProjectStakeholder>)
            .getItems()
            .filter((projectStakeholder) => projectStakeholder.type === ProjectStakeholderType.PERFORMER);
          return performerStakeholder.reduce((accum, projectStakeholder, index) => {
            const { name = '', org = '' } = projectStakeholder.stakeholder;
            return `${accum}${name || ''}${name && org ? ' - ' : ''}${org || ''}${
              index !== performerStakeholder.length - 1 ? ', ' : ''
            }`;
          }, '');
        },
      },
      transitionStakeholder: {
        fieldName: 'projectStakeholders',
        header: 'Project Stakeholders',
        transform: (value) => {
          const transitionStakeholder = (value as Collection<ProjectStakeholder>)
            .getItems()
            .filter((projectStakeholder) => projectStakeholder.type === ProjectStakeholderType.TRANSITION);
          return transitionStakeholder.reduce((accum, projectStakeholder, index) => {
            const { name, org } = projectStakeholder.stakeholder;
            return `${accum}${name || ''}${name && org ? ' - ' : ''}${org || ''}${
              index !== transitionStakeholder.length - 1 ? ', ' : ''
            }`;
          }, '');
        },
      },
    };
  }
}
