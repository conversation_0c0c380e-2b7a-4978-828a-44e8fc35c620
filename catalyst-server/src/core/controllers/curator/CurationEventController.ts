import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { CurationEvent } from 'core/entities/CurationEvent';
import { CurationEventData } from 'core/entities/CurationEventData';
import { User } from 'core/entities/User';

export class CurationEventController {
  /* 
    Note that this method currently maintains ONLY ONE EVENT PER USER for an opportunity
    In the future, we may want to log and keep all events.  This would simply require removing the 
    latter condition
   */
  static async getCurationEvent(
    em: EntityManager<IDatabaseDriver<Connection>>,
    user: User,
    entityType: EntityType,
    entityId: string,
    eventType: CurationEventType,
    data: CurationEventData = {},
  ): Promise<CurationEvent> {
    let curationEvent;
    if (eventType === CurationEventType.CREATE) {
      curationEvent = CurationEvent.newCurationEvent({
        type: eventType,
        entityType,
        entityId,
        data,
      });
      curationEvent.user = user;
    } else {
      curationEvent = await em.getRepository(CurationEvent).findOne({ user, entityType: entityType, entityId });
      if (!curationEvent) {
        curationEvent = CurationEvent.newCurationEvent({
          type: eventType,
          entityType,
          entityId,
          data,
        });
        curationEvent.user = user;
      } else {
        curationEvent.type = eventType;
        curationEvent.data = data;
      }
    }
    return curationEvent;
  }

  static async updateCurationEvent(
    em: EntityManager<IDatabaseDriver<Connection>>,
    user: User,
    entityType: EntityType,
    entityId: string,
    eventType: CurationEventType,
    updatedAt?: Date,
    data: CurationEventData = {},
  ): Promise<void> {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const curationEvent = await CurationEventController.getCurationEvent(
      em,
      user,
      entityType,
      entityId,
      eventType,
      data,
    );
    if (updatedAt) curationEvent.updatedAt = updatedAt;
    await em.persist(curationEvent).flush();
  }
}
