import { Connection, EntityManager, FilterQuery, IDatabaseDriver, QueryOrderMap } from '@mikro-orm/core';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { UpdateOperator } from 'core/contracts/enums/UpdateOperator';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  CreateProjectFromOpportunityInput,
  CreateProjectInput,
  UpdateProjectInput,
  UpdateProjectLinks,
} from 'core/contracts/input/curator/ProjectInput';
import { Category } from 'core/entities/Category';
import { Opportunity } from 'core/entities/Opportunity';
import { Project } from 'core/entities/Project';
import { User } from 'core/entities/User';
import { getPageInfo, handleUpdateLinks } from 'core/storage/queryUtils';
import { CurationEventController } from './CurationEventController';
import { AttachmentController } from './AttachmentController';
import { ProjectPage } from 'core/contracts/output/Page';
import { Stakeholder } from 'core/entities/Stakeholder';
import { ProjectStakeholder } from 'core/entities/ProjectStakeholder';
import { Tenant } from 'core/entities/Tenant';

export class ProjectController {
  static async createProject(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    creatorId: string,
    input: CreateProjectInput,
    userPaths: string[],
  ): Promise<Project> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const creator = await em
      .getRepository(User)
      .findOneOrFail({ id: creatorId, tenant: { id: tenantId } }, { populate: userPaths as never });
    const project = Project.newProject({ ...input }, tenant);
    project.creator = creator;
    await em.persist(project).flush();
    await CurationEventController.updateCurationEvent(
      em,
      creator,
      EntityType.PROJECT,
      project.id,
      CurationEventType.CREATE,
    );
    return project;
  }

  // get page of projects
  static async getProjectPage(
    em: EntityManager<IDatabaseDriver<Connection>>,
    filter: FilterQuery<Project>,
    relationPaths: string[],
    sortFilter: QueryOrderMap<Project>,
    limit: number,
    offset: number,
  ): Promise<ProjectPage> {
    const [results, totalCount] = await em
      .getRepository(Project)
      .findAndCount(filter, { populate: relationPaths as never, orderBy: sortFilter, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  static async createProjectFromOpportunity(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    tenantId: string;
    creatorId: string;
    input: CreateProjectFromOpportunityInput;
    userPaths: string[];
    opportunityPaths: string[];
    categoriesPaths: string[];
  }): Promise<Project> {
    const { em, input, tenantId, creatorId, userPaths, opportunityPaths, categoriesPaths } = params;
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const creator = await em
      .getRepository(User)
      .findOne({ id: creatorId, tenant: { id: tenantId } }, { populate: userPaths as never });
    if (!creator) throw new Error(errorKeys.UNAUTHORIZED);
    const opportunity = await em
      .getRepository(Opportunity)
      .findOneOrFail(
        { id: input.opportunityId, tenant: { id: tenantId } },
        { populate: ['categories', 'attachments', ...opportunityPaths] as never },
      );
    if (!opportunity) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    const background = input.includeProblemSolution
      ? `${opportunity.statement}\n${opportunity.context}\n${opportunity.benefits}\n${opportunity.solutionConcepts}`
      : undefined;

    const project = Project.newProject({ title: input.title, background }, tenant);
    project.creator = creator;
    project.opportunities.add(opportunity);
    await em.persist(project);

    input.includeCategories &&
      opportunity.categories?.length &&
      (await handleUpdateLinks<Project, Category>(em, project, 'categories', categoriesPaths, Category, [
        { ids: opportunity.categories.getIdentifiers(), operator: UpdateOperator.SET },
      ]));

    if (input.includeAttachments) {
      const attachmentCopies = await Promise.all(
        opportunity.attachments.getItems().map((attachment) => {
          return AttachmentController.shallowCopyAttachment({
            em: em,
            attachmentId: attachment.id,
            tenantId: tenantId,
          });
        }),
      );

      project.attachments.add(attachmentCopies);
    }

    await em.persist(project).flush();
    await em.populate(project, [...userPaths, ...opportunityPaths, ...categoriesPaths] as never[]);

    await CurationEventController.updateCurationEvent(
      em,
      creator,
      EntityType.PROJECT,
      project.id,
      CurationEventType.CREATE,
    );
    return project;
  }

  static async updateProject(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    tenantId: string,
    userId: string,
    input: UpdateProjectInput,
    relationPaths: string[],
    categoryPaths: string[],
    opportunityPaths: string[],
    links?: UpdateProjectLinks,
  ): Promise<Project> {
    // don't fetch it if we don't need it
    const additionalPaths = [];
    if (links?.projectStakeholders?.length) additionalPaths.push('projectStakeholders');
    if (links?.opportunities?.length) additionalPaths.push('opportunities');
    if (links?.categories?.length) additionalPaths.push('categories');
    const project = await em
      .getRepository(Project)
      .findOne(
        { id, creator: { tenant: { id: tenantId } } },
        { populate: [...relationPaths, ...additionalPaths] as never[] },
      );
    if (!project) throw new Error(errorKeys.OBJECT_NOT_FOUND);
    const lastCurated = new Date();
    project.modify({ ...input, lastCurated });
    await handleUpdateLinks<Project, Category>(em, project, 'categories', categoryPaths, Category, links?.categories);
    await handleUpdateLinks<Project, Opportunity>(
      em,
      project,
      'opportunities',
      opportunityPaths,
      Opportunity,
      links?.opportunities,
    );

    // manage the ProjectStakeholders association class
    // consider adding an 'addBefore(id) method for ordering large lists

    if (links?.projectStakeholders) {
      await Promise.all(
        links.projectStakeholders.map(async (updateLink) => {
          const { items, operator } = updateLink;
          const existingingProjectStakeholders = project.projectStakeholders.getItems();
          const targetStakeholders = await em.getRepository(Stakeholder).find(items.map((item) => item.id));
          if (operator === UpdateOperator.ADD) {
            items.forEach((item) => {
              if (
                !existingingProjectStakeholders.find(
                  (projectStakeholder) =>
                    projectStakeholder.stakeholder.id === item.id && projectStakeholder.type === item.type,
                )
              ) {
                const targetStakeholder = targetStakeholders.find((stakeholder) => item.id === stakeholder.id);
                if (!targetStakeholder) throw Error(errorKeys.OBJECT_NOT_FOUND);
                const projectStakeholder = ProjectStakeholder.newProjectStakeholder({
                  type: item.type,
                  orderBy: project.projectStakeholders.length,
                });
                projectStakeholder.stakeholder = targetStakeholder;
                targetStakeholder.projectStakeholders.add(projectStakeholder);
                project.projectStakeholders.add(projectStakeholder);
              }
            });
          } else if (operator === UpdateOperator.RM) {
            items.forEach((item) => {
              const targetStakeholder = targetStakeholders.find((stakeholder) => item.id === stakeholder.id);
              if (!targetStakeholder) throw Error(errorKeys.OBJECT_NOT_FOUND);
              const existingItem = existingingProjectStakeholders.find(
                (projectStakeholder) =>
                  projectStakeholder.stakeholder.id === targetStakeholder.id && projectStakeholder.type === item.type,
              );
              if (existingItem) {
                // note: orphanRemoval should take care of deleting dettached projectStakeholders here
                project.projectStakeholders.remove(existingItem);
              }
            });
          } else if (operator === UpdateOperator.SET) {
            const projectStakeholderSet: ProjectStakeholder[] = [];
            items.forEach((item, orderBy) => {
              const targetStakeholder = targetStakeholders.find((stakeholder) => item.id === stakeholder.id);
              if (!targetStakeholder) throw Error(errorKeys.OBJECT_NOT_FOUND);
              const newProjectStakeholder = ProjectStakeholder.newProjectStakeholder({
                type: item.type,
                orderBy,
              });
              newProjectStakeholder.stakeholder = targetStakeholder;
              targetStakeholder.projectStakeholders.add(newProjectStakeholder);
              projectStakeholderSet.push(newProjectStakeholder);
            });
            project.projectStakeholders.set(projectStakeholderSet);
          }
        }),
      );
    }

    await em.persist(project).flush();
    await em.populate(project, relationPaths as never[]);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const user = await em.getRepository(User).getReference(userId);
    await CurationEventController.updateCurationEvent(
      em,
      user,
      EntityType.PROJECT,
      project.id,
      CurationEventType.UPDATE,
      lastCurated,
    );
    return project;
  }

  static async deleteProject(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    tenantId: string,
    userId: string,
  ): Promise<boolean> {
    const project = await em
      .getRepository(Project)
      .findOneOrFail({ id, creator: { tenant: { id: tenantId } } }, { populate: ['projectStakeholders'] });
    const projId = project.id;
    await em.removeAndFlush(project);
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const user = em.getRepository(User).getReference(userId);
    await CurationEventController.updateCurationEvent(
      em,
      user,
      EntityType.PROJECT,
      projId,
      CurationEventType.DELETE,
      new Date(),
    );
    return true;
  }
}
