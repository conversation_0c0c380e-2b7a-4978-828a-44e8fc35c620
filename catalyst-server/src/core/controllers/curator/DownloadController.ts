export interface FieldSpec {
  fieldName: string;
  header: string;
  width?: number;
  transform?: (value: unknown, item?: unknown) => unknown;
}

export class DownloadController {
  static mapRows(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    items: any[],
    fieldSpecs: Record<string, FieldSpec>,
    fields: string[],
  ): unknown[][] {
    return items.map((item) => this.mapRow(item, fieldSpecs, fields));
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private static mapRow(item: Record<string, any>, fieldSpecs: Record<string, FieldSpec>, fields: string[]): unknown[] {
    const row: unknown[] = [];
    if (fields) {
      fields.forEach((field, index) => {
        const fieldSpec = fieldSpecs[field];
        if (fieldSpec) {
          const value = fieldSpec.transform
            ? fieldSpec.transform(item[fieldSpec.fieldName], item)
            : item[fieldSpec.fieldName];
          const targetValue = value ?? '';
          row[index] = targetValue;
        } else {
          throw Error(`field ${field} does not exist`);
        }
      });
    }
    return row;
  }

  static getHeaders(fieldSpecs: Record<string, FieldSpec>, fields: string[]): { header: string; width?: number }[] {
    return fields.map((field) => {
      const fieldSpec = fieldSpecs[field];
      if (fieldSpec) {
        return { header: fieldSpec.header, width: fieldSpec.width };
      } else {
        throw Error(`field ${field} does not exist`);
      }
    });
  }
}
