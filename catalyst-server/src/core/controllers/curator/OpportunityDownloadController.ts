import { Collection, Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { AppContext } from 'core/core';
import { checkRoles, verifyScope } from 'core/auth/authUtils';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, Scope, SearchField, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { Category } from 'core/entities/Category';
import { Opportunity } from 'core/entities/Opportunity';
import { Stakeholder } from 'core/entities/Stakeholder';
import { Tenant } from 'core/entities/Tenant';
import { Excel } from 'io/excel';
import { combineSortFields, getJsonSearchGroupFilter, getSearchFilter, getSortFilter } from 'core/storage/queryUtils';
import { DownloadController, FieldSpec } from './DownloadController';
import { OpportunityController } from './OpportunityController';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { getTenantOpportunityFilterForScope, isEmptyScope } from '../controllers';
import { OpportunityOwner } from 'core/entities/OpportunityOwner';
import { OpportunityOwnerStatus } from 'core/contracts/enums/OpportunityOwnerStatus';

export class OpportunityDownloadController {
  // create the opportunities delimited file
  static async downloadOpportunities(ctx: AppContext, id?: string): Promise<void> {
    const tenant = await ctx.em.getRepository(Tenant).findOne({ id: ctx.token?.[PayloadKeys.TENANT_KEY] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    if (!checkRoles(ctx.token, [RoleNames.CURATOR])) throw new Error(errorKeys.UNAUTHORIZED);
    const filterOpportunitiesInput: { searchSortInput: SearchSortInput; pagingInput: PagingInput; scope: Scope } = ctx
      .req.body['filterOpportunitiesInput'] || { searchSortInput: { searchFields: [] } };
    const { scope, searchSortInput } = filterOpportunitiesInput;
    if (isEmptyScope(scope)) throw new Error(errorKeys.ARGUMENT_VALIDATION_ERROR);
    if (scope && !(await verifyScope({ scope, context: ctx }))) throw new Error(errorKeys.UNAUTHORIZED);
    const tenantFilter = await getTenantOpportunityFilterForScope(ctx, scope);
    const fields: string[] | undefined = this.addOrgsIfPresent(ctx.req.body['fields']);
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    if (id) return this.writeOpportunity(ctx.em, ctx.res, id, ctx.token![PayloadKeys.TENANT_KEY], fields);
    searchSortInput.searchFields?.push({
      fieldNames: ['status'],
      operator: SearchOperator.NE,
      searchValue: 'Deleted',
    } as SearchField);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this.writeOpportunities(ctx.em, ctx.res, searchSortInput, fields, tenantFilter);
  }

  /*
    If the orgs field is present, replace it with the 4 org fields
  */
  static addOrgsIfPresent(fields?: string[]): string[] | undefined {
    if (!fields) return;
    const index = fields.indexOf('org1');
    if (index > -1) fields.splice(index, 1, 'org1', 'org2', 'org3', 'org4');
    return fields;
  }

  static async writeOpportunity(
    em: EntityManager<IDatabaseDriver<Connection>>,
    outStream: NodeJS.WritableStream,
    id: string,
    tenantId: string,
    fields?: string[],
  ): Promise<void> {
    try {
      const fieldSpecs = this.getOpportunityFieldSpecs();
      const paths = ['user', 'categories', 'stakeholders', 'owners', 'owners.owner', 'user.tenant'];
      if (fields) {
        if (fields.includes('relatedOpportunityCount')) paths.push('relatedOpportunityCount');
      } else {
        paths.push(
          'relatedOpportunityCount',
          'childOpportunityCount',
          'parentOpportunityCount',
          'linkedOpportunityCount',
          'ownedOpportunities',
          'owningOpportunities',
        );
      }
      const _fields = fields || Object.keys(fieldSpecs);
      const excel = new Excel(outStream, DownloadController.getHeaders(fieldSpecs, _fields));
      const opportunity = await em
        .getRepository(Opportunity)
        .findOne({ id, tenant: { id: tenantId } }, { populate: paths as never[] });
      const rows = DownloadController.mapRows([opportunity], fieldSpecs, _fields);
      excel.writeItems(rows);
      await excel.close();
    } catch (error) {
      console.error('📌 Failed write Opportunity file', error);
      throw new Error(errorKeys.FAILED_TO_GENERATE_FILE);
    }
  }

  // create the opportunities delimited file
  static async writeOpportunities(
    em: EntityManager<IDatabaseDriver<Connection>>,
    outStream: NodeJS.WritableStream,
    searchSortInput?: SearchSortInput,
    fields?: string[],
    tenantFilter?: Record<string, unknown>,
  ): Promise<void> {
    try {
      const fieldSpecs = this.getOpportunityFieldSpecs();
      const paths = ['user', 'categories', 'stakeholders', 'owners', 'owners.owner', 'user.tenant'];
      if (fields) {
        if (fields.includes('relatedOpportunityCount')) paths.push('relatedOpportunityCount');
      } else {
        paths.push(
          'relatedOpportunityCount',
          'childOpportunityCount',
          'parentOpportunityCount',
          'linkedOpportunityCount',
          'ownedOpportunities',
          'owningOpportunities',
        );
      }
      const _fields = fields || Object.keys(fieldSpecs);
      const excel = new Excel(outStream, DownloadController.getHeaders(fieldSpecs, _fields));
      // create the query
      const limit = 100;
      let offset = 0;
      const filter = {
        $and: [
          { ...tenantFilter },
          ...getSearchFilter<Opportunity>(searchSortInput?.searchFields),
          ...getJsonSearchGroupFilter<Opportunity>(searchSortInput?.jsonSearchGroups),
        ],
      };
      const sortFilter = getSortFilter(
        combineSortFields(searchSortInput?.sortFields, [
          { fieldName: 'lastCurated', ascending: false },
          { fieldName: 'id', ascending: true },
        ]),
      );
      let page = null;

      //do the query and write the rows
      do {
        page = await OpportunityController.getOpportunityPage(em, filter, paths, sortFilter, limit, offset);
        const rows = DownloadController.mapRows(page.results, fieldSpecs, _fields);
        excel.writeItems(rows);
        offset = offset + limit;
      } while (page.pageInfo.hasNext);

      await excel.close();
    } catch (error) {
      console.error('📌 Failed write Opportunities file', error);
      throw new Error(errorKeys.FAILED_TO_GENERATE_FILE);
    }
  }

  private static getOpportunityFieldSpecs(): Record<string, FieldSpec> {
    return {
      id: { fieldName: 'id', header: 'ID', width: 3 },
      tenant: {
        fieldName: 'tenant',
        header: 'Portfolio',
        transform: (value) => {
          const tenant = value as Tenant;
          if (tenant) {
            if (tenant.label) return tenant.label + ` (${(value as Tenant).handle})`;
            if (tenant.name) return tenant.name + ` (${(value as Tenant).handle})`;
          }
          return 'n/a';
        },
      },
      visibility: { fieldName: 'visibility', header: 'Visibility', width: 10 },
      createdAt: { fieldName: 'createdAt', header: 'Created', width: 10 },
      lastCurated: { fieldName: 'lastCurated', header: 'Last Curated', width: 10 },
      status: { fieldName: 'status', header: 'Status' },
      statusNotes: { fieldName: 'statusNotes', header: 'Status Notes' },
      title: { fieldName: 'title', header: 'Proposed Problem Title' },
      statement: { fieldName: 'statement', header: 'Problem Statement' },
      context: { fieldName: 'context', header: 'Problem Context' },
      benefits: { fieldName: 'benefits', header: 'Benefits' },
      solutionConcepts: { fieldName: 'solutionConcepts', header: 'Solution Concepts' },
      solutionPathway: { fieldName: 'solutionPathway', header: 'Solution Pathway' },
      solutionPathwayDetails: { fieldName: 'solutionPathwayDetails', header: 'Solution Pathway Details' },
      priority: { fieldName: 'priority', header: 'Priority' },
      priorityNotes: { fieldName: 'priorityNotes', header: 'Priority Notes' },
      initiatives: { fieldName: 'initiatives', header: 'Initiatives' },
      endorsements: { fieldName: 'endorsements', header: 'Endorsements' },
      campaign: { fieldName: 'campaign', header: 'Event' },
      function: { fieldName: 'function', header: 'Warfighting Function' },
      org1: { fieldName: 'org1', header: 'Team 1' },
      org2: { fieldName: 'org2', header: 'Team 2' },
      org3: { fieldName: 'org3', header: 'Team 3' },
      org4: { fieldName: 'org4', header: 'Team 4' },
      firstName: {
        fieldName: 'owners',
        header: 'Owner First Name',
        transform: (value, item) => {
          const opportunityOwners = (value as Collection<OpportunityOwner>).toArray();
          const currentOwner = opportunityOwners.find((o) => o.status === OpportunityOwnerStatus.CURRENT);
          if (currentOwner?.owner?.firstName) {
            return currentOwner.owner.firstName;
          }
          const opportunity = item as Opportunity;
          return opportunity.user?.firstName || 'n/a';
        },
      },
      lastName: {
        fieldName: 'owners',
        header: 'Owner Last Name',
        transform: (value, item) => {
          const opportunityOwners = (value as Collection<OpportunityOwner>).toArray();
          const currentOwner = opportunityOwners.find((o) => o.status === OpportunityOwnerStatus.CURRENT);

          if (currentOwner?.owner?.lastName) {
            return currentOwner.owner.lastName;
          }
          const opportunity = item as Opportunity;
          return opportunity.user?.lastName || 'n/a';
        },
      },
      emailAddress: {
        fieldName: 'owners',
        header: 'Owner Email Address',
        transform: (value, item) => {
          const opportunityOwners = (value as Collection<OpportunityOwner>).toArray();
          const currentOwner = opportunityOwners.find((o) => o.status === OpportunityOwnerStatus.CURRENT);

          if (currentOwner?.owner?.emailAddress) {
            return currentOwner.owner.emailAddress;
          }
          const opportunity = item as Opportunity;
          return opportunity.user?.emailAddress || 'n/a';
        },
      },
      phone: {
        fieldName: 'owners',
        header: 'Owner Phone Number',
        transform: (value, item) => {
          const opportunityOwners = (value as Collection<OpportunityOwner>).toArray();
          const currentOwner = opportunityOwners.find((o) => o.status === OpportunityOwnerStatus.CURRENT);

          if (currentOwner?.owner?.phone) {
            return currentOwner.owner.phone;
          }
          const opportunity = item as Opportunity;
          return opportunity.user?.phone || 'n/a';
        },
      },
      altContact: {
        fieldName: 'owners',
        header: 'Owner Alternate Email',
        transform: (value, item) => {
          const opportunityOwners = (value as Collection<OpportunityOwner>).toArray();
          const currentOwner = opportunityOwners.find((o) => o.status === OpportunityOwnerStatus.CURRENT);

          if (currentOwner?.owner?.altContact) {
            return currentOwner.owner.altContact;
          }
          const opportunity = item as Opportunity;
          return opportunity.user?.altContact || 'n/a';
        },
      },
      categories: {
        fieldName: 'categories',
        header: 'Custom Tags',
        transform: (value) => {
          const categories = (value as Collection<Category>).toArray();
          return categories.reduce(
            (accum, category, index) => `${accum}${category.name || ''}${index !== categories.length - 1 ? ', ' : ''}`,
            '',
          );
        },
      },
      stakeholders: {
        fieldName: 'stakeholders',
        header: 'Stakeholders',
        transform: (value) => {
          const stakeholders = (value as Collection<Stakeholder>).toArray();
          return stakeholders.reduce(
            (accum, stakeholder, index) =>
              `${accum}${stakeholder.name || ''}${index !== stakeholders.length - 1 ? ', ' : ''}`,
            '',
          );
        },
      },
      armyModernizationPriority: {
        fieldName: 'armyModernizationPriority',
        header: 'Army Modernization Priority',
      },
      echelonApplicability: {
        fieldName: 'echelonApplicability',
        header: 'Echelon Applicability',
      },
      transitionInContactLineOfEffort: {
        fieldName: 'transitionInContactLineOfEffort',
        header: 'Transition in Contact Line of Effort',
      },
      operationalRules: {
        fieldName: 'operationalRules',
        header: 'Operational Rules',
      },
      capabilityArea: {
        fieldName: 'capabilityArea',
        header: 'Capability Area',
      },
      relatedOpportunityCount: { fieldName: 'relatedOpportunityCount', header: 'Relationships Total' },
      parentOpportunityCount: { fieldName: 'parentOpportunityCount', header: 'Parent Total' },
      parentOpportunities: {
        fieldName: 'parentOpportunities',
        header: 'Parent ID',
        transform: (value) => {
          const parentOpportunities = value as Opportunity[];
          return parentOpportunities[0]?.id || '';
        },
      },
      childOpportunityCount: { fieldName: 'childOpportunityCount', header: 'Child Total' },
      childOpportunities: {
        fieldName: 'childOpportunities',
        header: 'Child IDs',
        transform: (value) => {
          const childOpportunities = value as Opportunity[];
          return childOpportunities.length > 0
            ? childOpportunities.map((childOpportunity) => childOpportunity.id).join(', ')
            : '';
        },
      },
      linkedOpportunityCount: { fieldName: 'linkedOpportunityCount', header: 'Linked Total' },
      linkedOpportunityIds: {
        fieldName: 'linkedOpportunityIds',
        header: 'Linked IDs',
        transform: (value) => {
          const linkedIds = value as string[];
          return linkedIds.length > 0 ? (value as string[]).join(', ') : '';
        },
      },
    };
  }
}
