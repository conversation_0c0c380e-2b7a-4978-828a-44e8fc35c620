import { Connection, EntityManager, FilterQuery, IDatabaseDriver, QueryOrder, QueryOrderMap } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import {
  CreateOpportunityOwnerInput,
  UpdateOpportunityOwnerInput,
  OpportunityOwnerLinks,
} from 'core/contracts/input/curator/OpportunityOwnerStatus';
import { OpportunityOwnerPage } from 'core/contracts/output/Page';
import { Opportunity } from 'core/entities/Opportunity';
import { OpportunityOwnerStatus } from 'core/entities/OpportunityOwnerStatus';
import { Tenant } from 'core/entities/Tenant';
import {
  getPageInfo,
  getSortFilter,
  handleUpdateLinks,
  getSearchFilter,
  getJsonSearchGroupFilter,
  combineSortFields,
  getQueryBounds,
  caseInsensitiveMatchValue,
} from 'core/storage/queryUtils';
import { OwnershipStatus } from 'core/contracts/enums/OwnershipStatus';
import { Owner } from 'core/entities/Owner';
import { castToShape } from 'core/utils/objectUtils';
import { User } from 'core/entities/User';

const ownerKeysToTransform: (keyof Owner)[] = ['organizationRole'];

const userKeysToTransform: (keyof User)[] = [
  'emailAddress',
  'firstName',
  'lastName',
  'org1',
  'org2',
  'org3',
  'org4',
  'phone',
  'altContact',
];

export class OpportunityOwnerController {
  static async getOpportunityOwnerPage(
    em: EntityManager<IDatabaseDriver<Connection>>,
    filter: FilterQuery<OpportunityOwnerStatus>,
    relationPaths: string[],
    sortFilter: QueryOrderMap<OpportunityOwnerStatus>,
    limit: number,
    offset: number,
  ): Promise<OpportunityOwnerPage> {
    const [results, totalCount] = await em
      .getRepository(OpportunityOwnerStatus)
      .findAndCount(filter, { populate: relationPaths as never, orderBy: sortFilter, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  public static async queryOpportunityOwners({
    em,
    tenantId,
    pagingInput,
    searchSortInput,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    tenantId: string;
    pagingInput?: PagingInput;
    searchSortInput?: SearchSortInput;
    relationPaths?: string[];
  }): Promise<OpportunityOwnerPage> {
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter: FilterQuery<OpportunityOwnerStatus> = {
      $and: [
        { owner: { tenant: { id: tenantId } } },
        ...getSearchFilter<OpportunityOwnerStatus>(searchSortInput?.searchFields),
        ...getJsonSearchGroupFilter<OpportunityOwnerStatus>(searchSortInput?.jsonSearchGroups),
      ],
    };
    const sortFilter = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, [
        { fieldName: 'createdAt', ascending: false },
        { fieldName: 'id', ascending: true },
      ]),
    );
    return OpportunityOwnerController.getOpportunityOwnerPage(em, filter, relationPaths, sortFilter, limit, offset);
  }

  static async getOpportunityOwner({
    em,
    id,
    tenantId,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    tenantId: string;
    relationPaths: string[];
  }): Promise<OpportunityOwnerStatus | null> {
    const result = await em
      .getRepository(OpportunityOwnerStatus)
      .findOne({ id, owner: { tenant: { id: tenantId } } }, { populate: relationPaths as never[] });
    return result;
  }

  public static async createOpportunityOwner(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    input: CreateOpportunityOwnerInput,
    relationPaths: string[],
    ownerPaths: string[] = [],
    opportunityPaths: string[] = [],
    links?: OpportunityOwnerLinks,
  ): Promise<OpportunityOwnerStatus> {
    const opportunityId = input.opportunityId;
    if (!opportunityId) throw Error(errorKeys.ARGUMENT_VALIDATION_ERROR);
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const additionalPaths = [];
    if (links?.opportunity?.length) additionalPaths.push('opportunity');
    if (links?.owner?.length) additionalPaths.push('owner');

    const existingUser = input?.userId
      ? await em.getRepository(User).findOne({ id: input.userId })
      : await em
          .getRepository(User)
          .findOne({ emailAddress: caseInsensitiveMatchValue(input.emailAddress), tenant: { id: tenantId } });

    const userInfo = castToShape<User>(input, userKeysToTransform);
    if (existingUser) {
      existingUser.modify(userInfo);
    }

    const ownerInput = castToShape<Owner>(input, ownerKeysToTransform);
    const owner = existingUser
      ? Owner.newOwner({ ...ownerInput, user: existingUser, tenant })
      : Owner.newOwner({ ...ownerInput, user: User.newUser({ ...userInfo, tenant }), tenant });

    const opportunity = await em.getRepository(Opportunity).findOneOrFail({ id: opportunityId });
    const opportunityOwnerStatus = OpportunityOwnerStatus.newOpportunityOwnerStatus({ ...input, opportunity, owner });

    if (links) await this.handleLinks(opportunityOwnerStatus, links, em, ownerPaths, opportunityPaths);
    await em.persist(opportunityOwnerStatus).flush();
    await em.populate(opportunityOwnerStatus, [...relationPaths, ...additionalPaths] as never[]);
    return opportunityOwnerStatus;
  }

  public static async updateOpportunityOwner(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    id: string,
    input: UpdateOpportunityOwnerInput,
    relationPaths: string[],
    ownerPaths: string[] = [],
    opportunityPaths: string[] = [],
    links?: OpportunityOwnerLinks,
  ): Promise<OpportunityOwnerStatus> {
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId }, { populate: ['owners'] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    const additionalPaths = [];
    if (links?.opportunity?.length) additionalPaths.push('opportunity');
    if (links?.owner?.length) additionalPaths.push('owner');
    const opportunityOwnerStatus = await em
      .getRepository(OpportunityOwnerStatus)
      .findOneOrFail({ id }, { populate: [...relationPaths, ...additionalPaths] as never });
    if (input.status === OwnershipStatus.PREVIOUS) input.statusSetPreviousAt = new Date();
    if (input.status === OwnershipStatus.REMOVED) input.statusSetRemovedAt = new Date();
    if (input.organizationRole) opportunityOwnerStatus.owner.organizationRole = input.organizationRole;
    opportunityOwnerStatus.modify(input);
    const updatedOpportunityOwnerStatus = links
      ? await this.handleLinks(opportunityOwnerStatus, links, em, ownerPaths, opportunityPaths)
      : opportunityOwnerStatus;
    await em.persist(updatedOpportunityOwnerStatus).flush();
    await em.populate(updatedOpportunityOwnerStatus, ownerPaths as never[]);
    return updatedOpportunityOwnerStatus;
  }

  private static async handleLinks(
    opportunityOwnerStatus: OpportunityOwnerStatus,
    links: OpportunityOwnerLinks,
    em: EntityManager,
    ownerPaths: string[] = [],
    opportunityPaths: string[] = [],
  ): Promise<OpportunityOwnerStatus> {
    let updatedOwner = await handleUpdateLinks<OpportunityOwnerStatus, Owner>(
      em,
      opportunityOwnerStatus,
      'owner',
      ownerPaths,
      Owner,
      links?.owner,
    );
    updatedOwner = await handleUpdateLinks<OpportunityOwnerStatus, Opportunity>(
      em,
      opportunityOwnerStatus,
      'opportunity',
      opportunityPaths,
      Opportunity,
      links?.opportunity,
    );
    return updatedOwner;
  }
}
