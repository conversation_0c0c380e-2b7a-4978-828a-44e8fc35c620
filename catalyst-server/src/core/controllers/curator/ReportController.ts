import { EntityRepository } from '@mikro-orm/postgresql';
import { verifyScope } from 'core/auth/authUtils';
import { CalcOperator } from 'core/contracts/enums/CalcOperator';
import { QueryEntity } from 'core/contracts/enums/QueryEntity';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  Calculation,
  FieldOperations,
  ReportInput,
  Scope,
  SearchSortInput,
} from 'core/contracts/input/base/CommonInput';
import { CalcResponse, OperationResult } from 'core/contracts/output/CalcResponse';
import { AppContext } from 'core/core';
import { CoreEntity } from 'core/entities/CoreEntity';
import { getJsonSearchGroupFilter, getSearchFilter, getSortFilter } from 'core/storage/queryUtils';
import { getTenantFilterForScope, isEmptyScope } from '../controllers';
import { ReportResponse } from 'core/contracts/output/ReportResponse';
import { ReporterAccessor } from 'core/reports/ReporterAccessor';
import { Parallel } from 'core/utils/Async';

const allowedEntities = ['Opportunity', 'Submission', 'User'];

export class ReportController {
  static async report<T extends CoreEntity>({
    ctx,
    reportInput,
    scope,
  }: {
    ctx: AppContext;
    reportInput: ReportInput;
    scope?: Scope;
  }): Promise<ReportResponse> {
    if (isEmptyScope(scope)) return { reports: [] };
    if (scope && !(await verifyScope({ scope, context: ctx }))) throw new Error(errorKeys.UNAUTHORIZED);
    const tenantFilter = await getTenantFilterForScope(ctx, scope);
    const reports = await Parallel.map(reportInput.queries, async (query) => {
      const filter = {
        $and: [
          { tenant: tenantFilter },
          ...getSearchFilter<T>(query.searchSortInput?.searchFields),
          ...getJsonSearchGroupFilter<T>(query.searchSortInput?.jsonSearchGroups),
        ],
      };
      // const sortFilter = getSortFilter(searchSortInput?.sortFields);

      return ReporterAccessor.getReporter(query.reportName).runReport({ em: ctx.em.fork(), filter, query });
    });
    return { reports };
  }

  /* 
  @TODO need to model FieldOperation to also include a 'distinct' field - right now we could
  return incorrect results on a distinct aggregate query
  Also - see version 1c2afff28594033e28e299f1573f5c6337700117 of this file for the start of a subquery implementation
  */
  static async calculation<T extends CoreEntity>({
    ctx,
    searchSortInput,
    calculation,
    scope,
    entityName,
    customTenantFilter,
  }: {
    ctx: AppContext;
    calculation: Calculation;
    searchSortInput?: SearchSortInput;
    relationPaths?: string[];
    scope?: Scope;
    entityName: QueryEntity;
    customTenantFilter?: Record<string, unknown>
  }): Promise<CalcResponse> {
    if (isEmptyScope(scope)) return { operationResults: [] };
    if (scope && !(await verifyScope({ scope, context: ctx }))) throw new Error(errorKeys.UNAUTHORIZED);
    if (!allowedEntities.includes(entityName)) throw new Error(errorKeys.UNAUTHORIZED);
    const defaultTenantFilter = await getTenantFilterForScope(ctx, scope);
    const tenantFilter = customTenantFilter ? customTenantFilter : { tenant: defaultTenantFilter};
    const filter = {
      $and: [
        tenantFilter,
        ...getSearchFilter<T>(searchSortInput?.searchFields),
        ...getJsonSearchGroupFilter<T>(searchSortInput?.jsonSearchGroups),
      ],
    };
    const sortFilter = getSortFilter(searchSortInput?.sortFields);
    //const select = operations.map(operation => operation.fieldNames);
    const entityVar = 'e';

    const { operations } = calculation;
    const qb = ((<unknown>ctx.em.getRepository(entityName)) as EntityRepository<T>)
      .qb(entityVar)
      .select(
        operations.map((operation) => ReportController.buildSelectParams(operation, !!calculation.distinct) as any),
        !!calculation.distinct,
      )
      .where(filter)
      .orderBy(sortFilter);
    const results = await qb.execute('all');

    const operationResults = operations.map((operation) => {
      const { fieldName, operator } = operation;
      const result = operator
        ? results.map((result) => (result as any)[operator])
        : fieldName
        ? results.map((result) => (result as any)[fieldName])
        : results;
      return { result } as OperationResult;
    });

    return { operationResults };
  }

  private static buildSelectParams(fieldOperation: FieldOperations, distinct: boolean) {
    const { fieldName, operator } = fieldOperation;
    const distinctStr = distinct ? 'DISTINCT ' : '';
    if (operator) {
      const opStr = ReportController.mapOperation(operator);
      return !!fieldName ? `${opStr}(${distinctStr}${fieldName})` : `${opStr}(${distinctStr}*)`;
    } else if (fieldName) {
      return fieldName;
    }
    // default to count
    return `COUNT(${distinctStr}*)`;
  }

  private static mapOperation(operator: CalcOperator) {
    if (operator === CalcOperator.COUNT) {
      return 'COUNT';
    } else if (operator === CalcOperator.SUM) {
      return 'SUM';
    } else if (operator === CalcOperator.AVG) {
      return 'AVG';
    }
  }
}
