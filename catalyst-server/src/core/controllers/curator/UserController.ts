import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { CreateUserInput, UpdateUserInput, UserLinks } from 'core/contracts/input/curator/UserInput';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { Role } from 'core/entities/Role';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import { caseInsensitiveMatchValue, handleUpdateLinks } from 'core/storage/queryUtils';

export class UserController {
  /***
   *       ___     __      _        ____
   *      / _ |___/ /_ _  (_)__    / __ \___  ___
   *     / __ / _  /  ' \/ / _ \  / /_/ / _ \(_-<
   *    /_/ |_\_,_/_/_/_/_/_//_/  \____/ .__/___/
   *                                  /_/
   */

  public static async createUser(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    input: CreateUserInput,
    links?: UserLinks,
    userPaths: string[] = [],
    privilegeGroupPaths: string[] = [],
  ): Promise<User> {
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId }, { populate: ['privilegeGroups'] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    if (!input.emailAddress) throw Error(errorKeys.ARGUMENT_VALIDATION_ERROR);
    const existingUser = await em
      .getRepository(User)
      .findOne(
        { emailAddress: caseInsensitiveMatchValue(input.emailAddress), tenant },
        { populate: ['privilegeGroups', ...userPaths] as never },
      );
    // if user exists but is Unverified, update the user and set the status to Verified
    if (existingUser) {
      if (existingUser.status === VerifiedStatus.VERIFIED) throw Error(errorKeys.USER_EXISTS_AS_VERIFIED);
      existingUser.modify({ ...input, status: VerifiedStatus.VERIFIED });
    }
    const user = existingUser || User.newUser({ ...input, status: VerifiedStatus.VERIFIED });
    user.tenant = tenant;
    if (links) await this.handleLinks(user, links, em, privilegeGroupPaths, tenant);
    await em.persist(user).flush();
    await em.populate(user, userPaths as never[]);
    return user;
  }

  public static async updateUser(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    id: string,
    input: UpdateUserInput,
    links?: UserLinks,
    userPaths: string[] = [],
    privilegeGroupPaths: string[] = [],
  ): Promise<User> {
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId }, { populate: ['privilegeGroups'] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const user = await em
      .getRepository(User)
      .findOneOrFail({ id, tenant }, { populate: ['privilegeGroups', ...userPaths] as never });
    user.modify(input);
    const updatedUser = links ? await this.handleLinks(user, links, em, privilegeGroupPaths, tenant) : user;
    await em.persist(updatedUser).flush();
    await em.populate(updatedUser, userPaths as never[]);
    return updatedUser;
  }

  private static async handleLinks(
    user: User,
    links: UserLinks,
    em: EntityManager,
    privilegeGroupPaths: string[],
    tenant: Tenant,
  ): Promise<User> {
    if (links.roleNames) await this.setUserRoles(user, links.roleNames, em);
    // If the tenant don't worry with updating user groups
    if (tenant.privilegeGroups && tenant.privilegeGroups.length > 0)
      this.setUserPrivilegeGroups(user, tenant.privilegeGroups.getItems());

    const updatedUser = await handleUpdateLinks<User, PrivilegeGroup>(
      em,
      user,
      'privilegeGroups',
      privilegeGroupPaths,
      PrivilegeGroup,
      links?.privilegeGroups,
    );
    return updatedUser;
  }

  private static async setUserRoles(user: User, roleNames: RoleNames[], em: EntityManager): Promise<void> {
    const roles = await em.getRepository(Role).findAll();
    const roleSet = roleNames.map((roleName) => {
      const role = roles.find((role) => role.name === roleName);
      if (!role) throw Error(`Role ${roleName} not found in database`);
      return role;
    });
    user.roles.set(roleSet);
  }

  private static setUserPrivilegeGroups(user: User, privilegeGroups: PrivilegeGroup[]): void {
    const userRoles = user.roles;
    // IN-1106: If the user has no roles or the only role they have is curator, we need to remove them
    // from the privilege groups on the tenant and all child tenants.
    if (
      userRoles.length === 0 ||
      (userRoles.length === 1 && userRoles.exists((role: Role) => role.name === RoleNames.CURATOR))
    ) {
      privilegeGroups.forEach((group) => user.privilegeGroups.remove(group));
      return;
    }

    // IN-1106: If the user has a role higher than curator we need to associate the user's
    // privilegeGroups with that of the tenant and all child tenants.
    const userPrivilegeGroups = user.privilegeGroups;
    const newGroups = privilegeGroups.filter((group) => !userPrivilegeGroups.contains(group));
    user.privilegeGroups.set([...newGroups, ...userPrivilegeGroups]);
  }
}
