import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  CreatePrivilegeGroupInput,
  CreatePrivilegeInput,
  UpdatePrivilegeGroupInput,
  UpdatePrivilegeGroupLinks,
  UpdatePrivilegeInput,
} from 'core/contracts/input/base/PrivilegeGroupInput';
import { Privilege } from 'core/entities/Privilege';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { Tenant } from 'core/entities/Tenant';
import { caseInsensitiveMatchValue, handleUpdateLinks } from 'core/storage/queryUtils';

export class PrivilegeController {
  static async getPrivilegeGroup({
    em,
    id,
    tenantId,
    name,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    tenantId: string;
    id?: string;
    name?: string;
    relationPaths: string[];
  }): Promise<PrivilegeGroup | null> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const filter = id ? { id } : { name: caseInsensitiveMatchValue(name) };
    return em.getRepository(PrivilegeGroup).findOne({ tenant, ...filter }, { populate: relationPaths as never });
  }

  static async createPrivilegeGroup(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    input: CreatePrivilegeGroupInput,
  ): Promise<PrivilegeGroup> {
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const privilegeGroup = PrivilegeGroup.newPrivilegeGroup(input, tenant);
    await em.persist(privilegeGroup).flush();
    return privilegeGroup;
  }

  static async updatePrivilegeGroup(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    id: string,
    input: UpdatePrivilegeGroupInput,
    relationPaths: string[],
    privilegesPaths: string[],
    links?: UpdatePrivilegeGroupLinks,
  ): Promise<PrivilegeGroup> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const privilegeGroup = await em
      .getRepository(PrivilegeGroup)
      .findOne({ id, tenant }, { populate: relationPaths as never });
    if (!privilegeGroup) throw Error(errorKeys.OBJECT_NOT_FOUND);
    privilegeGroup.modify(input);
    const updatedPrivilegeGroup = await handleUpdateLinks<PrivilegeGroup, Privilege>(
      em,
      privilegeGroup,
      'privileges',
      privilegesPaths,
      Privilege,
      links?.privileges,
    );

    await em.persist(updatedPrivilegeGroup).flush();
    await em.populate(updatedPrivilegeGroup, relationPaths as never[]);
    return updatedPrivilegeGroup;
  }

  static async deletePrivilegeGroup(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    id: string,
  ): Promise<boolean> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const privilegeGroup = await em.getRepository(PrivilegeGroup).findOneOrFail({ id });
    await em.remove(privilegeGroup).flush();
    return true;
  }

  static async getPrivilege({
    em,
    tenantId,
    id,
    name,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    tenantId: string;
    id?: string;
    name?: string;
    relationPaths: string[];
  }): Promise<Privilege | null> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const filter = id ? { id } : { name: caseInsensitiveMatchValue(name) };
    return em.getRepository(Privilege).findOne({ ...filter }, { populate: relationPaths as never });
  }

  static async createPrivilege(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    input: CreatePrivilegeInput,
  ): Promise<Privilege> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const privilege = Privilege.newPrivilege(input);
    await em.persist(privilege).flush();
    return privilege;
  }

  static async updatePrivilege(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    id: string,
    input: UpdatePrivilegeInput,
    relationPaths: string[],
  ): Promise<Privilege> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const privilege = await em.getRepository(Privilege).findOne({ id }, { populate: relationPaths as never });
    if (!privilege) throw Error(errorKeys.OBJECT_NOT_FOUND);
    privilege.modify(input);
    await em.persist(privilege).flush();
    return privilege;
  }

  static async deletePrivilege(em: EntityManager<IDatabaseDriver<Connection>>, id: string): Promise<boolean> {
    const privilege = await em.getRepository(Privilege).findOneOrFail({ id });
    await em.remove(privilege).flush();
    return true;
  }
}
