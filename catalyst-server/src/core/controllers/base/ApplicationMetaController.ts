import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ApplicationMetaInput } from 'core/contracts/input/base/ApplicationMetaInput';
import { ApplicationMeta } from 'core/entities/ApplicationMeta';
import { Tenant } from 'core/entities/Tenant';
import { TenantMeta } from 'core/entities/TenantMeta';

export class ApplicationMetaController {
  static async getApplicationMeta(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    tenantId: string,
    relationPaths: string[] = [],
  ): Promise<ApplicationMeta | null> {
    return em
      .getRepository(ApplicationMeta)
      .findOne({ id, tenant: { id: tenantId } }, { populate: relationPaths as never[] });
  }

  static async createApplicationMeta(
    em: EntityManager<IDatabaseDriver<Connection>>,
    input: ApplicationMetaInput,
    tenantId: string,
  ): Promise<ApplicationMeta> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const applicationMeta = ApplicationMeta.newApplicationMeta(input);
    applicationMeta.tenant = tenant;
    await em.persist(applicationMeta).flush();
    return applicationMeta;
  }

  static async updateApplicationMeta(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    input: ApplicationMetaInput,
    tenantId: string,
    relationPaths: string[] = [],
  ): Promise<ApplicationMeta> {
    const tenant = await em.getReference(Tenant, tenantId);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const applicationMeta = await em
      .getRepository(ApplicationMeta)
      .findOne({ id, tenant: { id: tenantId } }, { populate: relationPaths as never });
    if (!applicationMeta) throw Error(errorKeys.OBJECT_NOT_FOUND);
    applicationMeta.modify(input);
    await em.persist(applicationMeta).flush();
    await em.populate(applicationMeta, relationPaths as never[]);
    return applicationMeta;
  }

  static async deleteApplicationMeta(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    tenantId: string,
  ): Promise<boolean> {
    const applicationMeta = await em.getRepository(ApplicationMeta).findOneOrFail({ id, tenant: { id: tenantId } });
    await em.remove(applicationMeta).flush();
    return true;
  }
}
