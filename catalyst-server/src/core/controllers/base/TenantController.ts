import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, SearchField, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CreateTenantInput, UpdateTenantInput } from 'core/contracts/input/base/TenantInput';
import { TenantInfoPage, TenantPage } from 'core/contracts/output/Page';
import { TenantInfo } from 'core/contracts/output/TenantInfo';
import { Role } from 'core/entities/Role';
import { Tenant } from 'core/entities/Tenant';
import { TenantAlias } from 'core/entities/TenantAlias';
import { TenantMeta } from 'core/entities/TenantMeta';
import { User } from 'core/entities/User';
import {
  caseInsensitiveMatchValue,
  combineSortFields,
  defaultSecondarySortFields,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
} from 'core/storage/queryUtils';
import { TenantAliasController } from './TenantAliasController';

export class TenantController {
  static async getTenant(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    paths: string[] = [],
  ): Promise<Tenant | null> {
    return em.getRepository(Tenant).findOne({ id }, { populate: paths as never[] });
  }

  static async getTenantByHandle(
    em: EntityManager<IDatabaseDriver<Connection>>,
    handle: string,
    paths: string[] = [],
  ): Promise<Tenant | null> {
    return em.getRepository(Tenant).findOne({ handle }, { populate: paths as never[] });
  }

  static async getTenantByHandleOrAlias(
    em: EntityManager<IDatabaseDriver<Connection>>,
    handleOrAlias: string,
    paths: string[] = [],
  ): Promise<Tenant | null> {
    const matchValue = caseInsensitiveMatchValue(handleOrAlias);
    return await em
      .getRepository(Tenant)
      .findOne({ $or: [{ handle: matchValue }, { aliases: { handle: matchValue } }] }, { populate: paths as never[] });
  }

  static async getTenantInfoByHandleOrAlias(
    em: EntityManager<IDatabaseDriver<Connection>>,
    handleOrAlias: string,
    paths: string[] = [],
  ): Promise<TenantInfo | null> {
    const matchValue = caseInsensitiveMatchValue(handleOrAlias);
    let tenant;
    // try to get by tenant
    tenant = await em.getRepository(Tenant).findOne({ handle: matchValue }, { populate: paths as never[] });
    if (tenant) {
      return TenantInfo.get(tenant);
    }
    // try to get by tenantAlias
    tenant = await em
      .getRepository(Tenant)
      .findOne({ aliases: { handle: matchValue } }, { populate: paths as never[] });
    if (tenant) {
      const tenantAlias: TenantAlias | null = await em
        .getRepository(TenantAlias)
        .findOne({ handle: matchValue }, { populate: paths as never[] });
      return TenantInfo.get(tenant, tenantAlias);
    }
    return null;
  }

  static async findTenantInfoByName(
    em: EntityManager<IDatabaseDriver<Connection>>,
    name: string,
  ): Promise<TenantInfo[]> {
    const tenantPage = await TenantController.queryTenants(
      em,
      { pageSize: 100 },
      {
        searchFields: [{ fieldNames: ['name'], operator: SearchOperator.MATCH, searchValue: name } as SearchField],
        sortFields: [{ fieldName: 'name', ascending: true }],
      },
      ['meta'],
    );
    const tenantAliasPage = await TenantAliasController.queryTenantAliases(
      em,
      undefined,
      { pageSize: 100 },
      {
        searchFields: [{ fieldNames: ['name'], operator: SearchOperator.MATCH, searchValue: name } as SearchField],
        sortFields: [{ fieldName: 'name', ascending: true }],
      },
      ['tenant', 'meta'],
    );
    const results: TenantInfo[] = [];
    tenantPage.results.forEach((tenant) => results.push(TenantInfo.get(tenant)));
    tenantAliasPage.results.forEach((tenantAlias) => results.push(TenantInfo.get(tenantAlias.tenant, tenantAlias)));
    return results;
  }

  static async queryTenants(
    em: EntityManager<IDatabaseDriver<Connection>>,
    pagingInput?: PagingInput,
    searchSortInput?: SearchSortInput,
    paths: string[] = [],
  ): Promise<TenantPage> {
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter = { $and: [...getSearchFilter<Tenant>(searchSortInput?.searchFields)] };
    const orderBy = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields(), [
        { fieldName: 'handle', ascending: true },
      ]),
    );
    const [results, totalCount] = await em
      .getRepository(Tenant)
      .findAndCount(filter, { populate: paths as never, orderBy, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  static async createTenant(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantInput: CreateTenantInput,
    adminPass?: string,
  ): Promise<{ tenant: Tenant; admin: User }> {
    if (!adminPass) throw Error(errorKeys.ARGUMENT_VALIDATION_ERROR);
    const existingTenant = await em
      .getRepository(Tenant)
      .findOne({ handle: caseInsensitiveMatchValue(tenantInput.handle) });
    if (existingTenant) throw Error(errorKeys.OBJECT_ALREADY_EXISTS);
    const { config, theme, content, ...rest } = tenantInput;
    const tenant = Tenant.newTenant({ ...rest });
    tenant.meta = TenantMeta.newTenantMeta({ config, content, theme });
    await em.persist(tenant);
    const admin = await TenantController.createTenantAdmin(em, tenantInput.handle, adminPass);
    return { tenant, admin };
  }

  static async createTenantAdmin(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantHandle: string,
    adminPass?: string,
  ): Promise<User> {
    const roles = await em.getRepository(Role).findAll();
    const adminRole = roles.find((role) => role.name === RoleNames.ADMIN);
    if (!adminRole) throw Error(`Role 'admin' not found in database`);
    const curatorRole = roles.find((role) => role.name === RoleNames.CURATOR);
    if (!curatorRole) throw Error(`Role 'curator' not found in database`);
    const analystRole = roles.find((role) => role.name === RoleNames.ANALYST);
    if (!analystRole) throw Error(`Role 'analyst' not found in database`);
    const tenant = await em.getRepository(Tenant).findOne({ handle: caseInsensitiveMatchValue(tenantHandle) });
    if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
    const admin = User.newUser({
      emailAddress: 'admin@default',
      password: adminPass,
      firstName: '',
      lastName: '',
      org1: '',
      org2: '',
      phone: '',
      status: VerifiedStatus.VERIFIED,
    });
    admin.tenant = tenant;
    admin.roles.add(adminRole, curatorRole, analystRole);
    await em.persist(admin);
    return admin;
  }

  static async createTenantAlias(
    em: EntityManager<IDatabaseDriver<Connection>>,
    existingHandle: string,
    tenantAlias: Partial<TenantAlias>,
  ): Promise<Tenant> {
    const tenant = await TenantController.getTenantByHandle(em, existingHandle);
    if (!tenant) throw Error(`No tenant found for ${existingHandle}`);
    const newTenantAlias = TenantAlias.newTenantAlias(tenantAlias);
    newTenantAlias.tenant = tenant;
    em.persist(newTenantAlias);
    return tenant;
  }

  static async updateTenant(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    input: UpdateTenantInput,
    relationPaths: string[],
  ): Promise<Tenant> {
    const tenant = await em.getRepository(Tenant).findOne({ id }, { populate: relationPaths as never[] });
    if (!tenant) throw new Error(errorKeys.OBJECT_NOT_FOUND);
    const { config, theme, content, ...rest } = input;
    tenant.modify(rest);
    if (!tenant.meta) tenant.meta = TenantMeta.newTenantMeta({});
    if (config) tenant.meta.config = config;
    if (theme) tenant.meta.theme = theme;
    if (content) tenant.meta.content = content;
    await em.persistAndFlush(tenant);
    return tenant;
  }

  static async deleteTenant(em: EntityManager<IDatabaseDriver<Connection>>, id: string): Promise<boolean> {
    const tenant = await em.getRepository(Tenant).findOneOrFail({ id }, { populate: ['users', 'aliases'] });
    await tenant.removeAllAssociations(em);
    await em.remove(tenant).flush();
    return true;
  }
}
