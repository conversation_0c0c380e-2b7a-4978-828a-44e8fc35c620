import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CreateTenantAliasInput, UpdateTenantAliasInput } from 'core/contracts/input/base/TenantAliasInput';
import { TenantAliasPage } from 'core/contracts/output/Page';
import { Tenant } from 'core/entities/Tenant';
import { TenantAlias } from 'core/entities/TenantAlias';
import { TenantMeta } from 'core/entities/TenantMeta';
import {
  caseInsensitiveMatchValue,
  combineSortFields,
  defaultSecondarySortFields,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
} from 'core/storage/queryUtils';

export class TenantAliasController {
  static async queryTenantAliases(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId?: string,
    pagingInput?: PagingInput,
    searchSortInput?: SearchSortInput,
    relationPaths: string[] = [],
  ): Promise<TenantAliasPage> {
    const { limit, offset } = getQueryBounds(pagingInput);
    const tenantFilter = tenantId ? [{ tenant: { id: tenantId } }] : [];
    const filter = {
      $and: [...tenantFilter, ...getSearchFilter<TenantAlias>(searchSortInput?.searchFields)],
    };
    const orderBy = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields(), [
        { fieldName: 'handle', ascending: true },
      ]),
    );
    const [results, totalCount] = await em
      .getRepository(TenantAlias)
      .findAndCount(filter, { populate: relationPaths as never, orderBy, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  static async getTenantAlias({
    em,
    tenantId,
    id,
    handle,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    tenantId: string;
    id?: string;
    handle?: string;
    relationPaths: string[];
  }): Promise<TenantAlias | null> {
    const filter = id ? { id } : { handle: caseInsensitiveMatchValue(handle) };
    return em
      .getRepository(TenantAlias)
      .findOne({ ...filter, tenant: { id: tenantId } }, { populate: relationPaths as never });
  }

  static async createTenantAlias(
    em: EntityManager<IDatabaseDriver<Connection>>,
    input: CreateTenantAliasInput,
    tenantId: string,
  ): Promise<TenantAlias> {
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const existingTenantAlias = await em
      .getRepository(TenantAlias)
      .findOne({ handle: caseInsensitiveMatchValue(input.handle), tenant: { id: tenantId } });
    if (existingTenantAlias) throw Error(errorKeys.OBJECT_ALREADY_EXISTS);
    const { config, theme, content, ...rest } = input;
    const tenantAlias = TenantAlias.newTenantAlias(rest);
    tenantAlias.meta = TenantMeta.newTenantMeta({ config, content, theme });
    tenantAlias.tenant = tenant;
    await em.persist(tenantAlias).flush();
    return tenantAlias;
  }

  static async updateTenantAlias(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    input: UpdateTenantAliasInput,
    tenantId: string,
    relationPaths: string[],
  ): Promise<TenantAlias> {
    const tenantAlias = await em
      .getRepository(TenantAlias)
      .findOne({ id, tenant: { id: tenantId } }, { populate: relationPaths as never });
    if (!tenantAlias) throw Error(errorKeys.OBJECT_NOT_FOUND);
    const { config, theme, content, ...rest } = input;
    tenantAlias.modify(rest);
    if (!tenantAlias.meta) tenantAlias.meta = TenantMeta.newTenantMeta({});
    if (config) tenantAlias.meta.config = config;
    if (theme) tenantAlias.meta.theme = theme;
    if (content) tenantAlias.meta.content = content;
    await em.persist(tenantAlias).flush();
    await em.populate(tenantAlias, relationPaths as never[]);
    return tenantAlias;
  }

  static async deleteTenantAlias(
    em: EntityManager<IDatabaseDriver<Connection>>,
    id: string,
    tenantId: string,
  ): Promise<boolean> {
    const tenantAlias = await em.getRepository(TenantAlias).findOneOrFail({ id, tenant: { id: tenantId } });
    await em.remove(tenantAlias).flush();
    return true;
  }
}
