import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { AuthResponse } from 'core/contracts/output/AuthResponse';
import { AppContext } from 'core/core';
import { User } from 'core/entities/User';
import { TenantController } from './TenantController';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { caseInsensitiveMatchValue } from 'core/storage/queryUtils';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { roleMap } from 'core/contracts/enums/RoleNames';
import { getToken } from 'core/auth/authUtils';
import { PayloadKeys } from 'core/auth/JwtPayload';

export class UserController {
  public static async getUser(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    id: string,
    paths: string[] = [],
  ): Promise<User | null> {
    return em.getRepository(User).findOne({ id, tenant: { id: tenantId } }, { populate: paths as never });
  }

  public static async login({
    ctx,
    userName,
    password,
    tenantHandle,
    paths = [],
  } : {
    ctx: AppContext,
    userName: string,
    password: string,
    tenantHandle: string,
    paths?: string[],
  }
  ): Promise<AuthResponse> {
    // find tenant
    const tenant = await TenantController.getTenantByHandleOrAlias(ctx.em, tenantHandle);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const user = await ctx.em
      .getRepository(User)
      .findOne(
        { emailAddress: caseInsensitiveMatchValue(userName), tenant, status: VerifiedStatus.VERIFIED },
        { populate: [...paths, 'roles'] as never },
      );
    if (!user || !(await user.isValidPassword(password))) throw new Error(errorKeys.INVALID_LOGIN);

    const mappedRoles = user.roles.getItems().map((role) => roleMap[role.name]);

    const { token, expiresAt } = getToken({
      [PayloadKeys.USER_KEY]: user.id,
      [PayloadKeys.TENANT_KEY]: tenant.id,
      [PayloadKeys.ROLES_KEY]: mappedRoles,
    });
    return { user, token, expiresAt };
  }

}
