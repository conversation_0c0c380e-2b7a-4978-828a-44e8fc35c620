import { Connection, EntityManager, FilterQuery, IDatabaseDriver, QueryOrderMap } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CreateOwnerInput, UpdateOwnerInput, OwnerLinks } from 'core/contracts/input/user/OwnerInput';
import { OwnerPage } from 'core/contracts/output/Page';
import { Opportunity } from 'core/entities/Opportunity';
import { Owner } from 'core/entities/Owner';
import { Tenant } from 'core/entities/Tenant';
import {
  getPageInfo,
  getSortFilter,
  handleUpdateLinks,
  getSearchFilter,
  getJsonSearchGroupFilter,
  combineSortFields,
  getQueryBounds,
  caseInsensitiveMatchValue,
} from 'core/storage/queryUtils';
import { User } from 'core/entities/User';
import { castToShape } from 'core/utils/objectUtils';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { Role } from 'core/entities/Role';

const userKeysToTransform: (keyof User)[] = [
  'emailAddress',
  'firstName',
  'lastName',
  'org1',
  'org2',
  'org3',
  'org4',
  'phone',
  'altContact',
];

export class OwnerController {
  static async getOwnerPage(
    em: EntityManager<IDatabaseDriver<Connection>>,
    filter: FilterQuery<Owner>,
    relationPaths: string[],
    sortFilter: QueryOrderMap<Owner>,
    limit: number,
    offset: number,
  ): Promise<OwnerPage> {
    const [results, totalCount] = await em
      .getRepository(Owner)
      .findAndCount(filter, { populate: relationPaths as never, orderBy: sortFilter, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  public static async queryOwners({
    em,
    tenantId,
    pagingInput,
    searchSortInput,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    tenantId: string;
    pagingInput?: PagingInput;
    searchSortInput?: SearchSortInput;
    relationPaths?: string[];
  }): Promise<OwnerPage> {
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter: FilterQuery<Owner> = {
      $and: [
        { tenant: { id: tenantId } },
        ...getSearchFilter<Owner>(searchSortInput?.searchFields),
        ...getJsonSearchGroupFilter<Owner>(searchSortInput?.jsonSearchGroups),
      ],
    };
    const sortFilter = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, [
        { fieldName: 'createdAt', ascending: false },
        { fieldName: 'id', ascending: true },
      ]),
    );
    return OwnerController.getOwnerPage(em, filter, relationPaths, sortFilter, limit, offset);
  }

  static async getOwner({
    em,
    id,
    tenantId,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    tenantId: string;
    relationPaths: string[];
  }): Promise<Owner | null> {
    const result = await em
      .getRepository(Owner)
      .findOne({ id, tenant: { id: tenantId } }, { populate: relationPaths as never[] });
    return result;
  }

  static async getOwnerByUserId({
    em,
    userId,
    tenantId,
    relationPaths = [],
  }: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    userId: string;
    tenantId: string;
    relationPaths: string[];
  }): Promise<Owner | null> {
    const result = await em
      .getRepository(Owner)
      .findOne({ user: { id: userId }, tenant: { id: tenantId } }, { populate: relationPaths as never[] });
    return result;
  }

  public static async createOwner(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    input: CreateOwnerInput,
    relationPaths: string[],
    userPaths: string[] = [],
    opportunityPaths: string[] = [],
    links?: OwnerLinks,
  ): Promise<Owner> {
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const additionalPaths = [];
    if (links?.opportunities?.length) additionalPaths.push('opportunities');
    if (links?.user?.length) additionalPaths.push('users');
    const existingUser = input?.userId
      ? await em.getRepository(User).findOne({ id: input.userId })
      : await em
          .getRepository(User)
          .findOne({ emailAddress: caseInsensitiveMatchValue(input.emailAddress), tenant: { id: tenantId } });

    const userInfo = castToShape<User>(input, userKeysToTransform);
    let user = User.newUser({ ...userInfo, tenant });
    let existingOwner;
    if (existingUser) {
      existingUser.modify(userInfo);
      user = existingUser;
      existingOwner = await em.getRepository(Owner).findOne({ user: { emailAddress: existingUser.emailAddress } });
    }
    if (existingOwner) {
      existingOwner.modify({ ...input, user, tenant })
    } else {
      existingOwner = Owner.newOwner({ ...input, user, tenant });
    }
    const owner = existingOwner;
    this.addUserRoles(owner.user, [RoleNames.OWNER], em);

    if (links) await this.handleLinks(owner, links, em, userPaths, opportunityPaths);
    await em.persist(owner).flush();
    await em.populate(owner, [...relationPaths, ...additionalPaths] as never[]);
    return owner;
  }

  public static async updateOwner(
    em: EntityManager<IDatabaseDriver<Connection>>,
    tenantId: string,
    id: string,
    input: UpdateOwnerInput,
    relationPaths: string[],
    userPaths: string[] = [],
    opportunityPaths: string[] = [],
    links?: OwnerLinks,
  ): Promise<Owner> {
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId }, { populate: ['users'] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    const additionalPaths = [];
    if (links?.opportunities?.length) additionalPaths.push('opportunities');
    if (links?.user?.length) additionalPaths.push('user');
    const owner = await em
      .getRepository(Owner)
      .findOneOrFail({ id }, { populate: [...relationPaths, ...additionalPaths] as never });
    owner.modify(input);
    const updatedOwner = links ? await this.handleLinks(owner, links, em, userPaths, opportunityPaths) : owner;
    await em.persist(updatedOwner).flush();
    await em.populate(updatedOwner, userPaths as never[]);
    return updatedOwner;
  }

  private static async handleLinks(
    owner: Owner,
    links: OwnerLinks,
    em: EntityManager,
    userPaths: string[] = [],
    opportunityPaths: string[] = [],
  ): Promise<Owner> {
    let updatedOwner = await handleUpdateLinks<Owner, User>(em, owner, 'users', userPaths, User, links?.user);
    updatedOwner = await handleUpdateLinks<Owner, Opportunity>(
      em,
      owner,
      'opportunities',
      opportunityPaths,
      Opportunity,
      links?.opportunities,
    );
    return updatedOwner;
  }

  private static async addUserRoles(user: User, roleNames: RoleNames[], em: EntityManager): Promise<void> {
    const roles = await em.getRepository(Role).findAll();
    const roleSet = roleNames.map((roleName) => {
      const role = roles.find((role) => role.name === roleName);
      if (!role) throw Error(`Role ${roleName} not found in database`);
      return role;
    });
    user.roles.add(roleSet);
  }
}
