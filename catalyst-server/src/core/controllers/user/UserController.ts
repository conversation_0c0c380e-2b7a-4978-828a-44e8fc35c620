import { Connection, Entity<PERSON>anager, IDatabaseDriver } from '@mikro-orm/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { SubmitUserInput } from 'core/contracts/input/base/UserInput';
import { TenantController } from 'core/controllers/base/TenantController';
import { Role } from 'core/entities/Role';
import { User } from 'core/entities/User';
import { caseInsensitiveMatchValue } from 'core/storage/queryUtils';

export class UserController {
  /***
   *      __  __               __  __            __  _          __         __  ____
   *     / / / /__  ___ ___ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *    / /_/ / _ \/ _ `/ // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    \____/_//_/\_,_/\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                             /_/
   */

  /**
   * Create a new user (as an unauthorized User) via registration
   */
  public static async submitUser(
    em: EntityManager<IDatabaseDriver<Connection>>,
    input: SubmitUserInput,
    userPaths: string[] = [],
  ): Promise<User> {
    // find tenant
    const tenant = await TenantController.getTenantByHandleOrAlias(em, input.tenantHandle);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    // @TODO: registration - turning this off until we have registration
    // find verified user
    /*let user: User | null = await em.getRepository(User).findOne({
      emailAddress: caseInsensitiveMatchValue(input.emailAddress),
      tenant,
      status: VerifiedStatus.VERIFIED,
    });
    if (user) throw Error(errorKeys.USER_EXISTS_AS_VERIFIED);
    */

    // find user
    let user = await em
      .getRepository(User)
      .findOne(
        { emailAddress: caseInsensitiveMatchValue(input.emailAddress), tenant },
        { populate: userPaths as never },
      );

    if (user) {
      // allow unverifed users to update status
      if (user.status === VerifiedStatus.UNVERIFIED) {
        user.modify(input);
      }
    } else {
      user = User.newUser(input);
    }
    // All submitters are set as owners of the opportunity initially.
    this.setUserRoles(user, [RoleNames.OWNER], em);

    user.tenant = tenant;
    await em.persist(user).flush();
    await em.populate(user, userPaths as never[]);
    return user;
  }

  private static async setUserRoles(user: User, roleNames: RoleNames[], em: EntityManager): Promise<void> {
    const roles = await em.getRepository(Role).findAll();
    const roleSet = roleNames.map((roleName) => {
      const role = roles.find((role) => role.name === roleName);
      if (!role) throw Error(`Role ${roleName} not found in database`);
      return role;
    });
    user.roles.set(roleSet);
  }
}
