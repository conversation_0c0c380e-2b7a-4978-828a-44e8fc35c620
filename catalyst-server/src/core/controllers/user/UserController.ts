import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { SubmitUserInput } from 'core/contracts/input/base/UserInput';
import { TenantController } from 'core/controllers/base/TenantController';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import { caseInsensitiveMatchValue } from 'core/storage/queryUtils';

export class UserController {
  /***
   *      __  __               __  __            __  _          __         __  ____
   *     / / / /__  ___ ___ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *    / /_/ / _ \/ _ `/ // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    \____/_//_/\_,_/\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                             /_/
   */

  /**
   * Create a new user (as an unauthorized User) via registration
   */
  public static async submitUser(
    em: EntityManager<IDatabaseDriver<Connection>>,
    input: SubmitUserInput,
    userPaths: string[] = [],
  ): Promise<User> {
    // find tenant
    const tenant = await TenantController.getTenantByHandleOrAlias(em, input.tenantHandle);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    // @TODO: registration - turning this off until we have registration
    // find verified user
    /*let user: User | null = await em.getRepository(User).findOne({
      emailAddress: caseInsensitiveMatchValue(input.emailAddress),
      tenant,
      status: VerifiedStatus.VERIFIED,
    });
    if (user) throw Error(errorKeys.USER_EXISTS_AS_VERIFIED);
    */

    // find user
    let user = await em
      .getRepository(User)
      .findOne(
        { emailAddress: caseInsensitiveMatchValue(input.emailAddress), tenant },
        { populate: userPaths as never },
      );

    if (user) {
      // allow unverifed users to update status
      if (user.status === VerifiedStatus.UNVERIFIED) {
        user.modify(input);
      }
    } else {
      user = User.newUser(input);
    }
    user.tenant = tenant;
    await em.persist(user).flush();
    await em.populate(user, userPaths as never[]);
    return user;
  }
}
