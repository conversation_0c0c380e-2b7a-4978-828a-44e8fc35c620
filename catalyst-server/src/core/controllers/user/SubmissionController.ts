import { EntityManager } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { CreateSubmissionInput, NewSubmissionLinks } from 'core/contracts/input/user/SubmissionInput';
import { Opportunity } from 'core/entities/Opportunity';
import { Submission } from 'core/entities/Submission';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import { Id } from 'core/utils/Id';
import { Owner } from 'core/entities/Owner';
import { OwnershipStatus } from 'core/contracts/enums/OwnershipStatus';
import { OpportunityOwnerStatus } from 'core/entities/OpportunityOwnerStatus';

export class SubmissionController {
  public static async newSubmission(
    em: EntityManager,
    input: CreateSubmissionInput,
    links: NewSubmissionLinks,
    userPaths: string[] = [],
  ): Promise<Submission> {
    // @TODO:registration dont' do this for now until we have registration
    // make sure this is an unverified user
    /* 
    const user = await em
      .getRepository(User)
      .findOne({ id: links.userId, status: VerifiedStatus.UNVERIFIED }, { populate: userPaths as never });
    if (!user) throw Error(errorKeys.UNVERIFIED_USER_NOT_FOUND); */
    const user = await em
      .getRepository(User)
      .findOne({ id: links.userId }, { populate: [...(userPaths as never), 'tenant'] });
    if (!user) throw Error(errorKeys.OBJECT_NOT_FOUND);
    return this._createSubmission(user.tenant.id, user, userPaths, input, em);
  }

  public static async createSubmission(
    em: EntityManager,
    input: CreateSubmissionInput,
    userPaths: string[] = [],
    tenantId: Id,
    userId?: Id,
  ): Promise<Submission> {
    if (!userId) throw Error(errorKeys.UNAUTHORIZED);
    return this._createSubmission(tenantId, userId, userPaths, input, em);
  }

  private static async _createSubmission(
    tenantId: Id,
    userId: Id,
    userPaths: string[] = [],
    input: CreateSubmissionInput,
    em: EntityManager,
  ) {
    if (!tenantId) throw Error(errorKeys.UNAUTHORIZED);
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const user = await em.getRepository(User).findOne({ id: userId }, { populate: userPaths as never });
    if (!user) throw Error(errorKeys.UNAUTHORIZED);
    const submission = Submission.newSubmission(input, tenant);
    /*
      NOTE: We only have a CREATE CurationEvent if the Opportunity is directly created by the Curator
      We do not add one in this case, to indicate that the Opportunity has not yet been curated
    */
    const opportunity = Opportunity.newOpportunity(
      {
        ...input,
        org1: user.org1,
        org2: user.org2,
        org3: user.org3,
        org4: user.org4,
      },
      tenant,
    );

    const existingOwner = await em.getRepository(Owner).findOne({ user: { id: user.id } });
    const owner = existingOwner ? existingOwner : Owner.newOwner({ user, tenant });
    const opportunityOwnerStatus = OpportunityOwnerStatus.newOpportunityOwnerStatus({
      status: OwnershipStatus.INITIAL,
      owner,
    });
    opportunity.opportunityOwnerStatuses.add(opportunityOwnerStatus);

    await em.persist(opportunity);
    opportunity.submissions.add(submission);
    await em.persist(submission).flush();
    await em.populate(submission, userPaths as never[]);
    return submission;
  }
}
