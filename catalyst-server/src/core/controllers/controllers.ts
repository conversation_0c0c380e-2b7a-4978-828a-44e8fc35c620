import { AppContext } from 'core/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { Scope } from 'core/contracts/input/base/CommonInput';
import { getFilterForResourceType } from 'core/storage/queryUtils';
import { OpportunityVisibility } from 'core/contracts/enums/OpportunityVisibility';

export function isEmptyScope(scope?: Scope): boolean {
  return !!scope && scope.resources?.length === 0;
}

export async function getTenantFilterForScope(ctx: AppContext, scope?: Scope): Promise<Record<string, unknown>> {
  // if scope is not present then use the current tenant
  const filter = (scope && getFilterForResourceType(ResourceType.TENANT, scope)) || {
    id: ctx.token?.[PayloadKeys.TENANT_KEY],
  };
  return filter;
}

export async function getTenantOpportunityFilterForScope(ctx: AppContext, scope?: Scope): Promise<Record<string, unknown>> {
  if(scope) {
    const thisTenantId = ctx.token?.[PayloadKeys.TENANT_KEY] || '';
    const tenantServerConfig = ctx.serverConfigs.get(thisTenantId);
    const filterOtherPrivateOpportunities = tenantServerConfig ? tenantServerConfig.filterOtherPrivateOpportunities : true;
    const resourceIds = scope?.resources || undefined;

    if(filterOtherPrivateOpportunities && resourceIds) {
      const tenantFilter = await getTenantFilterForScope(ctx, scope);
      // Fetch all opportunities for resource tenants where visibility set to All
      return Promise.resolve({
        $and: [
          { tenant: tenantFilter },
          { visibility: { $eq: OpportunityVisibility.ALL }}
        ]
      });
    }
  }

  // Default to main tenant filter by scope
  const tenantFilter = await getTenantFilterForScope(ctx, scope);
  return Promise.resolve({ tenant: tenantFilter });
  
}
