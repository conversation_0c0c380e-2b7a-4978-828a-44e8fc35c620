import { EntityRepository } from '@mikro-orm/postgresql';
import { Report, ReportResponse } from 'core/contracts/output/ReportResponse';
import { AppContext } from 'core/core';
import { Opportunity } from 'core/entities/Opportunity';
import { Reporter } from './Reporter';
import { EntityManager, raw } from '@mikro-orm/core';
import { ReportQuery } from 'core/contracts/input/base/CommonInput';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';

/*
[
  {
    tenant: "Starship Troopers",
    missionCommandCount: 3,
    movementManeuverCount: 4,
    intelligenceCount: 4,
    firesCount: 3,
    sustainmentCount: 3,
    forceProtectionCount: 3,
    noneCount: 0,
  },
  {
    tenant: "The Monuments Men",
    missionCommandCount: 4,
    movementManeuverCount: 4,
    intelligenceCount: 3,
    firesCount: 3,
    sustainmentCount: 3,
    forceProtectionCount: 3,
    noneCount: 0,
  },
  {
    tenant: "Total",
    missionCommandCount: 7,
    movementManeuverCount: 8,
    intelligenceCount: 7,
    firesCount: 6,
    sustainmentCount: 6,
    forceProtectionCount: 6,
    noneCount: 0,
  },
]
*/

interface OpportunityWFFReportResult {
  tenant: string;
  missionCommandCount: number;
  movementManeuverCount: number;
  intelligenceCount: number;
  firesCount: number;
  sustainmentCount: number;
  forceProtectionCount: number;
  noneCount: number;
}

class OpportunityWFFReporter implements Reporter {
  async runReport({
    em,
    filter,
    query,
  }: {
    em: EntityManager;
    filter: Record<string, unknown>;
    query: ReportQuery;
  }): Promise<Report> {
    const qb = ((<unknown>em.getRepository(Opportunity)) as EntityRepository<Opportunity>)
      .qb('o')
      .select([
        't.label as tenant',
        raw(`CAST(COUNT(CASE WHEN o.function = 'Mission Command' THEN 1 END) AS INTEGER) as "missionCommandCount"`),
        raw(
          `CAST(COUNT(CASE WHEN o.function = 'Movement and Maneuver' THEN 1 END) AS INTEGER) as "movementManeuverCount"`,
        ),
        raw(`CAST(COUNT(CASE WHEN o.function = 'Intelligence' THEN 1 END) AS INTEGER) as "intelligenceCount"`),
        raw(`CAST(COUNT(CASE WHEN o.function = 'Fires' THEN 1 END) AS INTEGER) as "firesCount"`),
        raw(`CAST(COUNT(CASE WHEN o.function = 'Sustainment' THEN 1 END) AS INTEGER) as "sustainmentCount"`),
        raw(`CAST(COUNT(CASE WHEN o.function = 'Force Protection' THEN 1 END) AS INTEGER) as "forceProtectionCount"`),
        raw(`CAST(COUNT(CASE WHEN o.function = null THEN 1 END) AS INTEGER) as "noneCount"`),
      ])
      .where({ $and: [filter, { status: { $ne: OpportunityStatus.DELETED } }] })
      .join('o.tenant', 't')
      .groupBy('t.label')
      .orderBy({ 't.label': 'ASC' });

    const results = (await qb.execute('all')) as OpportunityWFFReportResult[];

    const totals = results.reduce(
      (acc: OpportunityWFFReportResult, row: OpportunityWFFReportResult) => {
        acc.missionCommandCount += row.missionCommandCount;
        acc.movementManeuverCount += row.movementManeuverCount;
        acc.intelligenceCount += row.intelligenceCount;
        acc.firesCount += row.firesCount;
        acc.sustainmentCount += row.sustainmentCount;
        acc.forceProtectionCount += row.forceProtectionCount;
        acc.noneCount += row.noneCount;
        return acc;
      },
      {
        tenant: 'Total',
        missionCommandCount: 0,
        movementManeuverCount: 0,
        intelligenceCount: 0,
        firesCount: 0,
        sustainmentCount: 0,
        forceProtectionCount: 0,
        noneCount: 0,
      },
    );

    return { label: query.label || query.reportName, name: query.reportName, data: [...results, ...[totals]] };
  }
}

export const reporter = new OpportunityWFFReporter();
