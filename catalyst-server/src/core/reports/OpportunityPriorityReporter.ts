import { EntityRepository } from '@mikro-orm/postgresql';
import { Report, ReportResponse } from 'core/contracts/output/ReportResponse';
import { AppContext } from 'core/core';
import { Opportunity } from 'core/entities/Opportunity';
import { Reporter } from './Reporter';
import { EntityManager, raw } from '@mikro-orm/core';
import { ReportQuery } from 'core/contracts/input/base/CommonInput';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';

/*
[
  {
    tenant: "Starship Troopers",
    highPriorityCount: 5,
    mediumPriorityCount: 5,
    lowPriorityCount: 5,
    noPriorityCount: 5,
  },
  {
    tenant: "The Monuments Men",
    highPriorityCount: 5,
    mediumPriorityCount: 5,
    lowPriorityCount: 5,
    noPriorityCount: 5,
  },
  {
    tenant: "Total",
    highPriorityCount: 10,
    mediumPriorityCount: 10,
    lowPriorityCount: 10,
    noPriorityCount: 10,
  },
]
*/

interface OpportunityPriorityReportResult {
  tenantName: string;
  highPriorityCount: number;
  mediumPriorityCount: number;
  lowPriorityCount: number;
  noPriorityCount: number;
}

class OpportunityPriorityReporter implements Reporter {
  async runReport({
    em,
    filter,
    query,
  }: {
    em: EntityManager;
    filter: Record<string, unknown>;
    query: ReportQuery;
  }): Promise<Report> {
    const qb = ((<unknown>em.getRepository(Opportunity)) as EntityRepository<Opportunity>).qb('o');
    qb.select([
      't.label as tenant',
      raw(`CAST(COUNT(CASE WHEN o.priority = 3 THEN 1 END) AS INTEGER) as "highPriorityCount"`),
      raw(`CAST(COUNT(CASE WHEN o.priority = 2 THEN 1 END) AS INTEGER) as "mediumPriorityCount"`),
      raw(`CAST(COUNT(CASE WHEN o.priority = 1 THEN 1 END) AS INTEGER) as "lowPriorityCount"`),
      raw(`CAST(COUNT(CASE WHEN o.priority = 0 THEN 1 END) AS INTEGER) as "noPriorityCount"`),
    ])
      .where({ $and: [filter, { status: { $ne: OpportunityStatus.DELETED } }] })
      .join('o.tenant', 't')
      .groupBy('t.label')
      .orderBy({ 't.label': 'ASC' });

    const results = await qb.execute('all');

    const totals = results.reduce(
      (acc: OpportunityPriorityReportResult, row: OpportunityPriorityReportResult) => {
        acc.highPriorityCount += row.highPriorityCount;
        acc.mediumPriorityCount += row.mediumPriorityCount;
        acc.lowPriorityCount += row.lowPriorityCount;
        acc.noPriorityCount += row.noPriorityCount;
        return acc;
      },
      { tenant: 'Total', highPriorityCount: 0, mediumPriorityCount: 0, lowPriorityCount: 0, noPriorityCount: 0 },
    );

    return { label: query.label || query.reportName, name: query.reportName, data: [...results, ...[totals]] };
  }
}

export const reporter = new OpportunityPriorityReporter();
