import { EntityRepository } from '@mikro-orm/postgresql';
import { Report } from 'core/contracts/output/ReportResponse';
import { Reporter } from './Reporter';
import { EntityManager, raw } from '@mikro-orm/core';
import { ReportQuery } from 'core/contracts/input/base/CommonInput';
import { Submission } from 'core/entities/Submission';
/*
{
  tenantSubmissionResults: [
    {
      tenant: "Starship Troopers",
      year: "2023",
      submissionCount: 13,
    },
    {
      tenant: "The Monuments Men",
      year: "2023",
      submissionCount: 15,
    },
    {
      tenant: "Starship Troopers",
      year: "2024",
      submissionCount: 7,
    },
    {
      tenant: "The Monuments Men",
      year: "2024",
      submissionCount: 5,
    },
  ],
  totals: [
    {
      year: "2023",
      submissionCount: 28,
    },
    {
      year: "2024",
      submissionCount: 12,
    },
  ],
}
*/

interface TenantSubmissionByYearReportResult {
  tenantSubmissionResults: TenantSubmissionResult[];
  totals: TenantSubmissionResult[];
}

interface TenantSubmissionResult {
  tenant?: string;
  year: string;
  submissionCount: number;
}

class OpportunityTenantSubmissionByYearReporter implements Reporter {
  async runReport({
    em,
    filter,
    query,
  }: {
    em: EntityManager;
    filter: Record<string, unknown>;
    query: ReportQuery;
  }): Promise<Report> {
    const qb = ((<unknown>em.getRepository(Submission)) as EntityRepository<Submission>).qb('s');

    const yearFormatStr = `to_char(s.created_at, 'YYYY')`;

    qb.select([
      't.label as tenant',
      raw(`${yearFormatStr} as year`),
      raw('CAST(COUNT(*) as INTEGER) as "submissionCount"'),
    ])
      .where(filter)
      .join('s.tenant', 't')
      .groupBy([raw(yearFormatStr), 't.label'])
      .orderBy([{ [raw(yearFormatStr)]: 'ASC' }, { 't.label': 'ASC' }]);

    const results = await qb.execute('all');

    const totals = results.reduce((acc: TenantSubmissionResult[], row: TenantSubmissionResult) => {
      const existing = acc.find((r) => r.year === row.year);
      if (existing) {
        existing.submissionCount += row.submissionCount;
      } else {
        acc.push({ year: row.year, submissionCount: row.submissionCount });
      }
      return acc;
    }, []);

    const result = { tenantSubmissionResults: results, totals } as TenantSubmissionByYearReportResult;

    return { label: query.label || query.reportName, name: query.reportName, data: result };
  }
}

export const reporter = new OpportunityTenantSubmissionByYearReporter();
