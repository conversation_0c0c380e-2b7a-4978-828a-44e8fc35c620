import { EntityManager, EntityRepository, raw } from '@mikro-orm/postgresql';
import { Report, ReportResponse } from 'core/contracts/output/ReportResponse';
import { AppContext } from 'core/core';
import { Opportunity } from 'core/entities/Opportunity';
import { Reporter } from './Reporter';
import { ReportQuery } from 'core/contracts/input/base/CommonInput';
import { Tenant } from 'core/entities/Tenant';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';

/*
   example result
   [
  {
    tenantName: "Starship Troopers",
    approvedCount: 6,
    pendingCount: 7,
    archivedCount: 7,
  },
  {
    tenantName: "The Monuments Men",
    approvedCount: 7,
    pendingCount: 6,
    archivedCount: 7,
  },
  {
    tenantName: "Total",
    approvedCount: 13,
    pendingCount: 13,
    archivedCount: 14,
  },
]
*/

interface OpportunityStatusReportResult {
  tenant: string;
  approvedCount: number;
  pendingCount: number;
  archivedCount: number;
}

class OpportunityStatusReporter implements Reporter {
  async runReport({
    em,
    filter,
    query,
  }: {
    em: EntityManager;
    filter: Record<string, unknown>;
    query: ReportQuery;
  }): Promise<Report> {
    const qb = ((<unknown>em.getRepository(Opportunity)) as EntityRepository<Opportunity>).qb('o');
    qb.select([
      't.label as tenant',
      raw(`CAST(COUNT(CASE WHEN o.status = 'Approved' THEN 1 END) AS INTEGER) as "approvedCount"`),
      raw(`CAST(COUNT(CASE WHEN o.status = 'Pending' THEN 1 END) AS INTEGER) as "pendingCount"`),
      raw(`CAST(COUNT(CASE WHEN o.status = 'Archived' THEN 1 END) AS INTEGER) as "archivedCount"`),
    ])
      .where({ $and: [filter, { status: { $ne: OpportunityStatus.DELETED } }] })
      .join('o.tenant', 't')
      .groupBy('t.label')
      .orderBy({ 't.label': 'ASC' });

    const results = await qb.execute('all');

    const totals = results.reduce(
      (acc: OpportunityStatusReportResult, row: OpportunityStatusReportResult) => {
        acc.approvedCount += row.approvedCount;
        acc.pendingCount += row.pendingCount;
        acc.archivedCount += row.archivedCount;
        return acc;
      },
      { tenant: 'Total', approvedCount: 0, pendingCount: 0, archivedCount: 0 },
    );

    return { label: query.label || query.reportName, name: query.reportName, data: [...results, ...[totals]] };
  }
}

// possible future queries

// Group by month and tenant
/*
const result = await qb.select([
  'date_trunc(\'month\', o.created_at) as month_created', 
  'o.tenant_id',
  'COUNT(CASE WHEN o.status = \'Approved\' THEN 1 END) as approvedCount',
  'COUNT(CASE WHEN o.status = \'Pending\' THEN 1 END) as pendingCount'
])
.groupBy(['month_created', 'o.tenant_id']) // Group by both month and tenant
.orderBy(['month_created', 'o.tenant_id']) // Order by month and tenant
.execute();
*/

// Sub select query
/*
const baseQB = ((<unknown>em.getRepository(Opportunity)) as EntityRepository<Opportunity>).qb('e').where(filter);
    const qb = ((<unknown>em.getRepository(Opportunity)) as EntityRepository<Opportunity>)
      .qb()
      .select(
        [
          'tenant_id',
          baseQB.clone().select(['COUNT(*)']).andWhere({ status: 'Approved' }).as('approvedCount'),
          baseQB.clone().select(['COUNT(*)']).andWhere({ status: 'Pending' }).as('pendingCount'),
          baseQB.clone().select(['COUNT(*)']).andWhere({ status: 'Archived' }).as('archivedCount')
        ]
      )
*/
// WITH AS query 'in drafts'

export const reporter = new OpportunityStatusReporter();
