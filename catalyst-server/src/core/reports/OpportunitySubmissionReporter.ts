import { EntityManager, EntityRepository, raw } from '@mikro-orm/postgresql';
import { Report, ReportResponse } from 'core/contracts/output/ReportResponse';
import { AppContext } from 'core/core';
import { Opportunity } from 'core/entities/Opportunity';
import { Reporter } from './Reporter';
import { ReportQuery } from 'core/contracts/input/base/CommonInput';
import { Tenant } from 'core/entities/Tenant';
import { Submission } from 'core/entities/Submission';
import { CurationEvent } from 'core/entities/CurationEvent';

/*
{
  totalSubmissions: 40,
  totalSubmitters: 10,
  uncuratedOpportunities: 40,
}
*/

interface OpportunitySubmissionReportResult {
  totalSubmissions: number;
  totalSubmitters: number;
  uncuratedOpportunities: number;
}

class OpportunitySubmissionReporter implements Reporter {
  async runReport({
    em,
    filter,
    query,
  }: {
    em: EntityManager;
    filter: Record<string, unknown>;
    query: ReportQuery;
  }): Promise<Report> {
    const qb = ((<unknown>em.getRepository(Submission)) as EntityRepository<Submission>).qb('s');
    qb.select([
      raw(`CAST(COUNT(*) AS INTEGER) AS "totalSubmissions"`),
      raw(`CAST(COUNT(DISTINCT s.user_id) AS INTEGER) AS "totalSubmitters"`),
    ]).where(filter);

    const results = await qb.execute('all');

    const qb1 = ((<unknown>em.getRepository(Opportunity)) as EntityRepository<Opportunity>).qb('o');
    qb1
      .select([raw(`CAST(COUNT(*) AS INTEGER) AS "uncuratedOpportunities"`)])
      .where({ $and: [{ lastCurated: { $eq: null } }, filter] });

    const results1 = await qb1.execute('all');

    return {
      label: query.label || query.reportName,
      name: query.reportName,
      data: { ...results?.[0], ...results1?.[0] } as OpportunitySubmissionReportResult,
    };
  }
}

export const reporter = new OpportunitySubmissionReporter();
