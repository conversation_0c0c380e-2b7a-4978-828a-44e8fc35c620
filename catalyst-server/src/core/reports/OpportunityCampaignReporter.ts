import { EntityRepository } from '@mikro-orm/postgresql';
import { Report, ReportResponse } from 'core/contracts/output/ReportResponse';
import { AppContext } from 'core/core';
import { Opportunity } from 'core/entities/Opportunity';
import { Reporter } from './Reporter';
import { EntityManager, raw } from '@mikro-orm/core';
import { ReportQuery } from 'core/contracts/input/base/CommonInput';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';

/*
{
  tenantCampaignResults: [
    {
      campaign: "Bonaroo 2023",
      tenant: "Starship Troopers",
      campaignCount: 7,
    },
    {
      campaign: "Southeast Fishing Championship",
      tenant: "Starship Troopers",
      campaignCount: 7,
    },
    {
      campaign: "Bonaroo 2023",
      tenant: "The Monuments Men",
      campaignCount: 7,
    },
    {
      campaign: "Southeast Fishing Championship",
      tenant: "The Monuments Men",
      campaignCount: 6,
    },
  ],
  totals: [
    {
      campaign: "Bonaroo 2023",
      campaignCount: 14,
    },
    {
      campaign: "Southeast Fishing Championship",
      campaignCount: 13,
    },
  ],
}
*/

interface OpportunityCampaignReportResult {
  tenantCampaignResults: TenantCampaignResult[];
  totals: TenantCampaignResult[];
}

interface TenantCampaignResult {
  tenant?: string;
  campaign: string;
  campaignCount: number;
}

class OpportunityCampaignReporter implements Reporter {
  async runReport({
    em,
    filter,
    query,
  }: {
    em: EntityManager;
    filter: Record<string, unknown>;
    query: ReportQuery;
  }): Promise<Report> {
    const qb = ((<unknown>em.getRepository(Opportunity)) as EntityRepository<Opportunity>).qb('o');
    qb.select(['t.label as tenant', 'o.campaign', raw(`CAST(COUNT(*) AS INTEGER) as "campaignCount"`)])
      .where({ $and: [{ campaign: { $ne: null } }, filter, { status: { $ne: OpportunityStatus.DELETED } }] })
      .join('o.tenant', 't')
      .groupBy(['t.label', 'o.campaign'])
      .orderBy([{ 't.label': 'ASC' }, { 'o.campaign': 'ASC' }]);

    const results = await qb.execute('all');

    const totals = results.reduce((acc: TenantCampaignResult[], row: TenantCampaignResult) => {
      const existing = acc.find((r) => r.campaign === row.campaign);
      if (existing) {
        existing.campaignCount += row.campaignCount;
      } else {
        acc.push({ campaign: row.campaign, campaignCount: row.campaignCount });
      }
      return acc;
    }, []);

    const result = { tenantCampaignResults: results, totals };

    return { label: query.label || query.reportName, name: query.reportName, data: result };
  }
}

export const reporter = new OpportunityCampaignReporter();
