import { EntityRepository } from '@mikro-orm/postgresql';
import { Report } from 'core/contracts/output/ReportResponse';
import { Reporter } from './Reporter';
import { EntityManager, raw } from '@mikro-orm/core';
import { ReportQuery } from 'core/contracts/input/base/CommonInput';
import { Submission } from 'core/entities/Submission';
import { getMonthsArrayFromDate } from 'core/utils/dates';

/*
{
    tenantSubmissionResults: [
      {
        tenant: "Starship Troopers",
        month: "01-2024",
        submissionCount: 2,
      },
      {
        tenant: "The Monuments Men",
        month: "01-2024",
        submissionCount: 3,
      },
      {
        tenant: "The Monuments Men",
        month: "02-2024",
        submissionCount: 2,
      },
      {
        tenant: "Starship Troopers",
        month: "03-2024",
        submissionCount: 2,
      },

      ...........

    ],
    totals: [
      {
        month: "01-2024",
        submissionCount: 5,
      },
      {
        month: "02-2024",
        submissionCount: 2,
      },
      {
        month: "03-2024",
        submissionCount: 3,
      },

      ...........

    ],
  },
}
*/

interface TenantSubmissionReportResult {
  tenantSubmissionResults: TenantSubmissionResult[];
  totals: TenantSubmissionResult[];
}

interface TenantSubmissionResult {
  tenant?: string;
  month: string;
  submissionCount: number;
}

class OpportunityTenantSubmissionReporter implements Reporter {
  async runReport({
    em,
    filter,
    query,
  }: {
    em: EntityManager;
    filter: Record<string, unknown>;
    query: ReportQuery;
  }): Promise<Report> {
    const qb = ((<unknown>em.getRepository(Submission)) as EntityRepository<Submission>).qb('s');
    const monthFormatStr = `to_char(date_trunc('month', s.created_at), 'MM-YYYY')`;

    qb.select([
      't.label as tenant',
      raw(`${monthFormatStr} as month`),
      raw('CAST(COUNT(*) as INTEGER) as "submissionCount"'),
    ])
      .where(filter)
      .join('s.tenant', 't')
      .groupBy([raw(monthFormatStr), 't.label'])
      .orderBy([{ [raw(monthFormatStr)]: 'ASC' }, { 't.label': 'ASC' }]);

    //get list of all unique tenants within filter

    const results = await qb.execute('all');

    const startDate = new Date();
    startDate.setFullYear(startDate.getFullYear() - 1);
    startDate.setDate(1);

    const currentDate = new Date(startDate);

    const tenants = results
      .map((r: TenantSubmissionResult) => r.tenant)
      .filter((v: string, i: number, a: string) => a.indexOf(v) === i);

    const totals = results.reduce((acc: TenantSubmissionResult[], row: TenantSubmissionResult) => {
      const existing = acc.find((r) => r.month === row.month);
      if (existing) {
        existing.submissionCount += row.submissionCount;
      } else {
        acc.push({ month: row.month, submissionCount: row.submissionCount });
      }
      return acc;
    }, []);

    //if a month is missing from the totals, add it with a submission count of 0
    const months: string[] = getMonthsArrayFromDate(currentDate);
    months.forEach((month) => {
      if (!totals.find((r: TenantSubmissionResult) => r.month === month)) {
        totals.push({ month, submissionCount: 0 });
      }
    });

    //if a tenant does not have a submission for a month, add it with a submission count of 0
    tenants.forEach((tenant: string) => {
      const tenantResults = results.filter((r: TenantSubmissionResult) => r.tenant === tenant);
      const tenantMonths = tenantResults.map((r: TenantSubmissionResult) => r.month);
      months.forEach((month: string) => {
        // if there is a tenantMonth missing from months, add it with a submission count of 0
        if (!tenantMonths.includes(month)) {
          results.push({ tenant, month, submissionCount: 0 });
        }
      });
    });

    const result = { tenantSubmissionResults: results, totals } as TenantSubmissionReportResult;

    return { label: query.label || query.reportName, name: query.reportName, data: result };
  }
}

export const reporter = new OpportunityTenantSubmissionReporter();
