import { reporter as oppStatusReporter } from './OpportunityStatusReporter';
import { reporter as oppPriorityReporter } from './OpportunityPriorityReporter';
import { reporter as oppWFFReporter } from './OpportunityWFFReporter';
import { reporter as oppCampaignReporter } from './OpportunityCampaignReporter';
import { reporter as oppSubmissionReporter } from './OpportunitySubmissionReporter';
import { reporter as oppTenantSubmissionReporter } from './OpportunityTenantSubmissionReporter';
import { reporter as oppTenantSubmissionByYearReporter } from './OpportunityTenantSubmissionByYearReporter';
import { Reporter } from './Reporter';

const reporters: { [key: string]: Reporter } = {
  oppStatus: oppStatusReporter,
  oppPriority: oppPriorityReporter,
  oppWFF: oppWFFReporter,
  oppCampaign: oppCampaignReporter,
  oppSubmission: oppSubmissionReporter,
  oppTenantSubmission: oppTenantSubmissionReporter,
  oppTenantSubmissionByYear: oppTenantSubmissionByYearReporter,
};

export class ReporterAccessor {
  static getReporter(name: string): Reporter {
    return reporters[name];
  }
}
