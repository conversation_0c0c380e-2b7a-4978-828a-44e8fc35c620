/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { CreateTenantInput } from 'core/contracts/input/base/TenantInput';
import { Tenant } from 'core/entities/Tenant';
import {
  addPrivilegeGroupsToAllVerifiedUsers,
  addPrivilegeGroupsToUser,
  addRolesToAllVerifiedUsers,
  addRolesToUser,
  addToTenantPrivilegeGroup,
  bootstrapDb,
  createMigration,
  createNewTenant,
  createSchema,
  createTenantAdmin,
  createTenantAlias,
  createTenantPrivilegeGroup,
  deleteTenant,
  dumpOpportunities,
  removeFromTenantPrivilegeGroup,
  removePrivilegeGroupsFromAll,
  removePrivilegeGroupsFromUser,
  removeRolesFromAll,
  removeRolesFromUser,
  runPendingMigrations,
  updatePassword,
  updateTenantMeta,
  updateAllTenantConfigs,
  updateAllTenantContent,
  runDownMigrations,
  createCleanLocalDbDump
} from './databaseUtils';

const performOp = async (args: string[]) => {
  const application = new Application();
  await application.init();
  const orm = application.orm!;
  const em = orm.em.fork();
  try {
    switch (args[0]) {
      case 'create-schema':
        await createSchema(orm!);
        console.log('Database created successfully');
        break;
      case 'bootstrap':
        await bootstrapDb(em);
        console.log('Database bootstrapped successfully');
        break;
      case 'tenant':
        if (args.length < 5) {
          console.log('tenant <name> <handle> <admin_pass> <configFilePath> <themeFilePath>');
          process.exit(1);
        }
        const label = args.length > 6 ? args[6] : args[1];
        const input: {
          em: EntityManager<IDatabaseDriver<Connection>>;
          tenantInput: CreateTenantInput;
          adminPass: string;
          configFilePath?: string;
          themeFilePath?: string;
        } = {
          em,
          tenantInput: { name: args[1], handle: args[2], label },
          adminPass: args[3],
        };
        if (args.length > 3) input.configFilePath = args[4];
        if (args.length > 4) input.themeFilePath = args[5];
        const { tenant } = await createNewTenant(input);
        console.log(`Tenant ${tenant.name} (${tenant.handle}) created`);
        break;
      case 'tenant-admin':
        if (args.length < 3) {
          console.log('tenant-admin <handle> <admin_pass>');
          process.exit(1);
        }
        await createTenantAdmin({ em, tenantHandle: args[1], adminPass: args[2] });
        console.log(`Tenant admin ${args[2]} create for tenant ${args[1]}`);
        break;
      case 'tenant-alias':
        if (args.length < 3) {
          console.log('tenant-alias <existingHandle> <alias> <name>');
          process.exit(1);
        }
        await createTenantAlias(em, args[1], { handle: args[2], name: args[3] });
        console.log(`Tenant alias ${args[2]} added to tenant ${args[1]}`);
        break;
      case 'delete-tenant':
        if (args.length < 2) {
          console.log('delete-tenant <tenant-id>');
          process.exit(1);
        }
        await deleteTenant(em, args[1]);
        console.log(`Tenant ${args[1]} deleted`);
        break;
      case 'update-config':
        if (args.length < 2) {
          console.log('update-config <tenant_handle> <configFilePath> <updateHandleOrAlias>');
          process.exit(1);
        }
        await updateTenantMeta({ em, tenantHandle: args[1], configFilePath: args[2], updateHandleOrAlias: args[3] });
        console.log(`Tenant config for ${args[1]} ${args[3] || ''} updated`);
        break;
      case 'update-theme':
        if (args.length < 2) {
          console.log('update-theme <tenant_handle> <themeFilePath> <updateHandleOrAlias>');
          process.exit(1);
        }
        await updateTenantMeta({ em, tenantHandle: args[1], themeFilePath: args[2], updateHandleOrAlias: args[3] });
        console.log(`Tenant theme for ${args[1]} ${args[3] || ''} updated`);
        break;
      case 'update-content':
        if (args.length < 2) {
          console.log('update-content <tenant_handle> <contentFilePath> <updateHandleOrAlias>');
          process.exit(1);
        }
        await updateTenantMeta({ em, tenantHandle: args[1], contentFilePath: args[2], updateHandleOrAlias: args[3] });
        console.log(`Tenant content for ${args[1]} ${args[3] || ''} updated`);
        break;
      case 'update-serverConfig':
        if (args.length < 2) {
          console.log('update-content <tenant_handle> <serverConfigFilePath> <updateHandleOrAlias>');
          process.exit(1);
        }
        await updateTenantMeta({ em, tenantHandle: args[1], serverFilePath: args[2], updateHandleOrAlias: args[3] });
        console.log(`Tenant system config for ${args[1]} ${args[3] || ''} updated`);
        break;
      case 'tenant-privilege-group':
        if (args.length < 4) {
          console.log('tenant-privilege-group <tenant_handle> <name> <privilege_tenant_handle> ...');
          process.exit(1);
        }
        const privilegeGroup = await createTenantPrivilegeGroup({
          em,
          tenantHandle: args[1],
          name: args[2],
          privilegeTenantHandles: args.slice(3),
        });
        console.log(`Tenant Privilege Group ${args[2]} for ${args[1]} created with id: ${privilegeGroup.id}`);
        break;
      case 'add-to-tenant-privilege-group':
        if (args.length < 4) {
          console.log('add-to-tenant-privilege-group <tenant_handle> <group_id> <privilege_tenant_handle> ...');
          process.exit(1);
        }
        const updatePrivilegeGroup = await addToTenantPrivilegeGroup({
          em,
          tenantHandle: args[1],
          groupId: args[2],
          privilegeTenantHandles: args.slice(3),
        });
        console.log(`Tenant Privilege Group ${args[2]} for ${args[1]}: ${updatePrivilegeGroup.id} updated`);
        break;
      case 'add-to-tenant-privilege-group-by-name':
          if (args.length < 4) {
            console.log('add-to-tenant-privilege-group <tenant_handle> <group_id> <privilege_tenant_handle> ...');
            process.exit(1);
          }
          const updatedPrivilegeGroup = await addToTenantPrivilegeGroup({
            em,
            tenantHandle: args[1],
            name: args[2],
            privilegeTenantHandles: args.slice(3),
          });
          console.log(`Tenant Privilege Group ${args[2]} for ${args[1]}: ${updatedPrivilegeGroup.id} updated`);
          break;
      case 'remove-from-tenant-privilege-group':
        if (args.length < 4) {
          console.log('remove-from-tenant-privilege-group <tenant_handle> <group_id> <privilege_tenant_handle> ...');
          process.exit(1);
        }
        await removeFromTenantPrivilegeGroup({
          em,
          tenantHandle: args[1],
          groupId: args[2],
          privilegeTenantHandles: args.slice(3),
        });
        console.log(`Tenant Privilege Group ${args[2]} for ${args[1]} updated`);
        break;
      case 'update-password':
        if (args.length < 4) {
          console.log('update-password <tenant_handle> <emailAddress> <newPassword>');
          process.exit(1);
        }
        await updatePassword({ em, tenantHandle: args[1], emailAddress: args[2], newPassword: args[3] });
        console.log(`Password for ${args[1]} ${args[2]} updated`);
        break;
      case 'add-roles-to-user':
        if (args.length < 4) {
          console.log('add-roles-to-user <tenant_handle> <emailAddress> <role_name> ...');
          process.exit(1);
        }
        await addRolesToUser({ em, tenantHandle: args[1], emailAddress: args[2], roleNames: args.slice(3) });
        console.log(`Roles for ${args[1]} ${args[2]} updated`);
        break;
      case 'remove-roles-from-user':
        if (args.length < 4) {
          console.log('remove-roles-from-user <tenant_handle> <emailAddress> <role_name> ...');
          process.exit(1);
        }
        await removeRolesFromUser({ em, tenantHandle: args[1], emailAddress: args[2], roleNames: args.slice(3) });
        console.log(`Roles for ${args[1]} ${args[2]} updated`);
        break;
      case 'add-roles-to-all':
        if (args.length < 3) {
          console.log('add-roles-to-all <tenant_handle> <role_name> ...');
          process.exit(1);
        }
        await addRolesToAllVerifiedUsers({ em, tenantHandle: args[1], roleNames: args.slice(2) });
        console.log(`Roles for all ${args[1]} users updated`);
        break;
      case 'remove-roles-from-all':
        if (args.length < 3) {
          console.log('remove-roles-from-all <tenant_handle> <role_name> ...');
          process.exit(1);
        }
        await removeRolesFromAll({ em, tenantHandle: args[1], roleNames: args.slice(2) });
        console.log(`Roles for all ${args[1]} users updated`);
        break;
      case 'add-privilege-groups-to-user':
        if (args.length < 4) {
          console.log('add-privilege-groups-to-user <tenant_handle> <emailAddress> <group_id> ...');
          process.exit(1);
        }
        await addPrivilegeGroupsToUser({ em, tenantHandle: args[1], emailAddress: args[2], groupIds: args.slice(3) });
        console.log(`Privilege Groups for ${args[1]} ${args[2]} updated`);
        break;
      case 'remove-privilege-groups-from-user':
        if (args.length < 4) {
          console.log('remove-privilege-groups-from-user <tenant_handle> <emailAddress> <group_id> ...');
          process.exit(1);
        }
        await removePrivilegeGroupsFromUser({
          em,
          tenantHandle: args[1],
          emailAddress: args[2],
          groupIds: args.slice(3),
        });
        console.log(`Privilege Groups for ${args[1]} ${args[2]} updated`);
        break;
      case 'add-privilege-groups-to-all':
        if (args.length < 3) {
          console.log('add-privilege-groups-to-all <tenant_handle> <group_id> ...');
          process.exit(1);
        }
        await addPrivilegeGroupsToAllVerifiedUsers({ em, tenantHandle: args[1], groupIds: args.slice(2) });
        console.log(`Privilege Groups for all ${args[1]} users updated`);
        break;
      case 'remove-privilege-groups-from-all':
        if (args.length < 3) {
          console.log('remove-privilege-groups-from-all <tenant_handle> <group_id> ...');
          process.exit(1);
        }
        await removePrivilegeGroupsFromAll({ em, tenantHandle: args[1], groupIds: args.slice(2) });
        console.log(`PrivilegeGroups for all ${args[1]} users updated`);
        break;
      case 'create-migrations':
        const filename = await createMigration(orm);
        console.log(filename ? `${filename} created in migrations directory` : 'No changes detected');
        break;
      case 'run-migrations':
        await runPendingMigrations(orm);
        console.log(`Migrations applied successfully`);
        break;
      case 'down-migrations':
        const migrationScripts = args.slice(1).map((arg) => arg);
        if(migrationScripts.length > 0) console.log(`Migrating down the following scripts: ${migrationScripts.toString()}`);
        await runDownMigrations(orm, migrationScripts);
        console.log(`Migrate down applied successfully`);
        break;
      case 'dump-ops':
        if (args.length < 3) {
          console.log('dump-ops <filepath>');
          process.exit(1);
        }
        await dumpOpportunities({ em, filepath: args[1], tenantIds: args.slice(2) });
        console.log(`Opportunities written to ${args[1]}`);
        break;
      case 'update-all-tenant-configs':
        if (args.length < 1) {
          console.log(`You must supply the path to the parent folder location of the tenant config files. ie: /config/tenants/`);
          process.exit(1);
        }
        await updateAllTenantConfigs({ em, parentTenantConfigPath: args[1] });
        console.log(`Tenant themes and configs have been updated: ${args[1]}`);
        break;
      case 'update-all-tenant-content':
        if (args.length < 1) {
          console.log(`You must supply the path to the config folder. ie: /config`);
          process.exit(1);
        }
        await updateAllTenantContent({ em, configDir: args[1] });
        console.log(`Tenant content has been updated: ${args[1]}`);
        break;
      case 'create-local-db-dump':
        if (args.length < 1) {
          console.log(`You must supply the path to the config folder. ie: /config`);
          process.exit(1);
        }
        createCleanLocalDbDump({ outputPath: args[1], fileName: args[2]})
        break;
    }
    await em.flush();
  } catch (e) {
    console.error(e);
  }
  await application.orm?.close();
};

(async () => {
  try {
    const args = process.argv.slice(2);
    if (
      args.length &&
      (args[0] == 'create-schema' ||
        args[0] === 'bootstrap' ||
        args[0] === 'tenant' ||
        args[0] === 'tenant-admin' ||
        args[0] === 'tenant-alias' ||
        args[0] === 'delete-tenant' ||
        args[0] === 'update-config' ||
        args[0] === 'update-theme' ||
        args[0] === 'update-content' ||
        args[0] === 'tenant-privilege-group' ||
        args[0] === 'add-to-tenant-privilege-group' ||
        args[0] === 'add-to-tenant-privilege-group-by-name' ||
        args[0] === 'remove-from-tenant-privilege-group' ||
        args[0] === 'update-password' ||
        args[0] === 'add-roles-to-user' ||
        args[0] === 'remove-roles-from-user' ||
        args[0] === 'add-roles-to-all' ||
        args[0] === 'remove-roles-from-all' ||
        args[0] === 'add-privilege-groups-to-user' ||
        args[0] === 'remove-privilege-groups-from-user' ||
        args[0] === 'add-privilege-groups-to-all' ||
        args[0] === 'remove-privilege-groups-from-all' ||
        args[0] === 'create-migrations' ||
        args[0] === 'run-migrations' ||
        args[0] === 'down-migrations' ||
        args[0] === 'dump-ops' ||
        args[0] === 'update-all-tenant-configs' ||
        args[0] === 'update-all-tenant-content' ||
        args[0] === 'update-serverConfig' ||
        args[0] === 'create-local-db-dump'
      )
    ) {
      await performOp(args);
    } else {
      console.log('Valid actions are:');
      console.log('  create-schema ');
      console.log('  bootstrap ');
      console.log('  tenant <name> <handle> <admin_pass> <configFilePath> <themeFilePath> (label)');
      console.log('  tenant-admin <handle> <admin_pass>');
      console.log('  tenant-alias <existingHandle> <alias> <name>');
      console.log('  delete-tenant <tenant-id>');
      console.log('  update-config <tenant_handle> <configFilePath> <updateHandleOrAlias>');
      console.log('  update-theme <tenant_handle> <themeFilePath> <updateHandleOrAlias>');
      console.log('  update-content <tenant_handle> <contentFilePath> <updateHandleOrAlias>');
      console.log('  update-serverConfig <tenant_handle> <serverConfigFilePath> <updateHandleOrAlias>');
      console.log('  tenant-privilege-group <tenant_handle> <name> <privilege_tenant_handle> ...');
      console.log('  add-to-tenant-privilege-group <tenant_handle> <group_id> <privilege_tenant_handle> ...');
      console.log('  add-to-tenant-privilege-group-by-name <tenant_handle> <name> <privilege_tenant_handle> ...')
      console.log('  remove-from-tenant-privilege-group <tenant_handle> <group_id> <privilege_tenant_handle> ...');
      console.log('  update-password <tenant_handle> <emailAddress> <newPassword>');
      console.log('  add-roles-to-user <tenant_handle> <emailAddress> <role_name> ...');
      console.log('  remove-roles-from-user <tenant_handle> <emailAddress> <role_name> ...');
      console.log('  add-roles-to-all <tenant_handle> <role_name> ...');
      console.log('  remove-roles-from-all <tenant_handle> <role_name> ...');
      console.log('  add-privilege-groups-to-user <tenant_handle> <emailAddress> <group_id> ...');
      console.log('  remove-privilege-groups-from-user <tenant_handle> <emailAddress> <group_id> ...');
      console.log('  add-privilege-groups-to-all <tenant_handle> <group_id> ...');
      console.log('  remove-privilege-groups-from-all <tenant_handle> <group_id> ...');
      console.log('  create-migrations');
      console.log('  run-migrations');
      console.log('  down-migrations');
      console.log('  dump-ops <filepath> <tenant_ids> ...');
      console.log('  update-all-tenant-configs <tenant_configs_base_path>');
      console.log('  update-all-tenant-content <config_base_path>');
      console.log('  create-local-db-dump <output_path> <file_name>');
    }
  } catch (e) {
    console.log(e);
    process.exit(1);
  }
})();
