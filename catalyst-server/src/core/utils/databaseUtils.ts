import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { Connection, EntityManager, IDatabaseDriver, MikroORM } from '@mikro-orm/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { CreateTenantInput } from 'core/contracts/input/base/TenantInput';
import { TenantController } from 'core/controllers/base/TenantController';
import { Role } from 'core/entities/Role';
import { Tenant } from 'core/entities/Tenant';
import { TenantAlias } from 'core/entities/TenantAlias';
import { User } from 'core/entities/User';
import { TenantAliasController } from 'core/controllers/base/TenantAliasController';
import { TenantMeta } from 'core/entities/TenantMeta';
import { OpportunityDownloadController } from 'core/controllers/curator/OpportunityDownloadController';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { PrivilegeController } from 'core/controllers/base/PrivilegeController';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { Privilege } from 'core/entities/Privilege';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { Series } from './Async';
import { version } from '../../../package.json';

const DefaultContentFileLocation = 'config/content/content.json';
export const clearDatabase = async (orm: MikroORM<IDatabaseDriver<Connection>>): Promise<void> => {
  if (process.env.NODE_ENV === 'production') throw Error('You are running in production mode!');
  if (!process.env.NODE_DEV) throw Error('You are not running in dev mode!');
  if (!process.env.POSTGRES_HOST?.includes('catalyst-db-test') && !process.env.POSTGRES_HOST?.includes('localhost'))
    throw Error('Target is not development or test database!');
  await orm.getSchemaGenerator().dropSchema({ wrap: false });
  await orm.getSchemaGenerator().updateSchema({ wrap: false });
};

export const createSchema = async (orm: MikroORM<IDatabaseDriver<Connection>>): Promise<void> => {
  await orm.getSchemaGenerator().updateSchema({ wrap: false });
};

/*
  These are the ops that should be done first on a an empty db
  Call flush after this operation
*/
export const bootstrapDb = async (em: EntityManager<IDatabaseDriver<Connection>>): Promise<{ roles: Role[] }> => {
  // create roles
  const roles = await Promise.all(
    Object.keys(RoleNames).map(async (roleNameKey) => {
      const role = Role.newRole({ name: RoleNames[<never>roleNameKey] });
      await em.persist(role);
      return role;
    }),
  );
  return { roles };

  /*
  // i.e. add a new role
  const role = Role.newRole({ name: RoleNames.ANALYST });
  await em.persist(role);
  return { roles: [role] };
  */
};
export const createTenantAlias = async (
  em: EntityManager<IDatabaseDriver<Connection>>,
  existingHandle: string,
  tenantAlias: Partial<TenantAlias>,
): Promise<Tenant> => {
  return TenantController.createTenantAlias(em, existingHandle, tenantAlias);
};

/*
  Create a new tenant w/ default admin user
  Call flush after this operation
*/
export const createNewTenant = async ({
  em,
  tenantInput,
  adminPass,
  configFilePath,
  themeFilePath,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantInput: CreateTenantInput;
  adminPass: string;
  configFilePath?: string;
  themeFilePath?: string;
}): Promise<{ tenant: Tenant; admin: User }> => {
  const additionalParams: { config?: Record<string, unknown>; theme?: Record<string, unknown> } = {};
  if (configFilePath) {
    const configString = fs.readFileSync(configFilePath).toString();
    const config = JSON.parse(configString);
    additionalParams.config = config;
  }
  if (themeFilePath) {
    const themeFileString = fs.readFileSync(themeFilePath).toString();
    const theme = JSON.parse(themeFileString);
    additionalParams.theme = theme;
  }
  return TenantController.createTenant(em, { ...tenantInput, ...additionalParams }, adminPass);
};

export const createTenantAdmin = async ({
  em,
  tenantHandle,
  adminPass,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  adminPass: string;
}): Promise<User> => {
  return TenantController.createTenantAdmin(em, tenantHandle, adminPass);
};


export const updateAllTenantConfigs = async ({
  em,
  parentTenantConfigPath,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  parentTenantConfigPath: string;
}): Promise<void> => {
  
  const tenantPaths = getAllTenantDirs(parentTenantConfigPath);
  if(tenantPaths.length === 0) return console.log('Warning! No tenants updated.');
  await Series.forEach(tenantPaths, async (tenantPath: string = '') => {
    const startIndex = tenantPath.lastIndexOf('/');
    let updateHandleOrAlias = tenantPath.substring(startIndex + 1);
    if (!updateHandleOrAlias) throw Error(`Tenant ${updateHandleOrAlias} configs folder not found`);
    try {
      const configFilePath = `${tenantPath}/config.json`;
      const themeFilePath = `${tenantPath}/theme.json`;
      const serverFilePath = `${tenantPath}/serverConfig.json`;
      const tenantContentFilePath = `${tenantPath}/content.json`;
      const contentFilExists = fs.existsSync(tenantContentFilePath);
      const contentFilePath = contentFilExists ? tenantContentFilePath : DefaultContentFileLocation;

      const tenant = await TenantController.getTenantByHandleOrAlias(em, updateHandleOrAlias, ['handle']);
      if(!tenant) {
        console.info(`Warning! Could not find tenant or alias to update: ${updateHandleOrAlias}`);
        return;
      }
      return updateTenantMeta({ em, tenantHandle: tenant.handle, themeFilePath, configFilePath, contentFilePath, updateHandleOrAlias, serverFilePath }).catch((error) =>
        console.log(`Failed to update tenant: ${updateHandleOrAlias}`, error),
      );
    } catch (e) {
      console.log(`Failed to update tenant: ${updateHandleOrAlias}`, e);
    }
  });
};


export const updateAllTenantContent = async ({
  em,
  configDir: configDir,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  configDir: string;
}) => {
  const tenantHandles = getAllTenantDirs(`${configDir}/tenants`).map((tenantPath) => tenantPath.split('/').pop());
  await Series.forEach(tenantHandles, async (tenantHandle) => {
    const contentFilePath = `${configDir}/content/content.json`;
    try {
      if (tenantHandle) await updateTenantMeta({ em, tenantHandle, contentFilePath });
    } catch (e) {
      console.log(`Failed to update tenant: ${tenantHandle}`, e);
    }
  });
};

export const updateTenantMeta = async ({
  em,
  tenantHandle,
  themeFilePath,
  configFilePath,
  contentFilePath,
  updateHandleOrAlias,
  serverFilePath,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  themeFilePath?: string;
  configFilePath?: string;
  contentFilePath?: string;
  updateHandleOrAlias?: string;
  serverFilePath?: string;
}): Promise<void> => {
  const config = configFilePath ? getFileContent(configFilePath) : undefined;
  const theme = themeFilePath ? getFileContent(themeFilePath) : undefined;
  const contentFileString = contentFilePath ? getFileContent(contentFilePath) : undefined;
  const content = contentFileString ? updateContentFileVersion(contentFileString) : undefined;
  const serverConfig = serverFilePath ? getFileContent(serverFilePath) : undefined;
  const tenant = await TenantController.getTenantByHandle(em, tenantHandle, ['meta']);
  if (tenant) {
    if (!updateHandleOrAlias || tenant.handle === updateHandleOrAlias) {
      if (!tenant.meta) tenant.meta = TenantMeta.newTenantMeta({});
      if (config) tenant.meta.config = config;
      if (theme) tenant.meta.theme = theme;
      if (content) tenant.meta.content = content;
      if (serverConfig) tenant.meta.serverConfig = serverConfig;
      await em.persistAndFlush(tenant);
    } else {
      const tenantAlias = await TenantAliasController.getTenantAlias({
        em,
        tenantId: tenant.id,
        handle: updateHandleOrAlias,
        relationPaths: ['meta'],
      });
      if (!tenantAlias) throw Error(`Tenant alias ${updateHandleOrAlias} not found`);
      if (!tenantAlias.meta) tenantAlias.meta = TenantMeta.newTenantMeta({});
      if (config) tenantAlias.meta.config = config;
      if (theme) tenantAlias.meta.theme = theme;
      if (content) tenantAlias.meta.content = content;
      if (serverConfig) tenantAlias.meta.serverConfig = serverConfig;
      await em.persistAndFlush(tenantAlias);
    }
  } else {
    throw Error(`Tenant ${tenantHandle} not found`);
  }
};

export const createTenantPrivilegeGroup = async ({
  em,
  tenantHandle,
  name,
  privilegeTenantHandles,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  name: string;
  privilegeTenantHandles: string[];
}): Promise<PrivilegeGroup> => {
  const tenant = await TenantController.getTenantByHandle(em, tenantHandle);
  if (tenant) {
    const tenants = await TenantController.queryTenants(
      em,
      { pageSize: 1000 },
      { searchFields: [{ fieldNames: ['handle'], operator: SearchOperator.IN, searchValue: privilegeTenantHandles }] },
    );
    if (tenants.results.length !== privilegeTenantHandles.length) throw Error('Some tenant handles not found');
    // Check to see if the privilege group exists for this tenant.
    const existingPrivilegeGroup = await PrivilegeController.getPrivilegeGroup({em, tenantId: tenant.id, name, relationPaths: ['privileges']});
    if(existingPrivilegeGroup) {
      const pleaseRead = `************************\r\n    PLEASE READ\r\n************************`;
      const errorMessage = `${pleaseRead}\r\nFailed to create a privilege group as one already exists for this tenant with the same information.\r\ntenant: ${tenant.name}\r\nGroup Name: ${name}`;
      throw Error(errorMessage);
    }
    const privilegeGroup = await PrivilegeController.createPrivilegeGroup(em, tenant.id, { name });
    for (const targetTenant of tenants.results) {
      const privilege = Privilege.newPrivilege({
        resourceType: ResourceType.TENANT,
        resourceId: targetTenant.id,
        name: targetTenant.label || targetTenant.name,
      });
      privilegeGroup.privileges.add(privilege);
    }
    await em.persistAndFlush(privilegeGroup);
    await addPrivilegeGroupsToAllVerifiedUsers({em, tenantHandle, groupIds: [privilegeGroup.id]});
    return privilegeGroup;
  } else {
    throw Error(`Tenant ${tenantHandle} not found`);
  }
};

export const addToTenantPrivilegeGroup = async ({
  em,
  tenantHandle,
  groupId,
  name,
  privilegeTenantHandles,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  groupId?: string;
  name?: string;
  privilegeTenantHandles: string[];
}): Promise<PrivilegeGroup> => {
  const tenant = await TenantController.getTenantByHandle(em, tenantHandle);
  if (tenant) {
    const tenants = await TenantController.queryTenants(
      em,
      { pageSize: 1000 },
      { searchFields: [{ fieldNames: ['handle'], operator: SearchOperator.IN, searchValue: privilegeTenantHandles }] },
    );
    if (tenants.results.length !== privilegeTenantHandles.length) throw Error('Some tenant handles not found');
    const privilegeGroup = await PrivilegeController.getPrivilegeGroup({
      em,
      id: groupId,
      name: name,
      tenantId: tenant.id,
      relationPaths: ['privileges'],
    });
    if (!privilegeGroup) throw Error(`Privilege group ${groupId} ${name} not found`);
    for (const tenant of tenants.results) {
      const existingPrivilege = privilegeGroup.getPrivilege({
        resourceId: tenant.id,
        resourceType: ResourceType.TENANT,
      });
      if (!existingPrivilege) {
        const privilege = Privilege.newPrivilege({
          resourceType: ResourceType.TENANT,
          resourceId: tenant.id,
          name: tenant.label || tenant.name,
        });
        privilegeGroup.privileges.add(privilege);
      } else {
        existingPrivilege.name = tenant.label || tenant.name;
      }
    }
    await em.persistAndFlush(privilegeGroup);
    return privilegeGroup;
  } else {
    throw Error(`Tenant ${tenantHandle} not found`);
  }
};

export const removeFromTenantPrivilegeGroup = async ({
  em,
  tenantHandle,
  groupId,
  privilegeTenantHandles,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  groupId: string;
  privilegeTenantHandles: string[];
}): Promise<PrivilegeGroup> => {
  const tenant = await TenantController.getTenantByHandle(em, tenantHandle);
  if (tenant) {
    const tenants = await TenantController.queryTenants(
      em,
      { pageSize: 1000 },
      { searchFields: [{ fieldNames: ['handle'], operator: SearchOperator.IN, searchValue: privilegeTenantHandles }] },
    );
    if (tenants.results.length !== privilegeTenantHandles.length) throw Error('Some tenant handles not found');
    const privilegeGroup = await PrivilegeController.getPrivilegeGroup({
      em,
      id: groupId,
      tenantId: tenant.id,
      relationPaths: ['privileges'],
    });
    if (!privilegeGroup) throw Error(`Privilege group ${groupId} not found`);
    for (const tenant of tenants.results) {
      // remove the privilege from the group
      const privilege = privilegeGroup.privileges.getItems().find((p) => p.resourceId === tenant.id);
      if (privilege) privilegeGroup.privileges.remove(privilege);
    }
    await em.persistAndFlush(privilegeGroup);
    return privilegeGroup;
  } else {
    throw Error(`Tenant ${tenantHandle} not found`);
  }
};

export const updatePassword = async ({
  em,
  tenantHandle,
  emailAddress,
  newPassword,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  emailAddress: string;
  newPassword: string;
}): Promise<void> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const user = await em.getRepository(User).findOne({ emailAddress, tenant });
  if (!user) throw Error(`User ${emailAddress} not found`);
  user.modify({ password: newPassword });
  await em.persist(user).flush();
};

export const addRolesToUser = async ({
  em,
  tenantHandle,
  emailAddress,
  roleNames,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  emailAddress: string;
  roleNames: string[];
}): Promise<void> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const user = await em.getRepository(User).findOne({ emailAddress, tenant }, { populate: ['roles'] });
  if (!user) throw Error(`User ${emailAddress} not found`);
  const roles = await Role.rolesForRoleNames(em, roleNames as RoleNames[]);
  for (const role of roles) {
    if (!user.roles.contains(role)) {
      user.roles.add(role);
    }
  }
  await em.persist(user).flush();
};

export const addRolesToAllVerifiedUsers = async ({
  em,
  tenantHandle,
  roleNames,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  roleNames: string[];
}): Promise<User[]> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const users = await em.getRepository(User).find({ tenant, status: VerifiedStatus.VERIFIED }, { populate: ['roles'] });
  console.log(`Checking ${users.length} users for role addition.`);
  const roles = await Role.rolesForRoleNames(em, roleNames as RoleNames[]);
  for (const user of users) {
    for (const role of roles) {
      if (!user.roles.contains(role)) {
        user.roles.add(role);
      }
    }
    await em.persist(user);
  }
  await em.flush();
  return users;
};

export const removeRolesFromUser = async ({
  em,
  tenantHandle,
  emailAddress,
  roleNames,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  emailAddress: string;
  roleNames: string[];
}): Promise<void> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const user = await em.getRepository(User).findOne({ emailAddress, tenant }, { populate: ['roles'] });
  if (!user) throw Error(`User ${emailAddress} not found`);
  const roles = await Role.rolesForRoleNames(em, roleNames as RoleNames[]);
  for (const role of roles) {
    user.roles.remove(role);
  }
  await em.persist(user).flush();
};

export const removeRolesFromAll = async ({
  em,
  tenantHandle,
  roleNames,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  roleNames: string[];
}): Promise<void> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const users = await em.getRepository(User).find({ tenant }, { populate: ['roles'] });
  const roles = await Role.rolesForRoleNames(em, roleNames as RoleNames[]);
  for (const user of users) {
    for (const role of roles) {
      user.roles.remove(role);
    }
    await em.persist(user);
  }
  await em.flush();
};

export const addPrivilegeGroupsToUser = async ({
  em,
  tenantHandle,
  emailAddress,
  groupIds,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  emailAddress: string;
  groupIds: string[];
}): Promise<void> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const user = await em.getRepository(User).findOne({ emailAddress, tenant }, { populate: ['privilegeGroups'] });
  if (!user) throw Error(`User ${emailAddress} not found`);
  const privilegeGroups = await em.getRepository(PrivilegeGroup).find({ tenant, id: { $in: groupIds } });
  for (const group of privilegeGroups) {
    if (!user.privilegeGroups.contains(group)) {
      user.privilegeGroups.add(group);
    }
  }
  await em.persist(user).flush();
};

export const addPrivilegeGroupsToAllVerifiedUsers = async ({
  em,
  tenantHandle,
  groupIds,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  groupIds: string[];
}): Promise<User[]> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const users = await em
    .getRepository(User)
    .find({ tenant, status: VerifiedStatus.VERIFIED }, { populate: ['privilegeGroups'] });
  console.log(`Checking ${users.length} users for privilege group addition.`);
  const privilegeGroups = await em.getRepository(PrivilegeGroup).find({ tenant, id: { $in: groupIds } });
  for (const user of users) {
    for (const group of privilegeGroups) {
      if (!user.privilegeGroups.contains(group)) {
        user.privilegeGroups.add(group);
      }
    }
    await em.persist(user);
  }
  await em.flush();
  return users;
};

export const removePrivilegeGroupsFromUser = async ({
  em,
  tenantHandle,
  emailAddress,
  groupIds,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  emailAddress: string;
  groupIds: string[];
}): Promise<void> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const user = await em.getRepository(User).findOne({ emailAddress, tenant }, { populate: ['privilegeGroups'] });
  const privilegeGroups = await em.getRepository(PrivilegeGroup).find({ tenant, id: { $in: groupIds } });
  if (!user) throw Error(`User ${emailAddress} not found`);
  for (const group of privilegeGroups) {
    user.privilegeGroups.remove(group);
  }
  await em.persist(user).flush();
};

export const removePrivilegeGroupsFromAll = async ({
  em,
  tenantHandle,
  groupIds,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  tenantHandle: string;
  groupIds: string[];
}): Promise<void> => {
  const tenant = await TenantController.getTenantByHandleOrAlias(em, tenantHandle);
  if (!tenant) throw Error(`Tenant ${tenantHandle} not found`);
  const users = await em.getRepository(User).find({ tenant }, { populate: ['privilegeGroups'] });
  const privilegeGroups = await em.getRepository(PrivilegeGroup).find({ tenant, id: { $in: groupIds } });
  for (const user of users) {
    for (const group of privilegeGroups) {
      user.privilegeGroups.remove(group);
    }
    await em.persist(user);
  }
  await em.flush();
};

export const deleteTenant = async (em: EntityManager<IDatabaseDriver<Connection>>, tenantId: string): Promise<void> => {
  await TenantController.deleteTenant(em, tenantId);
};

export const createMigration = async (orm: MikroORM<IDatabaseDriver<Connection>>): Promise<string> => {
  const migrator = orm.getMigrator();
  return (await migrator.createMigration()).fileName;
};

export const runPendingMigrations = async (orm: MikroORM<IDatabaseDriver<Connection>>): Promise<void> => {
  const migrator = orm.getMigrator();
  await migrator.up();
};

export const runDownMigrations = async (orm: MikroORM<IDatabaseDriver<Connection>>, migrationScripts: Array<string> = []): Promise<void> => {
  const migrator = orm.getMigrator();
  if (migrationScripts.length > 0) {
    // Order and reverse to match Mikro's pattern for migration scripts.
    // This will help is processing the files in order.
    migrationScripts.sort();
    await migrator.down(migrationScripts.reverse());
    return Promise.resolve();
  }
  await migrator.down();
};

export const dumpOpportunities = async ({
  em,
  filepath,
  tenantIds,
}: {
  em: EntityManager<IDatabaseDriver<Connection>>;
  filepath: string;
  tenantIds: string[];
}): Promise<void> => {
  const outstream = fs.createWriteStream(filepath);
  outstream.on('error', (error) => {
    console.error(`Failed to write file: ${error.message}`, error);
  });
  const searchSortInput = {
    searchFields: [{ fieldNames: ['status'], operator: SearchOperator.NE, searchValue: 'Deleted' }],
  };
  await OpportunityDownloadController.writeOpportunities(em, outstream, searchSortInput, undefined, {
    tenant: { id: { $in: tenantIds } },
  });
};

export const createCleanLocalDbDump = ({ outputPath, fileName = 'local.sql'}: { outputPath: string; fileName?: string;}): void => {
  const pathExists = fs.existsSync(outputPath);
  if(!pathExists) return console.log(`Please provide a valid location path: ${outputPath}`);
  execSync(`PGPASSWORD="dev" pg_dump -h localhost -p 5432 -U dev -c catalyst-db -f ${outputPath}/${fileName}`);
  console.log(`Created catalyst-db dump file ${outputPath}/local.sql`);
};

//////////////////////// Private

const getFileContent = (filePath: string) => {
  const filExists = fs.existsSync(filePath);
  const fileString = filExists ? fs.readFileSync(filePath).toString() : undefined;
  return fileString ? JSON.parse(fileString) : undefined;
}

const excludePath: Array<string> = [];
const isTenantDirectory = (directoryName: string) => {
  const startIndex = directoryName.lastIndexOf('/');
  let tenant = directoryName.substring(startIndex + 1);
  return fs.lstatSync(directoryName).isDirectory() && !excludePath.includes(tenant);
};


const getAllTenantDirs = (tenantConfigPath: string ): Array<string> => {
  if(!fs.existsSync(tenantConfigPath) ) {
    console.log(`Failed to fetch path: ${tenantConfigPath}`);
    return [];
  };
  return fs
    .readdirSync(tenantConfigPath, { recursive: true })
    .map((directoryName) => {
      const tenantFilePath = path.join(tenantConfigPath, directoryName as string);
      return tenantFilePath;
    })
    .filter(isTenantDirectory);
}

const updateContentFileVersion = (content: any): any => {
  if (content && content['releaseNotes']){
    if (content['releaseNotes']['production-release-notes'] && !content['releaseNotes'][version]) {
      content['releaseNotes'][version] = content['releaseNotes']['production-release-notes'];
      delete content['releaseNotes']['production-release-notes'];
    }
  }
  return content;
};
