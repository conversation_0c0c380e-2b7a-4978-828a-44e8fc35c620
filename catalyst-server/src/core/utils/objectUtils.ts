export function deepCopy(obj: any, propFilter?: (obj: any, key: string) => boolean): any {
  const seen = new Map();

  function internalDeepCopy(value: any): any {
    if (value === null) return value;

    // Check if value is already copied to handle circular references
    if (seen.has(value)) return seen.get(value);

    let copy: any;

    if (Array.isArray(value)) {
      copy = [];
      seen.set(value, copy); // Add array to seen map
      for (let i = 0; i < value.length; i++) {
        copy[i] = internalDeepCopy(value[i]);
      }
      return copy;
    }

    if (typeof value === 'object') {
      copy = {};
      seen.set(value, copy); // Add object to seen map
      for (let key in value) {
        if (!propFilter || propFilter(value, key)) {
          copy[key] = internalDeepCopy(value[key]);
        }
      }
      return copy;
    }

    if (typeof value !== 'object') return value;
  }

  return internalDeepCopy(obj);
}

export function copyPrimitivesOnly(obj: any): any {
  return deepCopy(obj, (obj, key) => typeof obj[key] !== 'object');
}

/**
 * This function can help copy an object of a certain type and convert it to another type based
 * on the properties you want to copy over.
 * @param obj Object you want to extract values from
 * @param keys The keys identifies which values from the obj you want to copy over.
 * @returns returns a copy of the object as the Type you defined
 */
export function castToShape<T>(obj: any, keys: (keyof T)[]): T {
  const result: Partial<T> = {};
  for (const key of keys) {
    if (key in obj) {
      result[key] = copyPrimitivesOnly(obj[key]);
    }
  }
  return result as T;
}
