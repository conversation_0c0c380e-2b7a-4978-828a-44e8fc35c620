/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Connection, EntityManager, IDatabaseDriver, MikroORM } from '@mikro-orm/core';
import { SubmitUserInput } from 'core/contracts/input/base/UserInput';
import { CreateSubmissionInput } from 'core/contracts/input/user/SubmissionInput';
import { SubmissionController } from 'core/controllers/user/SubmissionController';
import { UserController } from 'core/controllers/user/UserController';

export class OppImportHandler {
  constructor(private readonly tenantHandle: string) {}

  static headers = {
    ID: 'ID',
    START_TIME: 'Start time',
    COMPLETION_TIME: 'Completion time',
    NAME: 'Name',
    PROBLEM_TITLE: 'Proposed Problem Title',
    PROBLEM_STATEMENT: 'Problem Statement',
    PROBLEM_CONTEXT: 'Problem Context (impact, costs, constraints)',
    BENFITS: 'Benefits',
    SOLUTION_CONCEPTS: 'Solution Concepts',
    FIRST_NAME: 'First Name',
    LAST_NAME: 'Last Name',
    BRIGADE: 'Brigade',
    UNIT: 'Unit',
    EMAIL: 'Email (.mil)',
    ALT_EMAIL: 'Alternate Email',
    PHONE: 'Phone Number',
  };

  handler = async (row: any, em: EntityManager<IDatabaseDriver<Connection>>): Promise<void> => {
    console.log('Row: ' + JSON.stringify(row));

    try {
      const headers = OppImportHandler.headers;
      const createdAt = new Date(row[headers.COMPLETION_TIME]);
      const userInput: SubmitUserInput = {
        emailAddress: row[headers.EMAIL],
        firstName: row[headers.FIRST_NAME],
        lastName: row[headers.LAST_NAME],
        org1: row[headers.BRIGADE] || '',
        org2: row[headers.UNIT] || '',
        phone: row[headers.PHONE],
        altContact: row[headers.ALT_EMAIL],
        tenantHandle: this.tenantHandle,
        createdAt,
      };
      const user = await UserController.submitUser(em, userInput);
      console.log(`Submitted user ${user.emailAddress} ${user.firstName} ${user.lastName} sucessfully`);

      const submissionInput: CreateSubmissionInput = {
        title: row[headers.PROBLEM_TITLE],
        statement: row[headers.PROBLEM_STATEMENT],
        context: row[headers.PROBLEM_CONTEXT],
        benefits: row[headers.BENFITS],
        solutionConcepts: row[headers.SOLUTION_CONCEPTS],
        createdAt,
      };

      const submission = await SubmissionController.newSubmission(em, submissionInput, { userId: user.id });
      console.log(`Created submission ${submission.title} successfuly`);
    } catch (e) {
      throw e;
    }
  };
}
