import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { AnyPage, OpportunityPage, ProjectPage, SubmissionPage } from 'core/contracts/output/Page';
import { Opportunity } from 'core/entities/Opportunity';
import { iterateEntities } from './entityIterator';
import { Submission } from 'core/entities/Submission';
import { Project } from 'core/entities/Project';
import { Series } from 'core/utils/Async';
import { getSortFilter } from 'core/storage/queryUtils';

// Use this to add a tenant relationship to opportunities
// Opportunities were previously only related to tenant through User

const copyTenantIdToOpportunity = async (em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  const page = p as OpportunityPage;
  await Series.forEach(page.results, async (opportunity, index) => {
    console.log(`${index}) opp: ${opportunity.id} ${opportunity.tenant?.handle} ${opportunity.user?.tenant?.handle}`);
    if (!opportunity.tenant && opportunity.user) {
      console.log(`${index}) opp: Assigning tenant ${opportunity.user.tenant} to opportunity ${opportunity.id}`);
      opportunity.tenant = opportunity.user.tenant;
      em.persist(opportunity);
    }
  });
  await em.flush();
  console.log('Opp: finished page');
};

const copyTenantIdToSubmission = async (em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  const page = p as SubmissionPage;
  await Series.forEach(page.results, async (submission, index) => {
    console.log(`${index}) sub: ${submission.id} ${submission.tenant?.handle} ${submission.user?.tenant?.handle}`);
    if (!submission.tenant && submission.user) {
      console.log(`${index}) sub: Assigning tenant ${submission.user.tenant} to submission ${submission.id}`);
      submission.tenant = submission.user.tenant;
      em.persist(submission);
    }
  });
  await em.flush();
  console.log('Sub: finished page');
};

const copyTenantIdToProject = async (em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  const page = p as ProjectPage;
  await Series.forEach(page.results, async (project, index) => {
    console.log(`${index}) proj: ${project.id} ${project.tenant?.handle} ${project.creator?.tenant?.handle}`);
    if (!project.tenant && project.creator) {
      console.log(`${index}) proj: Assigning tenant ${project.creator.tenant} to project ${project.id}`);
      project.tenant = project.creator.tenant;
      em.persist(project);
    }
  });
  await em.flush();
  console.log('Proj: finished page');
};

(async () => {
  let application: Application | undefined;
  try {
    const args = process.argv.slice(2);
    const application = new Application();
    await application.init();
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const em = application!.orm!.em.fork();
    await iterateEntities(
      Opportunity,
      em,
      {},
      getSortFilter([], { fieldName: 'id', ascending: false }),
      ['user', 'tenant', 'user.tenant'],
      copyTenantIdToOpportunity,
    );
    console.log('Finished ops');
    await iterateEntities(
      Submission,
      em,
      {},
      getSortFilter([], { fieldName: 'id', ascending: false }),
      ['user', 'tenant', 'user.tenant'],
      copyTenantIdToSubmission,
    );
    console.log('Finished subs');
    await iterateEntities(
      Project,
      em,
      {},
      getSortFilter([], { fieldName: 'id', ascending: false }),
      ['creator', 'tenant', 'creator.tenant'],
      copyTenantIdToProject,
    );
    console.log('Finished projs');
  } catch (e) {
    console.log(e);
    process.exit(1);
  } finally {
    if (application) await application.orm?.close();
    if (application) application.server?.close();
    process.exit(0);
  }
})();

// run this with something like:
// NODE_ENV=production NODE_DEV=true npx ts-node -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/utils/tools/tenantIdMigration
