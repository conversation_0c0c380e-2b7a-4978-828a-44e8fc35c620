import Application from 'application';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { OpportunityDownloadController } from 'core/controllers/curator/OpportunityDownloadController';
import * as fs from 'fs';

const createFile = async () => {
  const application = new Application();
  await application.init();
  await OpportunityDownloadController.writeOpportunities(
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    application.orm!.em.fork(),
    fs.createWriteStream('tmp/test.xls'),
    {
      searchFields: [{ fieldNames: ['status'], operator: SearchOperator.NE, searchValue: 'Deleted' }],
    },
    undefined,
    { tenant: { id: { $in: ['<ids here>'] } } },
  );
  await application.orm?.close();
};

createFile();
