import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { RelatedOpportunityType } from 'core/contracts/enums/RelatedOpportunityType';
import { AnyPage, OpportunityPage } from 'core/contracts/output/Page';
import { Opportunity } from 'core/entities/Opportunity';
import { RelatedOpportunity } from 'core/entities/RelatedOpportunity';
import { getSortFilter } from 'core/storage/queryUtils';
import { Series } from 'core/utils/Async';
import { iterateEntities } from './entityIterator';

const migrateRelatedOpportunities = async (em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  const page = p as OpportunityPage;
  await Series.forEach(page.results, async (opportunity, index) => {
    console.log(`opp: ${opportunity.id}`);
    const oldRelatedOpps = opportunity.opportunities.getItems();
    oldRelatedOpps.forEach((oldRelatedOpp, oppIndex) => {
      if (
        !opportunity.ownedOpportunities.getItems().find((ownedOpp) => ownedOpp.target.id === oldRelatedOpp.id) &&
        !opportunity.owningOpportunities.getItems().find((owningOpp) => owningOpp.source.id === oldRelatedOpp.id)
      ) {
        const newRelatedOpp = RelatedOpportunity.newRelatedOpportunity({
          type: RelatedOpportunityType.LINKED,
          orderBy: oppIndex,
        });
        newRelatedOpp.target = oldRelatedOpp;
        newRelatedOpp.source = opportunity;
        opportunity.ownedOpportunities.add(newRelatedOpp);
        oldRelatedOpp.owningOpportunities.add(newRelatedOpp);
        console.log(`opp: ${opportunity.id} added relatedOpp: ${oldRelatedOpp.id}`);
      } else {
        console.log(`opp: ${opportunity.id} already has relatedOpp: ${oldRelatedOpp.id}`);
      }
    });
  });
  await em.flush();
  console.log('Opp: finished page');
};

// what does this do?
// Iterates opportunities, adds old related opportunites as a new related opportunity LINK
// Each opp is checked to see if the link has already been added, so that it's added only once

(async () => {
  let application: Application | undefined;
  try {
    const application = new Application();
    await application.init();
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const em = application!.orm!.em.fork();
    await iterateEntities(
      Opportunity,
      em,
      {},
      getSortFilter([], { fieldName: 'id', ascending: false }),
      ['opportunities', 'opportunities.owningOpportunities', 'ownedOpportunities', 'owningOpportunities'],
      migrateRelatedOpportunities,
    );
    console.log('Finished ops');
  } catch (e) {
    console.log(e);
    process.exit(1);
  } finally {
    if (application) await application.orm?.close();
    if (application) application.server?.close();
    process.exit(0);
  }
})();

// run this with something like:
// NODE_ENV=production NODE_DEV=true npx ts-node -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/utils/tools/migrateRelatedOpportunities
