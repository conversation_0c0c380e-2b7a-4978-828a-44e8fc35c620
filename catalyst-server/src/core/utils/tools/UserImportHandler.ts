/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Connection, EntityManager, IDatabaseDriver, MikroORM } from '@mikro-orm/core';
import faker from 'faker';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { CreateUserInput, UserLinks } from 'core/contracts/input/curator/UserInput';
import { UserController } from 'core/controllers/curator/UserController';

export class UserImportHandler {
  constructor(private readonly tenantId: string) {}

  static headers = {
    FIRST_NAME: 'First Name',
    LAST_NAME: 'Last Name',
    EMAIL: 'Email',
    ORG1: 'Org 1',
    ORG2: 'Org 2',
    PHONE: 'Phone',
    ALT_EMAIL: 'Alternate Email',
    PASSWORD: 'Password',
  };

  handler = async (row: any, em: EntityManager<IDatabaseDriver<Connection>>): Promise<void> => {
    // console.log('Row: ' + JSON.stringify(row));

    try {
      const headers = UserImportHandler.headers;
      const userInput: CreateUserInput = {
        emailAddress: row[headers.EMAIL],
        firstName: row[headers.FIRST_NAME],
        lastName: row[headers.LAST_NAME],
        org1: row[headers.ORG1] || '',
        org2: row[headers.ORG2] || '',
        phone: row[headers.PHONE],
        password: row[headers.PASSWORD] || faker.internet.password(12),
      };

      const links: UserLinks = {
        roleNames: [RoleNames.CURATOR],
      };

      console.log(`${userInput.emailAddress}  ${userInput.password}`);
      const user = await UserController.createUser(em, this.tenantId, userInput, links);
      //console.log(`Submitted user ${user.emailAddress} ${user.firstName} ${user.lastName} sucessfully`);
    } catch (e) {
      throw e;
    }
  };
}
