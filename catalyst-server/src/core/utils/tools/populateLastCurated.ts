import { Connection, EntityManager, IDatabaseDriver, QueryOrder } from '@mikro-orm/core';
import Application from 'application';
import { AnyPage, OpportunityPage, ProjectPage } from 'core/contracts/output/Page';
import { Opportunity } from 'core/entities/Opportunity';
import { iterateEntities } from './entityIterator';
import { Project } from 'core/entities/Project';
import { Series } from 'core/utils/Async';
import { getSortFilter } from 'core/storage/queryUtils';
import { CurationEvent } from 'core/entities/CurationEvent';
import { EntityType } from 'core/contracts/enums/EntityType';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';

// This tool sets the new field 'lastCurated' on Opp/Project based on the presence of an UPDATE CurationEvent

const setOpportunityLastCurated = async (em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  const page = p as OpportunityPage;
  await Series.forEach(page.results, async (opportunity, index) => {
    const lastCurated = await getLastCurated(em, opportunity.id, EntityType.OPPORTUNITY);
    console.log(
      `${index}) opp: ${opportunity.id} currentLastCurated: ${opportunity.lastCurated} LastCuratedAt: ${lastCurated}`,
    );
    if (lastCurated) {
      opportunity.lastCurated = lastCurated;
      em.persist(opportunity);
    }
  });
  await em.flush();
  console.log('Opp: finished page');
};

const setProjectLastCurated = async (em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  const page = p as ProjectPage;
  await Series.forEach(page.results, async (project, index) => {
    const lastCurated = await getLastCurated(em, project.id, EntityType.PROJECT);
    console.log(
      `${index}) proj: ${project.id} currentLastCurated: ${project.lastCurated} LastCuratedAt: ${lastCurated}`,
    );
    if (lastCurated) {
      project.lastCurated = lastCurated;
      em.persist(project);
    }
  });
  await em.flush();
  console.log('Proj: finished page');
};

const getLastCurated = async (
  em: EntityManager<IDatabaseDriver<Connection>>,
  entityId: string,
  entityType: EntityType,
): Promise<Date | null> => {
  const curationEvents = await em.getRepository(CurationEvent).find(
    {
      entityId,
      entityType,
    },
    {
      orderBy: { updatedAt: QueryOrder.DESC },
    },
  );
  if (!curationEvents?.length) return null;
  let lastCurated: Date | null | undefined = null;
  curationEvents.forEach((curationEvent: CurationEvent) => {
    if (curationEvent.type === CurationEventType.UPDATE) {
      if (!lastCurated || (curationEvent.updatedAt && curationEvent.updatedAt.getTime() > lastCurated.getTime())) {
        lastCurated = curationEvent.updatedAt;
      }
    }
  });
  return lastCurated;
};

(async () => {
  let application: Application | undefined;
  try {
    const application = new Application();
    await application.init();
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const em = application!.orm!.em.fork();
    await iterateEntities(
      Opportunity,
      em,
      {},
      getSortFilter([], { fieldName: 'id', ascending: false }),
      [],
      setOpportunityLastCurated,
    );
    console.log('Finished ops');
    await iterateEntities(
      Project,
      em,
      {},
      getSortFilter([], { fieldName: 'id', ascending: false }),
      [],
      setProjectLastCurated,
    );
    console.log('Finished projs');
  } catch (e) {
    console.log(e);
    process.exit(1);
  } finally {
    if (application) await application.orm?.close();
    if (application) application.server?.close();
    process.exit(0);
  }
})();

// run this with something like:
// NODE_ENV=production NODE_DEV=true npx ts-node -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/core/utils/tools/populateLastCurated
