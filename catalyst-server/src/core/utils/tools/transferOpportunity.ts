import Application from 'application';
import { Opportunity } from 'core/entities/Opportunity';
import { Tenant } from 'core/entities/Tenant';
import { iterateEntities } from './entityIterator';
import { getSortFilter } from 'core/storage/queryUtils';
import { AnyPage, OpportunityPage } from 'core/contracts/output/Page';
import { Series } from '../Async';
import { EntityManager, Connection, IDatabaseDriver } from '@mikro-orm/core';
import { TenantController } from 'core/controllers/base/TenantController';
import { copyPrimitivesOnly } from '../objectUtils';
import { Category } from 'core/entities/Category';

const transferOpportunity = async (
  opportunity: Opportunity,
  toTenant: Tenant,
  em: EntityManager<IDatabaseDriver<Connection>>,
) => {
  const jsonObj = copyPrimitivesOnly(opportunity);
  const newOpportunity = Opportunity.newOpportunity(jsonObj, toTenant);

  newOpportunity.categories.add(await handleCategories(opportunity, toTenant, em));

  // FINISH THIS

  //em.persist(newOpportunity);
  console.log(newOpportunity);
};

const handleCategories = async (
  opportunity: Opportunity,
  toTenant: Tenant,
  em: EntityManager<IDatabaseDriver<Connection>>,
): Promise<Category[]> => {
  const categories = opportunity.categories.getItems();
  const newCategories: Category[] = [];
  await Series.forEach(categories, async (category) => {
    let targetCategory = (await em.findOne(Category, { name: category.name, tenant: toTenant })) || Category.newCategory({ name: category.name });
    newCategories.push(targetCategory);
  });
  return newCategories;
};

const handler = async (toTenant: Tenant, em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  const page = p as OpportunityPage;
  await Series.forEach(page.results, async (opportunity, index) => {
    await transferOpportunity(opportunity, toTenant, em);
    console.log(`${index} transferred ${opportunity.id}`);
  });
  await em.flush();
  console.log('Finshed Opportunity Page');
};

/*
    What does this do?
    Migrate a list of opportunities from one tenant to another. Copying primitives and recreateing the corresponding entitiy relationships.
 */

(async () => {
  let application: Application | undefined;
  try {
    const application = new Application();
    await application.init();
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const em = application!.orm!.em.fork();
    const tenant = await TenantController.getTenant(em, '4bfd734e-dc36-406b-b92b-68a4ab81bb83');
    if (!tenant) throw new Error('Tenant not found');
    const _handler = handler.bind(null, tenant);
    await iterateEntities(
      Opportunity,
      em,
      { id: { $in: ['8117fbfd-1d0f-4e97-bf5e-1fcb1184dcb6'] } },
      getSortFilter([], { fieldName: 'id', ascending: false }),
      [
        'opportunities',
        'opportunities.owningOpportunities',
        'ownedOpportunities',
        'owningOpportunities',
        'submissions',
        'projects',
        'categories',
        'stakeholders',
        'attachments',
        'user',
      ],
      _handler,
    );
    console.log('Finished ops');
  } catch (e) {
    console.log(e);
    process.exit(1);
  } finally {
    if (application) await application.orm?.close();
    if (application) application.server?.close();
    process.exit(0);
  }
})();

// run this with something like:
// NODE_ENV=production NODE_DEV=true npx ts-node -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/core/utils/tools/transferOpportunity
