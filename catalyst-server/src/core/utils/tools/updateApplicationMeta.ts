import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { AnyPage } from 'core/contracts/output/Page';
import { iterateEntities } from './entityIterator';
import { Series } from 'core/utils/Async';
import { getSortFilter } from 'core/storage/queryUtils';
import { ApplicationMeta } from 'core/entities/ApplicationMeta';

// This tool sets the new field 'lastCurated' on Opp/Project based on the presence of an UPDATE CurationEvent

const updateAppMeta = async (em: EntityManager<IDatabaseDriver<Connection>>, p: AnyPage) => {
  await Series.forEach(p.results, async (entity, index) => {
    const appMeta = entity as ApplicationMeta;
    console.log(`${index}) entity: ${appMeta.id}`);
    console.log(JSON.stringify(appMeta.curationMeta, null, 2));
    if (appMeta.curationMeta?.oppTable) {
      const newCurationMeta = JSON.parse(JSON.stringify(appMeta.curationMeta));
      const cols = (newCurationMeta.oppTable as { cols?: [{ colId?: string }] }).cols;
      const updatedAtCol = cols?.find((col) => col.colId === 'updatedAt');
      if (updatedAtCol) {
        console.log(`Fixing up ${appMeta.id}`);
        updatedAtCol.colId = 'lastCurated';
        appMeta.curationMeta = newCurationMeta;
        em.persist(appMeta);
      }
    }
  });
  await em.flush();
  console.log('finished page');
};

(async () => {
  let application: Application | undefined;
  try {
    const application = new Application();
    await application.init();
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const em = application!.orm!.em.fork();
    await iterateEntities(
      ApplicationMeta,
      em,
      {},
      getSortFilter([], { fieldName: 'id', ascending: false }),
      [],
      updateAppMeta,
    );
    console.log('Finished appMeta');
  } catch (e) {
    console.log(e);
    process.exit(1);
  } finally {
    if (application) await application.orm?.close();
    if (application) application.server?.close();
    process.exit(0);
  }
})();

// run this with something like:
// NODE_ENV=production NODE_DEV=true npx ts-node -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/utils/tools/updateApplicationMeta
