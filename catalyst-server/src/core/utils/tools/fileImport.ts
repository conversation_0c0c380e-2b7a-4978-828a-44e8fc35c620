/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { parseStream } from '@fast-csv/parse';
import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { TenantController } from 'core/controllers/base/TenantController';
import * as fs from 'fs';
import { UserImportHandler } from './UserImportHandler';

async function loadAndProcess(
  em: EntityManager<IDatabaseDriver<Connection>>,
  stream: fs.ReadStream,
  handler: (row: any, em: EntityManager<IDatabaseDriver<Connection>>) => Promise<void>,
  options = { delimiter: '\t', headers: true, ignoreEmpty: true, trim: true },
) {
  const promises: any[] = [];
  return new Promise<void>((resolve, reject) => {
    parseStream(stream, options)
      .on('error', (error) => {
        console.error(error);
        reject(error);
      })
      .on('data', (row) => {
        promises.push(
          handler(row, em).catch((e) => {
            console.log(e);
            reject(e);
          }),
        );
      })
      .on('end', (rowCount: number) => {
        Promise.all(promises)
          .then((all) => {
            console.log(`Parsed ${rowCount} rows`);
            resolve();
          })
          .catch((e) => reject(e));
      });
  });
}

(async () => {
  let application: Application | undefined;
  try {
    const args = process.argv.slice(2);
    if (args?.length > 1) {
      const application = new Application();
      await application.init();
      const em = application!.orm!.em.fork();
      const tenant = await TenantController.getTenantByHandleOrAlias(em, args[1]);
      if (!tenant) throw Error(`Tenant Not Found for handle or alias ${args[1]}`);
      const stream = fs.createReadStream(args[0]);
      //const importHandler = new OppImportHandler(args[1]);
      const importHandler = new UserImportHandler(tenant.id);
      await loadAndProcess(em, stream, importHandler.handler);
    } else {
      console.log('fileImport <filename> <tenantHandle>');
    }
  } catch (e) {
    console.log(e);
    process.exit(1);
  } finally {
    if (application) await application.orm?.close();
  }
})();

// run this with something like:
// NODE_ENV=production NODE_DEV=true npx ts-node -r tsconfig-paths/register -r reflect-metadata
//      -r dotenv/config src/utils/tools/fileImport.ts tmp/pathfinder_users.csv pathfinder
