import { Connection, EntityManager, FilterQuery, IDatabaseDriver, QueryOrderMap } from '@mikro-orm/core';
import { AnyPage } from 'core/contracts/output/Page';
import { CoreEntity } from 'core/entities/CoreEntity';
import { getPageInfo } from 'core/storage/queryUtils';

export const iterateEntities = async <E extends CoreEntity>(
  classType: { new (): E },
  em: EntityManager<IDatabaseDriver<Connection>>,
  filter: FilterQuery<E>,
  sortFilter: QueryOrderMap<E>,
  relationPaths: string[],
  handler: (em: EntityManager<IDatabaseDriver<Connection>>, page: AnyPage) => Promise<void>,
): Promise<void> => {
  const limit = 10;
  let offset = 0;
  let page: AnyPage | null = null;
  do {
    const [results, totalCount] = await em
      .getRepository(classType)
      .findAndCount(filter, { populate: relationPaths as never, orderBy: sortFilter, limit, offset });
    page = { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
    await handler(em, page);
    console.log('finsished page (handler)');
    offset = offset + limit;
  } while (page.pageInfo.hasNext);
};
