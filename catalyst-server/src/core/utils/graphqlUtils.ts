/* eslint-disable @typescript-eslint/no-explicit-any */
import { GraphQLResolveInfo } from 'graphql';
import graphqFields from 'graphql-fields';

export function fieldsToRelations(
  info: GraphQLResolveInfo,
  options: { depth?: number; root?: string; excludeFields?: string[]; includeFields?: string[] } = {
    depth: undefined,
    root: '',
    excludeFields: [],
    // fields, not paths that should be explicitly included if present in the input info
    // useful for virtual fields that must be explicitly requested
    includeFields: [],
  },
): string[] {
  const paths: string[][] = [];

  const nested = (field: any, key: string = undefined as any, deep = 0, parent: string[] = []) => {
    if (!field || Object.values(field).length === 0) {
      return;
    }

    if (deep > 0 || !!options.root) {
      parent.push(key);
      if (
        parent.slice(!options.root ? 0 : options.root?.split('.').length).length > 0 &&
        parent.slice(0, !options.root ? 0 : options.root?.split('.').length).toString() ===
          (!options.root ? '' : options.root?.split('.').toString())
      ) {
        const path = parent.slice(!options.root ? 0 : options.root?.split('.').length);
        paths.push(path);
      }
    }

    Object.keys(field).forEach((key: any) => {
      if (Object.values(field[key]).length > 0 && !!options.depth ? deep < options.depth : true) {
        nested(field[key], key, deep + 1, [...parent]);
      }
    });
  };

  const value = !options.root
    ? graphqFields(info, {}, { excludedFields: options.excludeFields })
    : options.root.split('.').reduce(function (p, prop) {
        return p[prop];
      }, graphqFields(info, {}, { excludedFields: options.excludeFields }));

  nested(value, !!options.root ? options.root.split('.').pop() : undefined);

  options.includeFields?.forEach((field: string) => {
    if (value[field]) paths.push([field]);
  });

  return paths.map((list: string[]) => list.join('.'));
}
