import { v4 } from 'uuid';

export class Id {
  static newId(): string {
    return v4();
  }

  static simpleId(value: number | string, prefix?: number | string): string {
    const format = '00000000-0000-0000-0000-000000000000';
    const valueString = value.toString();
    const prefixString = prefix ? prefix.toString() : '';
    return `${prefixString}${format.substring(prefixString.length, format.length - valueString.length)}${valueString}`;
  }
}
