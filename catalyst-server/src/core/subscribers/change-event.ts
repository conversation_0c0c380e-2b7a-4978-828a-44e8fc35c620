import { ChangeSet, EventSubscriber, FlushEventArgs } from '@mikro-orm/core';
import { ChangeEventType } from 'core/contracts/enums/ChangeEventType';
import { TargetType } from 'core/contracts/enums/TargetType';
import { ChangeEvent } from 'core/entities/ChangeEvent';
import fs from 'fs';
import path from 'path';

export type EventTrackingConfig =
  | { eventType: ChangeEventType; op: 'update'; fields: string[]; entityType: string }
  | { eventType: ChangeEventType; op: 'create' | 'delete'; entityType: string };

const CONFIG_PATH = path.resolve(__dirname, '../../../config/audit-logs/audit-logs.json');
let configCache: Record<string, EventTrackingConfig[]> = {};

function loadEventTrackingConfig(): Record<string, EventTrackingConfig[]> {
  try {
    const raw = fs.readFileSync(CONFIG_PATH, 'utf-8');
    configCache = JSON.parse(raw);
  } catch (e) {
    console.error('Failed to load change-events config:', e);
    configCache = {};
  }
  return configCache;
}

function getConfigsForEntity(entity: ChangeSet<Partial<unknown>>): EventTrackingConfig[] {
  const entityName = entity.name;
  const configs = configCache[entityName] ?? [];
  return configs;
}

loadEventTrackingConfig(); // initial load

export class ChangeEventSubscriber implements EventSubscriber {
  async onFlush(args: FlushEventArgs) {
    const em = args.em.fork();
    const uow = args.em.getUnitOfWork();
    const logs: ChangeEvent[] = [];
    const emLoggerContext = args.em.getLoggerContext();
    const actorUserId = emLoggerContext?.user?.userId ?? null;

    for (const changeSet of uow.getChangeSets()) {
      const entity = changeSet.entity;
      if (!entity || entity instanceof ChangeEvent) continue;

      const configs = getConfigsForEntity(changeSet);
      if (!configs.length) continue;

      for (const config of configs) {
        if (config.op !== changeSet.type) continue;

        let shouldLog = true;

        // Only apply diff trigger logic to update
        if (config.op === 'update') {
          const changedFields = Object.keys(changeSet.payload);
          shouldLog = config.fields.some((f) => changedFields.includes(f));
        }

        if (!shouldLog) continue;

        logs.push(
          em.create(ChangeEvent, {
            eventType: config.eventType,
            user: actorUserId ? em.getReference('User', actorUserId) : undefined,
            targetId: entity.id,
            targetType: config.entityType as TargetType,
            payload: changeSet.payload,
            createdAt: new Date(),
          }),
        );
      }
    }

    for (const log of logs) {
      try {
        await em.persist(log).flush();
      } catch (e) {
        console.error('Failed to persist change event:', e);
      }
    }
  }
}
