import {
  Cascade,
  Collection,
  Entity,
  Enum,
  ManyToMany,
  ManyToOne,
  OneToMany,
  Property,
  QueryOrder,
} from '@mikro-orm/core';
import { ProjectStatus } from 'core/contracts/enums/ProjectStatus';
import { Field, ObjectType } from 'type-graphql';
import { Attachment } from './Attachment';
import { Category } from './Category';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';
import { ProjectStakeholder } from './ProjectStakeholder';
import { User } from './User';
import { Tenant } from './Tenant';

@ObjectType({ simpleResolvers: true })
@Entity()
export class Project extends CoreEntity {
  @Field(() => Date, { nullable: true })
  @Property({ nullable: true })
  public lastCurated?: Date;

  @Field()
  @Property()
  title!: string;

  @Field(() => ProjectStatus)
  @Enum(() => ProjectStatus)
  status: ProjectStatus = ProjectStatus.PENDING;

  // optional
  @Field({ nullable: true })
  @Property({ nullable: true, columnType: 'text' })
  summary?: string;

  @Field({ nullable: true })
  @Property({ nullable: true, columnType: 'text' })
  background?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  startDate?: Date;

  @Field({ nullable: true })
  @Property({ nullable: true })
  endDate?: Date;

  @Field({ nullable: true })
  @Property({ nullable: true, columnType: 'text' })
  goals?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  type?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  otherType?: string;

  @Field({ nullable: true })
  @Property({ nullable: true, columnType: 'text' })
  statusNotes?: string;

  @Field(() => Tenant, { nullable: true })
  @ManyToOne(() => Tenant, { nullable: true })
  tenant?: Tenant;

  @Field(() => User, { nullable: true })
  @ManyToOne(() => User, { nullable: true })
  creator?: User;

  @Field(() => [Attachment])
  @OneToMany(() => Attachment, (a: Attachment) => a.project, { cascade: [Cascade.PERSIST, Cascade.MERGE] })
  attachments = new Collection<Attachment>(this);

  @Field(() => [Opportunity])
  @ManyToMany(() => Opportunity, (o: Opportunity) => o.projects, { owner: true })
  opportunities = new Collection<Opportunity>(this);

  @Field(() => [Category])
  //should be unique, but mikro bug in 5.1 causes error on delete when set to unique
  @ManyToMany(() => Category, (c: Category) => c.projects, { owner: true })
  categories = new Collection<Category>(this);

  @Field(() => [ProjectStakeholder])
  @OneToMany(() => ProjectStakeholder, (p: ProjectStakeholder) => p.project, {
    orphanRemoval: true,
    cascade: [Cascade.ALL],
    orderBy: { orderBy: QueryOrder.ASC },
  })
  projectStakeholders = new Collection<ProjectStakeholder>(this);

  static newProject(values: Partial<Project>, tenant: Tenant): Project {
    const instance = new Project();
    instance.initialize(values);
    instance.tenant = tenant;
    return instance;
  }
}
