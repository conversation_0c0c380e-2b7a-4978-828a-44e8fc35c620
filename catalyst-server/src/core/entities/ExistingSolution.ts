import { Entity, ManyToOne, Property } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { Exclude, Expose } from 'class-transformer';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
export class ExistingSolution extends CoreEntity {
  @Field()
  @Property()
  @Expose()
  source!: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  title?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  organization?: string;

  @Field()
  @Property()
  @Expose()
  needsModification: boolean = false;

  @ManyToOne(() => Opportunity)
  @Field(() => Opportunity)
  opportunity!: Opportunity;

  static newExistingSolution(values: Partial<ExistingSolution>): ExistingSolution {
    const instance = new ExistingSolution();
    instance.initialize(values);
    return instance;
  }
}
