import { BaseEntity, EntityData, PrimaryKey, Property, FromEntityType } from '@mikro-orm/core';
import { Field, ID, ObjectType } from 'type-graphql';
import { Id } from 'core/utils/Id';
import { Exclude, Expose } from 'class-transformer';

@ObjectType({ isAbstract: true, simpleResolvers: true })
@Exclude()
export class CoreEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryKey({ type: 'uuid' })
  @Expose()
  public id: string = Id.newId();

  @Field()
  @Property()
  public createdAt: Date = new Date();

  @Field(() => Date, { nullable: true })
  @Property({ nullable: true, onUpdate: () => new Date() })
  public updatedAt?: Date;

  protected initialize(values: EntityData<FromEntityType<this>>): void {
    this.assign(values);
  }

  public modify(values: EntityData<FromEntityType<this>>): void {
    this.assign(values);
  }
}
