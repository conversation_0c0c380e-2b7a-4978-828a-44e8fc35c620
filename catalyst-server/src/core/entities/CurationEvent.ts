import { Entity, Enum, ManyToOne, Property } from '@mikro-orm/core';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { CurationEventData } from './CurationEventData';
import { User } from './User';

@ObjectType({ simpleResolvers: true })
@Entity()
export class CurationEvent extends CoreEntity {
  @Field(() => User, { nullable: true })
  @ManyToOne(() => User, { nullable: true })
  user?: User;

  @Field(() => CurationEventType)
  @Enum(() => CurationEventType)
  type!: CurationEventType;

  @Field(() => EntityType)
  @Enum(() => EntityType)
  entityType!: EntityType;

  @Field({ nullable: true })
  @Property({ nullable: true, index: true })
  entityId?: string;

  @Field(() => CurationEventData, { nullable: true })
  @Property({ type: 'json' })
  data: CurationEventData = {};

  static newCurationEvent(values: Partial<CurationEvent>): CurationEvent {
    const instance = new CurationEvent();
    instance.initialize(values);
    return instance;
  }
}
