import { <PERSON>tity, Enum, ManyToOne, Property } from '@mikro-orm/core';
import { RelatedOpportunityType } from 'core/contracts/enums/RelatedOpportunityType';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';

@ObjectType({ simpleResolvers: true })
@Entity()
// Note: disabling unique indexes to allow for transactional replace of entire set
// @Unique({ properties: ['project', 'stakeholder', 'type'] })
// @Unique({ properties: ['project', 'orderBy'] })
export class RelatedOpportunity extends CoreEntity {
  @Field(() => Opportunity)
  @ManyToOne(() => Opportunity)
  source!: Opportunity;

  @Field(() => Opportunity)
  @ManyToOne(() => Opportunity)
  target!: Opportunity;

  @Field(() => RelatedOpportunityType)
  @Enum(() => RelatedOpportunityType)
  type!: RelatedOpportunityType;

  // a 'weight' value used for ordering on retrieval
  @Property()
  orderBy!: number;

  static newRelatedOpportunity(values: Partial<RelatedOpportunity>): RelatedOpportunity {
    const instance = new RelatedOpportunity();
    instance.initialize(values);
    return instance;
  }
}
