import { Entity, Enum, ManyToOne, Property } from '@mikro-orm/core';
import { Opportunity } from './Opportunity';
import { Field, ObjectType } from 'type-graphql';
import { Expose, Type } from 'class-transformer';
import { CoreEntity } from './CoreEntity';
import { OwnershipStatus } from 'core/contracts/enums/OwnershipStatus';
import { Owner } from './Owner';

@ObjectType({ simpleResolvers: true })
@Entity()
export class OpportunityOwnerStatus extends CoreEntity {
  @Field(() => OwnershipStatus)
  @Enum(() => OwnershipStatus)
  @Property()
  @Expose()
  status: OwnershipStatus = OwnershipStatus.CURRENT;

  @Field(() => Date, { nullable: true })
  @Property({ nullable: true })
  @Expose()
  statusSetPreviousAt?: Date;

  @Field(() => Date, { nullable: true })
  @Property({ nullable: true })
  @Expose()
  statusSetRemovedAt?: Date;

  @Field(() => Boolean)
  @Property({ persist: false, default: false })
  @Expose()
  get isRemoved(): boolean {
    return this.status === OwnershipStatus.REMOVED;
  }

  @Field(() => Owner, { nullable: true })
  @ManyToOne(() => Owner, { nullable: true })
  @Expose()
  @Type(() => Owner)
  owner!: Owner;

  @ManyToOne(() => Opportunity)
  @Field(() => Opportunity)
  @Expose()
  @Type(() => Opportunity)
  opportunity!: Opportunity;

  static newOpportunityOwnerStatus(values: Partial<OpportunityOwnerStatus>): OpportunityOwnerStatus {
    const instance = new OpportunityOwnerStatus();
    instance.initialize(values);
    return instance;
  }
}
