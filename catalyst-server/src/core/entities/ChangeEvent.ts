import { Entity, Enum, Index, ManyToOne, Property } from '@mikro-orm/core';
import { ChangeEventType } from 'core/contracts/enums/ChangeEventType';
import { TargetType } from 'core/contracts/enums/TargetType';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { User } from './User';

@ObjectType({ simpleResolvers: true })
@Entity()
@Index({ properties: ['eventType', 'targetId'] })
@Index({ properties: ['targetType', 'targetId'] })
export class ChangeEvent extends CoreEntity {
  @Field(() => ChangeEventType)
  @Enum(() => ChangeEventType)
  @Property()
  eventType!: ChangeEventType;

  @Field(() => User, { nullable: true })
  @ManyToOne(() => User, { nullable: true, deleteRule: 'set null' })
  user?: User;

  @Field(() => String)
  @Property({ type: 'json' })
  payload!: Record<string, unknown>;

  @Field(() => String)
  @Property()
  targetId!: string;

  @Field(() => TargetType, { nullable: true })
  @Enum(() => TargetType)
  @Property()
  targetType?: TargetType;
}
