import { Entity, Enum, ManyToOne, Property } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Permission } from 'core/auth/Permission';
import { PrivilegeGroup } from './PrivilegeGroup';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { Resource } from 'core/contracts/input/base/CommonInput';
import { Exclude, Expose } from 'class-transformer';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
export class Privilege extends CoreEntity {
  @Field({})
  @Property({})
  @Expose()
  name!: string;

  // assumption is all permissions for now...
  //@Property()
  permissions = Permission.READ | Permission.WRITE | Permission.DELETE | Permission.SHARE;

  @Field({ nullable: true })
  @Property({ nullable: true, index: true })
  @Expose()
  resourceId?: string;

  @Field(() => ResourceType)
  @Enum(() => ResourceType)
  @Expose() 
  resourceType!: ResourceType;

  @ManyToOne(() => PrivilegeGroup, { deleteRule: 'cascade' })
  privilegeGroup!: PrivilegeGroup;

  hasPermissions(...permissions: number[]): boolean {
    return Permission.has(this.permissions, ...permissions);
  }

  hasPrivilege(resource: Resource): boolean {
    return this.resourceType === resource.resourceType && this.resourceId === resource.resourceId;
  }

  equals(privilege: Privilege): boolean {
    return this.resourceType === privilege.resourceType && this.resourceId === privilege.resourceId;
  }

  static newPrivilege(values: Partial<Privilege>): Privilege {
    const instance = new Privilege();
    instance.initialize(values);
    return instance;
  }
}
