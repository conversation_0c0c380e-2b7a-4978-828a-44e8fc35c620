import { Cascade, Entity, ManyToOne, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Tenant } from './Tenant';
import { TenantMeta } from './TenantMeta';

@ObjectType({ simpleResolvers: true })
@Entity()
@Unique({ properties: ['handle', 'tenant'] })
export class TenantAlias extends CoreEntity {
  @Property()
  @Field()
  handle!: string;
  @Property({ nullable: true })
  @Field({ nullable: true })
  name?: string;
  @Property({ nullable: true })
  @Field({ nullable: true })
  label?: string;
  @ManyToOne(() => Tenant)
  tenant!: Tenant;

  @Field(() => TenantMeta, { nullable: true })
  @ManyToOne(() => TenantMeta, { nullable: true, cascade: [Cascade.ALL] })
  meta?: TenantMeta;

  static newTenantAlias(values: Partial<TenantAlias>): TenantAlias {
    const instance = new TenantAlias();
    instance.initialize(values);
    return instance;
  }
}
