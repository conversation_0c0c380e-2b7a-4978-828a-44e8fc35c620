import {
  Cascade,
  Collection,
  Connection,
  Entity,
  EntityManager,
  IDatabaseDriver,
  ManyToOne,
  OneToMany,
  Property,
  Unique,
} from '@mikro-orm/core';
import { Authorized, Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { TenantAlias } from './TenantAlias';
import { User } from './User';
import { TenantMeta } from './TenantMeta';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { Stakeholder } from './Stakeholder';
import { Series } from 'core/utils/Async';
import { Category } from './Category';
import { Attachment } from './Attachment';
import { Opportunity } from './Opportunity';
import { Submission } from './Submission';
import { ApplicationMeta } from './ApplicationMeta';
import { Project } from './Project';
import { PrivilegeGroup } from './PrivilegeGroup';
import { Exclude, Expose } from 'class-transformer';
import { Owner } from './Owner';

@ObjectType()
@Entity()
@Exclude()
export class Tenant extends CoreEntity {
  @Unique()
  @Field()
  @Property()
  @Expose()
  name!: string;
  @Unique()
  @Field()
  @Property()
  @Expose()
  handle!: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  label?: string;
  @Authorized(RoleNames.CURATOR)
  @OneToMany(() => Opportunity, (o: Opportunity) => o.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  opportunities = new Collection<Opportunity>(this);
  @Authorized(RoleNames.CURATOR)
  @OneToMany(() => Submission, (s: Submission) => s.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  submission = new Collection<Submission>(this);
  @Authorized(RoleNames.CURATOR)
  @OneToMany(() => Project, (p: Project) => p.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  project = new Collection<Project>(this);
  @Authorized(RoleNames.ADMIN)
  @OneToMany(() => User, (u: User) => u.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  users = new Collection<User>(this);

  @OneToMany(() => TenantAlias, (t: TenantAlias) => t.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  aliases = new Collection<TenantAlias>(this);

  @Field(() => TenantMeta, { nullable: true })
  @ManyToOne(() => TenantMeta, { nullable: true })
  meta?: TenantMeta;

  @OneToMany(() => Stakeholder, (s: Stakeholder) => s.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  stakeholders = new Collection<Stakeholder>(this);

  @OneToMany(() => Category, (c: Category) => c.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  categories = new Collection<Category>(this);

  @OneToMany(() => Attachment, (a: Attachment) => a.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  attachments = new Collection<Attachment>(this);

  @OneToMany(() => PrivilegeGroup, (p: PrivilegeGroup) => p.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  privilegeGroups = new Collection<PrivilegeGroup>(this);

  @OneToMany(() => Owner, (s: Owner) => s.tenant, { orphanRemoval: true, cascade: [Cascade.ALL] })
  owners = new Collection<Owner>(this);

  // @TODO Replace this with onDelete: 'CASCADE' on the other side of the relationship
  // @TODO remove orphans - still need to add deletion of all items here for a comprehensive delete
  async removeAllAssociations(em: EntityManager<IDatabaseDriver<Connection>>): Promise<void> {
    await this.meta?.init();
    if (this.meta) {
      await this.meta.tenants.init();
      this.meta.tenants.remove(this);
    }
    const appMetaList = await em.getRepository(ApplicationMeta).find({ tenant: { id: this.id } });
    await Series.forEach(appMetaList, async (appMeta: ApplicationMeta) => {
      em.removeAndFlush(appMeta);
    });
  }

  static newTenant(values: Partial<Tenant>): Tenant {
    // @TODO
    // need to check here for existing tenant using 'toLowerCase'
    // tenant names should be unique regardless of casing...
    const instance = new Tenant();
    instance.initialize(values);
    return instance;
  }
}
