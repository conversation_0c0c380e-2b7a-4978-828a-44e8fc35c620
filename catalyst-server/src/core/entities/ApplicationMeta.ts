import { Entity, ManyToOne, Property } from '@mikro-orm/core';
import { JSONType } from 'core/contracts/types/JSONType';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Tenant } from './Tenant';

@ObjectType({ simpleResolvers: true })
@Entity()
export class ApplicationMeta extends CoreEntity {
  @ManyToOne(() => Tenant)
  tenant!: Tenant;
  @Field(() => JSONType, { nullable: true })
  @Property({ type: 'json', nullable: true })
  curationMeta?: Record<string, unknown>;

  static newApplicationMeta(values: Partial<ApplicationMeta>): ApplicationMeta {
    const instance = new ApplicationMeta();
    instance.initialize(values);
    return instance;
  }
}
