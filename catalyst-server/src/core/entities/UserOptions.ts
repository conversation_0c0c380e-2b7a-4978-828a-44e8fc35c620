import { Field, ObjectType } from 'type-graphql';

@ObjectType({ simpleResolvers: true })
export class UserOptions {
  @Field({ nullable: true })
  lastUsedServerVersion?: string;
  @Field({ nullable: true })
  cookieAcceptance?: boolean;
  @Field({ nullable: true })
  submissionEmailOptOut?: boolean;
  @Field({ nullable: true })
  optOutOtherContact?: boolean;
  @Field({ nullable: true })
  optOutTeamUpdates?: boolean;
  @Field({ nullable: true })
  optOutAll?: boolean;
}
