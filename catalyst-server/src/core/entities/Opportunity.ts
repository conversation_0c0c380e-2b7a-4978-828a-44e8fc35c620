import {
  Cascade,
  Collection,
  Entity,
  Enum,
  Formula,
  ManyToMany,
  ManyToOne,
  OneToMany,
  Property,
  QueryOrder,
} from '@mikro-orm/core';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';
import { Field, Int, ObjectType } from 'type-graphql';
import { Attachment } from './Attachment';
import { Category } from './Category';
import { CoreEntity } from './CoreEntity';
import { ExistingSolution } from './ExistingSolution';
import { Project } from './Project';
import { Stakeholder } from './Stakeholder';
import { Submission } from './Submission';
import { User } from './User';
import { Tenant } from './Tenant';
import { RelatedOpportunity } from './RelatedOpportunity';
import { RelatedOpportunityType } from 'core/contracts/enums/RelatedOpportunityType';
import { Exclude, Expose, Type } from 'class-transformer';
import { OpportunityVisibility } from 'core/contracts/enums/OpportunityVisibility';
import { OpportunityOwner } from './OpportunityOwner';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
export class Opportunity extends CoreEntity {
  @Field(() => Date, { nullable: true })
  @Property({ nullable: true })
  @Expose()
  public lastCurated?: Date;

  @Field()
  @Property()
  @Expose()
  title!: string;

  @Field()
  @Property({ columnType: 'text' })
  @Expose()
  statement!: string;

  @Field()
  @Property({ columnType: 'text' })
  @Expose()
  context!: string;

  @Field(() => OpportunityStatus)
  @Enum(() => OpportunityStatus)
  @Expose()
  status: OpportunityStatus = OpportunityStatus.PENDING;

  @Field(() => OpportunityVisibility)
  @Enum(() => OpportunityVisibility)
  @Expose()
  visibility: OpportunityVisibility = OpportunityVisibility.ALL;

  @Field()
  @Property()
  @Expose()
  isTiCLOE: boolean = false;

  // optional
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org1?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org2?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org3?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org4?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  benefits?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  solutionConcepts?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  additionalNotes?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  campaign?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  campaignNotes?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  function?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  statusNotes?: string;
  @Field(() => Int, { nullable: true })
  @Property({ nullable: true })
  @Expose()
  priority?: number = 0;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  priorityNotes?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  solutionPathway?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  solutionPathwayDetails?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  permission?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  attachmentNotes?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  initiatives?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  endorsements?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  armyModernizationPriority?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  echelonApplicability?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  transitionInContactLineOfEffort?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  operationalRules?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  capabilityArea?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  feasibilitySummary?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  materielSolutionType?: string;

  @Field(() => [String], { nullable: true })
  @Property({ type: 'text[]', nullable: true })
  @Expose()
  DOTMLPFPPChange?: string[];

  @Field(() => Tenant, { nullable: true })
  @ManyToOne(() => Tenant, { nullable: true })
  @Expose()
  @Type(() => Tenant)
  tenant?: Tenant;

  @Field(() => User, { nullable: true })
  @ManyToOne(() => User, { nullable: true })
  @Expose()
  @Type(() => User)
  user?: User;

  @Field(() => [OpportunityOwner])
  @OneToMany(() => OpportunityOwner, (opOwner) => opOwner.opportunity)
  owners = new Collection<OpportunityOwner>(this);

  @Field(() => [Attachment])
  @OneToMany(() => Attachment, (a: Attachment) => a.opportunity, { cascade: [Cascade.MERGE, Cascade.PERSIST] })
  attachments = new Collection<Attachment>(this);

  @Expose({ name: 'attachments' })
  @Type(() => Attachment)
  get attachementsToArray(): Attachment[] {
    return this.attachments.getItems();
  }

  @Field(() => [ExistingSolution])
  @OneToMany(() => ExistingSolution, (es: ExistingSolution) => es.opportunity, {
    cascade: [Cascade.MERGE, Cascade.PERSIST],
  })
  existingSolutions = new Collection<ExistingSolution>(this);

  @Expose({ name: 'existingSolutions' })
  @Type(() => ExistingSolution)
  get existingSolutionsToArray(): ExistingSolution[] {
    return this.existingSolutions.getItems();
  }

  @Field(() => [Submission])
  @ManyToMany(() => Submission, (s: Submission) => s.opportunities, { owner: true })
  submissions = new Collection<Submission>(this);

  @Expose({ name: 'submissions' })
  @Type(() => Submission)
  get submissionsToArray(): Submission[] {
    return this.submissions.getItems();
  }

  @Field(() => [Project])
  @ManyToMany(() => Project, (p: Project) => p.opportunities)
  projects = new Collection<Project>(this);

  @Field(() => [Opportunity])
  @ManyToMany(() => Opportunity)
  opportunities = new Collection<Opportunity>(this);

  @Field(() => [Category])
  //should be unique, but mikro bug in 5.1 causes error on delete when set to unique
  @ManyToMany(() => Category, (c: Category) => c.opportunities, { owner: true })
  categories = new Collection<Category>(this);

  @Expose({ name: 'categories' })
  @Type(() => Category)
  get categoriesToArray(): Category[] {
    return this.categories.getItems();
  }

  @Field(() => [Stakeholder])
  //should be unique, but mikro bug in 5.1 causes error on delete when set to unique
  @ManyToMany(() => Stakeholder, (s: Stakeholder) => s.opportunities, { owner: true })
  stakeholders = new Collection<Stakeholder>(this);

  @Expose({ name: 'stakeholders' })
  @Type(() => Stakeholder)
  get stakeholdersToArray(): Stakeholder[] {
    return this.stakeholders.getItems();
  }

  @Field(() => [RelatedOpportunity])
  @OneToMany(() => RelatedOpportunity, (r: RelatedOpportunity) => r.source, {
    orphanRemoval: true,
    cascade: [Cascade.ALL],
    orderBy: { orderBy: QueryOrder.ASC },
  })
  ownedOpportunities = new Collection<RelatedOpportunity>(this);

  @Field(() => [RelatedOpportunity])
  @OneToMany(() => RelatedOpportunity, (r: RelatedOpportunity) => r.target, {
    orphanRemoval: true,
    cascade: [Cascade.ALL],
    orderBy: { orderBy: QueryOrder.ASC },
  })
  owningOpportunities = new Collection<RelatedOpportunity>(this);

  @Field(() => Int, { defaultValue: 0 })
  @Formula(
    (alias) =>
      `(select count(*) from related_opportunity ro where ro.source_id = ${alias}.id OR ro.target_id = ${alias}.id)`,
    { lazy: true },
  )
  relatedOpportunityCount = 0;

  @Field(() => Int, { defaultValue: 0 })
  @Formula(
    (alias) =>
      `(select count(*) from related_opportunity ro where (ro.source_id = ${alias}.id OR ro.target_id = ${alias}.id) AND ro.type = 'linked')`,
    { lazy: true },
  )
  linkedOpportunityCount = 0;

  @Field(() => Int, { defaultValue: 0 })
  @Formula(
    (alias) => `(select count(*) from related_opportunity ro where ro.source_id = ${alias}.id AND ro.type = 'child')`,
    { lazy: true },
  )
  childOpportunityCount = 0;

  @Field(() => Int, { defaultValue: 0 })
  @Formula(
    (alias) => `(select count(*) from related_opportunity ro where ro.target_id = ${alias}.id AND ro.type = 'child')`,
    { lazy: true },
  )
  parentOpportunityCount = 0;

  // helpers
  //@TODO consider delivering these to the client via a filter query

  @Expose()
  get linkedOpportunityIds(): string[] {
    return this.ownedOpportunities
      .getItems()
      .filter((ownedOpportunity) => ownedOpportunity.type === RelatedOpportunityType.LINKED)
      .map((ownedOpportunity) => ownedOpportunity.target.id)
      .concat(
        this.owningOpportunities
          .getItems()
          .filter((owningOpportunity) => owningOpportunity.type === RelatedOpportunityType.LINKED)
          .map((owningOpportunity) => owningOpportunity.source.id),
      );
  }

  @Expose()
  get parentOpportunityIds(): string[] {
    return this.owningOpportunities
      .getItems()
      .filter((owningOpportunity) => owningOpportunity.type === RelatedOpportunityType.CHILD)
      .map((owningOpportunity) => owningOpportunity.source.id);
  }

  @Expose()
  get childOpportunityIds(): string[] {
    return this.ownedOpportunities
      .getItems()
      .filter((ownedOpportunity) => ownedOpportunity.type === RelatedOpportunityType.CHILD)
      .map((ownedOpportunity) => ownedOpportunity.target.id);
  }

  get parentOpportunities(): Opportunity[] {
    return this.owningOpportunities
      .getItems()
      .filter((owningOpportunity) => owningOpportunity.type === RelatedOpportunityType.CHILD)
      .map((owningOpportunity) => owningOpportunity.source);
  }

  get childOpportunities(): Opportunity[] {
    return this.ownedOpportunities
      .getItems()
      .filter((ownedOpportunity) => ownedOpportunity.type === RelatedOpportunityType.CHILD)
      .map((ownedOpportunity) => ownedOpportunity.target);
  }

  static newOpportunity(values: Partial<Opportunity>, tenant: Tenant): Opportunity {
    const instance = new Opportunity();
    instance.initialize(values);
    instance.tenant = tenant;
    return instance;
  }
}
