import { Entity, ManyToOne, Property } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';
import { User } from './User';
import { Exclude, Expose } from 'class-transformer';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
export class Link extends CoreEntity {
  @Field()
  @Property()
  @Expose()
  name!: string;
  @Field()
  @Property()
  @Expose()
  url!: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  notes!: string;
  @Field(() => User, { nullable: true })
  @ManyToOne(() => User, { nullable: true })
  @Expose()
  createdBy?: User;
  @ManyToOne(() => Opportunity, { nullable: true })
  opportunity?: Opportunity;

  static newLink(values: Partial<Link>): Link {
    const instance = new Link();
    instance.initialize(values);
    return instance;
  }
}
