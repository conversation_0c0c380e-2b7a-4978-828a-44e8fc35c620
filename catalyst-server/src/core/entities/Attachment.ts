import { Entity, ManyToOne, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';
import { Project } from './Project';
import { Tenant } from './Tenant';
import { User } from './User';
import { Exclude, Expose } from 'class-transformer';

// An attachment is intended to be associated with either an opportunity or a project, but not both
// It is a shallow pointer to a remote file, and 2 attachments can certainly point to the same remote artifact
@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
export class Attachment extends CoreEntity {
  @Field()
  @Property()
  @Expose()
  name!: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  displayName!: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  notes!: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  encoding?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  mimetype?: string;
  @Field(() => User, { nullable: true })
  @ManyToOne(() => User, { nullable: true })
  @Expose()
  createdBy?: User;
  @ManyToOne(() => Opportunity, { nullable: true })
  opportunity?: Opportunity;
  @ManyToOne(() => Project, { nullable: true })
  project?: Project;
  @Property()
  location!: string;
  @ManyToOne(() => Tenant, { deleteRule: 'cascade' })
  tenant!: Tenant;

  static newAttachment(values: Partial<Attachment>): Attachment {
    const instance = new Attachment();
    instance.initialize(values);
    return instance;
  }
}
