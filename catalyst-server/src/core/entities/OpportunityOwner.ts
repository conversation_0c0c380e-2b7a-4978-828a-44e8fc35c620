import { <PERSON>ti<PERSON>, Enum, ManyToOne, PrimaryKey, Property } from '@mikro-orm/core';
import { Opportunity } from './Opportunity';
import { CoreEntity } from './CoreEntity';
import { Field, ObjectType } from 'type-graphql';
import { Expose } from 'class-transformer';
import { Owner } from './Owner';
import { OpportunityOwnerStatus } from 'core/contracts/enums/OpportunityOwnerStatus';

@ObjectType({ simpleResolvers: true })
@Entity()
export class OpportunityOwner extends CoreEntity {
  @Field()
  @Expose()
  @PrimaryKey()
  id!: string;

  @ManyToOne(() => Owner)
  @Field(() => Owner)
  owner!: Owner;

  @ManyToOne(() => Opportunity)
  @Field(() => Opportunity)
  opportunity!: Opportunity;

  @Field(() => Date)
  @Property()
  @Expose()
  addedAt: Date = new Date();

  @Field(() => Date, { nullable: true })
  @Property({ nullable: true })
  @Expose()
  madePreviousAt?: Date;

  @Field(() => OpportunityOwnerStatus)
  @Enum(() => OpportunityOwnerStatus)
  @Property()
  @Expose()
  status: OpportunityOwnerStatus = OpportunityOwnerStatus.CURRENT;

  @Field(() => Date, { nullable: true })
  @Property({ nullable: true })
  @Expose()
  removedAt?: Date;

  @Field(() => Boolean)
  @Property({ default: false })
  @Expose()
  isRemoved: boolean = false;

  static newOwner(values: Partial<OpportunityOwner>): OpportunityOwner {
    const instance = new OpportunityOwner();
    Object.assign(instance, values);
    return instance;
  }
}
