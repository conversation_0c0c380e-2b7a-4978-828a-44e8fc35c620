import { Cascade, Collection, Entity, ManyToMany, ManyToOne, OneToMany, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Tenant } from './Tenant';
import { Privilege } from './Privilege';
import { Resource } from 'core/contracts/input/base/CommonInput';
import { User } from './User';
import { Exclude, Expose, Type } from 'class-transformer';

@ObjectType({ simpleResolvers: true })
@Entity()
@Unique({ properties: ['name', 'tenant'] })
@Exclude()
export class PrivilegeGroup extends CoreEntity {
  @Field({})
  @Property({})
  @Expose()
  name!: string;

  @ManyToOne(() => Tenant, { deleteRule: 'cascade' })
  tenant!: Tenant;

  @Field(() => [Privilege], { nullable: true })
  @OneToMany(() => Privilege, (p: Privilege) => p.privilegeGroup, {
    orphanRemoval: true,
    cascade: [Cascade.ALL],
    orderBy: { name: 'ASC' },
  })
  privileges = new Collection<Privilege>(this);

  @Expose({ name: 'privileges' })
  @Type(() => Privilege)
  privilegesToArray(): Privilege[] {
    return this.privileges.getItems();
  }

  @Field(() => [User], { nullable: true })
  @ManyToMany(() => User, (user) => user.privilegeGroups)
  users = new Collection<User>(this);

  hasPrivilege(resource: Resource): boolean {
    return this.privileges.getItems().some((privilege) => privilege.hasPrivilege(resource));
  }

  getPrivilege(resource: Resource): Privilege | undefined {
    return this.privileges.getItems().find((privilege) => privilege.hasPrivilege(resource));
  }

  static newPrivilegeGroup(values: Partial<Privilege>, tenant: Tenant): PrivilegeGroup {
    const instance = new PrivilegeGroup();
    instance.initialize(values);
    instance.tenant = tenant;
    return instance;
  }
}
