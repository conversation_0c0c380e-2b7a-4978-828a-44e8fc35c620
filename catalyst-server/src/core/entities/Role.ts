import { Collection, Entity, EntityManager, Enum, ManyToMany, Property, Unique } from '@mikro-orm/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { User } from './User';

@ObjectType({ simpleResolvers: true })
@Entity()
export class Role extends CoreEntity {
  @Unique()
  @Field(() => RoleNames)
  @Enum(() => RoleNames)
  name!: RoleNames;

  /*
  @Field()
  @Property()
  displayName!: RoleNames;
  */

  @ManyToMany(() => User, (u: User) => u.roles)
  users = new Collection<User>(this);

  static async rolesForRoleNames(em: EntityManager, roleNames: RoleNames[]): Promise<Role[]> {
    const roles = await em.getRepository(Role).findAll();
    return roleNames.map((roleName) => {
      const role = roles.find((role) => role.name === roleName);
      if (!role) throw Error(`Role ${roleName} not found in database`);
      return role;
    });
  }

  static newRole(values: Partial<Role>): Role {
    const instance = new Role();
    instance.initialize(values);
    return instance;
  }

  private constructor() {
    super();
  }
}
