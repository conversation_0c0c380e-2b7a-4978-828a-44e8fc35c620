import { Cascade, Collection, Entity, ManyToMany, ManyToOne, OneToMany, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';
import { ProjectStakeholder } from './ProjectStakeholder';
import { Tenant } from './Tenant';
import { Exclude, Expose } from 'class-transformer';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
@Unique({ properties: ['name', 'org', 'tenant'] })
export class Stakeholder extends CoreEntity {
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  name?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org?: string;
  @ManyToOne(() => Tenant, { deleteRule: 'cascade' })
  tenant!: Tenant;
  @Field(() => [Opportunity])
  @ManyToMany(() => Opportunity, (o: Opportunity) => o.stakeholders)
  opportunities = new Collection<Opportunity>(this);

  @OneToMany(() => ProjectStakeholder, (p: ProjectStakeholder) => p.stakeholder, {
    orphanRemoval: true,
    cascade: [Cascade.ALL],
  })
  projectStakeholders = new Collection<ProjectStakeholder>(this);

  async removeAllAssociations(): Promise<void> {
    await this.projectStakeholders.init();
    this.projectStakeholders.removeAll();
  }

  static newStakeholder(values: Partial<Stakeholder>): Stakeholder {
    const instance = new Stakeholder();
    instance.initialize(values);
    return instance;
  }
}
