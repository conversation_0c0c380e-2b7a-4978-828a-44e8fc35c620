import { Collection, Entity, ManyToMany, ManyToOne, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';
import { Project } from './Project';
import { Tenant } from './Tenant';
import { Exclude, Expose } from 'class-transformer';

@ObjectType({ simpleResolvers: true })
@Entity()
@Unique({ properties: ['name', 'tenant'] })
@Exclude()
export class Category extends CoreEntity {
  @Property()
  @Field()
  @Expose()
  name!: string;
  @ManyToOne(() => Tenant, { deleteRule: 'cascade' })
  tenant!: Tenant;
  @Field(() => [Opportunity])
  @ManyToMany(() => Opportunity, (o: Opportunity) => o.categories)
  opportunities = new Collection<Opportunity>(this);
  @Field(() => [Project])
  @ManyToMany(() => Project, (p: Project) => p.categories)
  projects = new Collection<Project>(this);

  static newCategory(values: Partial<Category>): Category {
    const instance = new Category();
    instance.initialize(values);
    return instance;
  }
}
