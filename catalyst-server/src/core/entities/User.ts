import { Collection, Entity, Enum, ManyToMany, ManyToOne, OneToMany, Property, Unique } from '@mikro-orm/core';
import bcrypt from 'bcrypt';
import { Exclude, Expose, Type } from 'class-transformer';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { Field, ObjectType } from 'type-graphql';
import { ApplicationMeta } from './ApplicationMeta';
import { CoreEntity } from './CoreEntity';
import { CurationEvent } from './CurationEvent';
import { Opportunity } from './Opportunity';
import { PrivilegeGroup } from './PrivilegeGroup';
import { Project } from './Project';
import { Role } from './Role';
import { Submission } from './Submission';
import { Tenant } from './Tenant';
import { UserOptions } from './UserOptions';

@ObjectType({ simpleResolvers: true })
@Entity()
@Unique({ properties: ['emailAddress', 'status', 'tenant'] })
@Exclude()
export class User extends CoreEntity {
  @Field()
  @Property()
  @Expose()
  emailAddress!: string;

  @Property({ nullable: true, hidden: true })
  private _password?: string;

  @Property({ persist: false })
  set password(password: string) {
    this._password = bcrypt.hashSync(password, 12);
  }

  @Field(() => VerifiedStatus)
  @Enum(() => VerifiedStatus)
  status: VerifiedStatus = VerifiedStatus.UNVERIFIED;

  @Field()
  @Property()
  @Expose()
  firstName!: string;
  @Field()
  @Property()
  @Expose()
  lastName!: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org1?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org2?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org3?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org4?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  phone?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  altContact?: string;

  @Field(() => UserOptions)
  @Property({ type: 'json' })
  options: UserOptions = {};

  @Field(() => ApplicationMeta, { nullable: true })
  @ManyToOne(() => ApplicationMeta, { nullable: true })
  appMeta?: ApplicationMeta;

  @Field(() => [Submission])
  @OneToMany(() => Submission, (s: Submission) => s.user)
  submissions = new Collection<Submission>(this);

  @ManyToOne(() => Tenant)
  tenant!: Tenant;

  @Field(() => [CurationEvent])
  @OneToMany(() => CurationEvent, (c: CurationEvent) => c.user)
  curationEvent = new Collection<CurationEvent>(this);

  @Field(() => [Role])
  @ManyToMany(() => Role, (r: Role) => r.users, { owner: true })
  roles = new Collection<Role>(this);

  @Field(() => [PrivilegeGroup])
  @ManyToMany(() => PrivilegeGroup)
  privilegeGroups = new Collection<PrivilegeGroup>(this);

  @Expose({ name: 'privilegeGroups', groups: ['user_full'] })
  @Type(() => PrivilegeGroup)
  privilegeGroupsToArray(): PrivilegeGroup[] {
    return this.privilegeGroups.getItems();
  }

  @OneToMany(() => Opportunity, (o: Opportunity) => o.user)
  opportunities = new Collection<Opportunity>(this);

  @OneToMany(() => Project, (p: Project) => p.creator)
  projects = new Collection<Project>(this);

  // @TODO remove orphans - still need to add deletion of all items here for a comprehensive delete
  async removeAllAssociations(): Promise<void> {
    await this.submissions.init();
    this.submissions.removeAll();
    await this.opportunities.init();
    this.opportunities.removeAll();
    await this.projects.init();
    this.projects.removeAll();
  }

  async isValidPassword(password: string): Promise<boolean> {
    if (!this._password) return false;
    return bcrypt.compare(password, this._password);
  }

  static newUser(values: Partial<User>): User {
    const instance = new User();
    instance.initialize(values);
    return instance;
  }

  constructor() {
    super();
  }
}
