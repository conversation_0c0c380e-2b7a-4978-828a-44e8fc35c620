import { Collection, Entity, OneToMany, Property } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { JSONType } from 'core/contracts/types/JSONType';
import { CoreEntity } from './CoreEntity';
import { Tenant } from './Tenant';
import { TenantAlias } from './TenantAlias';
@ObjectType({ simpleResolvers: true })
@Entity()
export class TenantMeta extends CoreEntity {
  @Field(() => JSONType, { nullable: true })
  @Property({ type: 'json', nullable: true })
  config?: Record<string, unknown>;

  @Field(() => JSONType, { nullable: true })
  @Property({ type: 'json', nullable: true })
  theme?: Record<string, unknown>;
  
  @Field(() => JSONType, { nullable: true })
  @Property({ type: 'json', nullable: true })
  content?: Record<string, unknown>;

  @OneToMany(() => Tenant, (t: Tenant) => t.meta)
  //should be unique, but mikro bug in 5.1 causes error on delete when set to unique
  tenants = new Collection<Tenant>(this);

  @OneToMany(() => TenantAlias, (t: TenantAlias) => t.meta)
  //should be unique, but mikro bug in 5.1 causes error on delete when set to unique
  tenantAliases = new Collection<TenantAlias>(this);

  static newTenantMeta(values: Partial<TenantMeta>): TenantMeta {
    const instance = new TenantMeta();
    instance.initialize(values);
    return instance;
  }

  private constructor() {
    super();
  }

  @Field(() => Boolean)
  @Property({ persist: false})
  get filterOtherPrivateOpportunities(){
    const serverConfiguration = this.serverConfig;
    return serverConfiguration ? serverConfiguration.filterOtherPrivateOpportunities : true;
  }

  @Field(() => JSONType, { nullable: true })
  @Property({ persist: false})
  get serverConfiguration(){
    return this.serverConfig;
  }
  
  // We do not want to expose this field to the graph and is the reason why it
  // does not contain the decorator @Field. This should only be used on the server side
  // and not marshalled out for edits until it can be controlled.
  @Property({ type: 'json', nullable: true })
  serverConfig?: Record<string, unknown>;
}
