import { Collection, Entity, ManyToOne, OneToMany, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { OpportunityOwner } from './OpportunityOwner';
import { Expose } from 'class-transformer';
import { Tenant } from './Tenant';

@ObjectType()
@Entity()
export class Owner extends CoreEntity {
  @PrimaryKey()
  @Field()
  id!: string;

  @Field()
  @Property()
  @Expose()
  emailAddress!: string;

  @Field()
  @Property()
  @Expose()
  firstName!: string;

  @Field()
  @Property()
  @Expose()
  lastName!: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org1?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org2?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org3?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org4?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  phone?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  altContact?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  organizationRole?: string;

  @OneToMany(() => OpportunityOwner, (opOwner) => opOwner.owner)
  opportunityLinks = new Collection<OpportunityOwner>(this);
}
