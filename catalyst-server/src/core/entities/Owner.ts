import { Collection, Entity, ManyToOne, OneToMany, OneToOne, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { Expose, Type } from 'class-transformer';
import { CoreEntity } from './CoreEntity';
import { User } from './User';
import { Tenant } from './Tenant';
import { OpportunityOwnerStatus } from './OpportunityOwnerStatus';

@ObjectType({ simpleResolvers: true })
@Entity()
export class Owner extends CoreEntity {
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  organizationRole?: string;

  @Field(() => [OpportunityOwnerStatus], { nullable: true })
  @OneToMany(() => OpportunityOwnerStatus, (opOwner) => opOwner.owner)
  opportunityOwnerStatuses = new Collection<OpportunityOwnerStatus>(this);

  @Field(() => User)
  @OneToOne(() => User, { deleteRule: 'cascade' })
  @Expose()
  @Type(() => User)
  user!: User;

  @ManyToOne(() => Tenant)
  tenant!: Tenant;

  static newOwner(values: Partial<Owner>): Owner {
    const instance = new Owner();
    instance.initialize(values);
    return instance;
  }
}
