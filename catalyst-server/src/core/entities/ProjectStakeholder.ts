import { <PERSON><PERSON>ty, EntityData, Enum, ManyToOne, Property, Unique } from '@mikro-orm/core';
import { ProjectStakeholderType } from 'core/contracts/enums/ProjectStakeholderType';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Project } from './Project';
import { Stakeholder } from './Stakeholder';

@ObjectType({ simpleResolvers: true })
@Entity()
// Note: disabling unique indexes to allow for transactional replace of entire set
// @Unique({ properties: ['project', 'stakeholder', 'type'] })
// @Unique({ properties: ['project', 'orderBy'] })
export class ProjectStakeholder extends CoreEntity {
  @Field(() => Project)
  @ManyToOne(() => Project, { deleteRule: 'cascade' })
  project!: Project;

  @Field(() => Stakeholder)
  @ManyToOne(() => Stakeholder, { deleteRule: 'cascade' })
  stakeholder!: Stakeholder;

  @Field(() => ProjectStakeholderType)
  @Enum(() => ProjectStakeholderType)
  type!: ProjectStakeholderType;

  // a 'weight' value used for ordering on retrieval
  @Property()
  orderBy!: number;

  static newProjectStakeholder(values: Partial<ProjectStakeholder>): ProjectStakeholder {
    const instance = new ProjectStakeholder();
    instance.initialize(values);
    return instance;
  }
}
