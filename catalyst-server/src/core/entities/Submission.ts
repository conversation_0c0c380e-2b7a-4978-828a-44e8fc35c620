import { Collection, Entity, ManyToMany, ManyToOne, OneToMany, Property } from '@mikro-orm/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { Authorized, Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';
import { User } from './User';
import { Tenant } from './Tenant';
import { Exclude, Expose } from 'class-transformer';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
export class Submission extends CoreEntity {
  @Field()
  @Property()
  @Expose()
  title!: string;

  @Field()
  @Property({ columnType: 'text' })
  @Expose()
  statement!: string;

  @Field()
  @Property({ columnType: 'text' })
  @Expose()
  context!: string;

  // optional
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  benefits?: string;
  @Field({ nullable: true })
  @Property({ columnType: 'text', nullable: true })
  @Expose()
  solutionConcepts?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  campaign?: string;
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  function?: string;

  @ManyToOne(() => Tenant, { nullable: true })
  tenant?: Tenant;

  @Authorized(RoleNames.CURATOR)
  @Field(() => User, { nullable: true })
  @ManyToOne(() => User, { nullable: true })
  user?: User;

  @Authorized(RoleNames.CURATOR)
  @Field(() => [Opportunity])
  @ManyToMany(() => Opportunity, (o: Opportunity) => o.submissions)
  opportunities = new Collection<Opportunity>(this);

  static newSubmission(values: Partial<Submission>, tenant: Tenant): Submission {
    const instance = new Submission();
    instance.initialize(values);
    instance.tenant = tenant;
    return instance;
  }
}
