import {
  Collection,
  Connection,
  EntityManager,
  FilterQuery,
  IDatabaseDriver,
  QueryOrder,
  QueryOrderMap,
} from '@mikro-orm/core';
import { FilterValue, ObjectQuery } from '@mikro-orm/core/typings';
import { LogicalOperator } from 'core/contracts/enums/LogicalOperator';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { UpdateOperator } from 'core/contracts/enums/UpdateOperator';
import {
  PagingInput,
  SearchField,
  JsonSearchGroup,
  SortField,
  UpdateLinks,
  Scope,
} from 'core/contracts/input/base/CommonInput';
import { PageInfo } from 'core/contracts/output/Page';
import { CoreEntity } from 'core/entities/CoreEntity';
import { Series } from 'core/utils/Async';

export const DEFAULT_PAGE_SIZE = 100;
export const MAX_PAGE_SIZE = 1000;

export function getQueryBounds(pagingInput?: PagingInput): { limit: number; offset: number } {
  const limit = pagingInput?.pageSize || DEFAULT_PAGE_SIZE;
  if (limit > MAX_PAGE_SIZE) throw new Error(`pageSize cannot exceed ${MAX_PAGE_SIZE}`);
  const offset: number = pagingInput?.cursor ? parseInt(pagingInput.cursor) : 0;
  return { limit, offset };
}

/**
 *
 * @param totalCount - How many rows are there overall to page across
 * @param resultsCount - How many rows did we retrieve this time
 * @param limit - The max number of rows allowed for this query
 * @param offset - How many rows to 'skip' (or the zero-based start index of the rows)
 * @returns PageInfo
 */
export function getPageInfo(totalCount: number, resultsCount: number, limit: number, offset: number): PageInfo {
  const retrievedCount = resultsCount;
  const hasNext = totalCount > offset + resultsCount;
  const hasPrevious = offset != 0;
  const lastCursor = offset.toString();
  const lastPageSize = limit;
  return { totalCount, retrievedCount, hasNext, hasPrevious, lastCursor, lastPageSize };
}

export function getEmptyPageInfo(): PageInfo {
  return { totalCount: 0, retrievedCount: 0, hasNext: false, hasPrevious: false, lastCursor: '0', lastPageSize: 0 };
}

export function getSearchFilter<T>(searchFields?: SearchField[]): FilterQuery<T>[] {
  if (!searchFields) return [];
  return searchFields.reduce<FilterQuery<T>[]>((results, searchField) => {
    const filterValue = getSearchFilterValues<T>(searchField.fieldNames, searchField.searchValue, searchField.operator);
    if (filterValue) results.push(filterValue);
    return results;
  }, []);
}

export function getJsonSearchGroupFilter<T>(searchGroups?: JsonSearchGroup[]): FilterQuery<T>[] {
  if (!searchGroups) return [];
  return searchGroups.reduce<FilterQuery<T>[]>((results, searchGroup) => {
    const filterValue = getJsonSearchGroup<T>(searchGroup);
    if (filterValue) results.push(filterValue);
    return results;
  }, []);
}

export function getSortFilter<T>(sortFields?: SortField[], defaultSort?: SortField): QueryOrderMap<T> {
  return getSortFilterValues<T>(sortFields, defaultSort);
}

export function combineSortFields<T>(
  sortFields?: SortField[],
  secondarySortFields?: SortField[],
  defaultSortFields?: SortField[],
): SortField[] {
  const primaryFields = sortFields || defaultSortFields || [];
  // remove any secondary fields that are already primary fields
  const secondaryFields = secondarySortFields?.filter(
    (secondaryField) => !primaryFields.find((primaryField) => primaryField.fieldName === secondaryField.fieldName),
  );
  return [...primaryFields, ...(secondaryFields || [])];
}

export function defaultSecondarySortFields<T>(): SortField[] {
  return [
    { fieldName: 'updatedAt', ascending: false },
    { fieldName: 'id', ascending: true },
  ];
}

export function caseInsensitiveMatchValue(value = ''): FilterValue<string> {
  return { $re: `(?i)^${value}$` };
}

export async function handleUpdateLinks<E extends CoreEntity, C extends CoreEntity>(
  em: EntityManager<IDatabaseDriver<Connection>>,
  entity: E,
  collectionName: string,
  relations: string[],
  classType: { new (): C },
  updateLinks?: UpdateLinks[],
  forceInverse = false,
): Promise<E> {
  if (updateLinks && updateLinks.length) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    for (const updateLink of updateLinks) {
      const updateItems: C[] = await em.getRepository(classType).find(updateLink.ids, relations);
      if (updateLink.operator === UpdateOperator.ADD) {
        // add items if not already there
        await Series.forEach(updateItems, async (item) => {
          await addCollectionItem(entity, collectionName, item);
          if (forceInverse) await addCollectionItem(item, collectionName, entity);
        });
      } else if (updateLink.operator === UpdateOperator.RM) {
        // remove items if present
        await Series.forEach(updateItems, async (item) => {
          await removeCollectionItem(entity, collectionName, item);
          if (forceInverse) await removeCollectionItem(item, collectionName, entity);
        });
      } else if (updateLink.operator === UpdateOperator.SET) {
        if (forceInverse) await removeAllInverse(entity, collectionName);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const collection: Collection<C, any> = (entity as any)[collectionName];
        if (!collection.isInitialized()) await collection.init();
        collection.set(updateItems);
        if (forceInverse) await addAllInverse(entity, collectionName);
      }
    }
  }
  return entity;
}

export function getFilterForResourceType(
  resourceType: ResourceType,
  scope: Scope,
): Record<string, unknown> | undefined {
  const resourceIds = scope.resources.reduce((resourceIds, resource) => {
    if (resource.resourceType === resourceType) resourceIds.push(resource.resourceId);
    return resourceIds;
  }, [] as string[]);
  if (resourceIds.length) return { id: { $in: resourceIds } };
}

// private functions

async function addCollectionItem<E extends CoreEntity, C extends CoreEntity>(
  owner: E,
  collectionName: string,
  toAdd: C,
) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const collection: Collection<C, any> = (owner as any)[collectionName];
  if (!collection.isInitialized()) await collection.init();
  const existingIds = collection.getIdentifiers('id');
  if (!existingIds.some((existingId) => toAdd.id === existingId)) {
    collection.add(toAdd);
  }
}

async function removeCollectionItem<E extends CoreEntity, C extends CoreEntity>(
  owner: E,
  collectionName: string,
  toRemove: C,
) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const collection: Collection<C, any> = (owner as any)[collectionName];
  if (!collection.isInitialized()) await collection.init();
  const existingItem = collection.getItems().find((existingItem) => existingItem.id === toRemove.id);
  if (existingItem) collection.remove(existingItem);
}

async function addAllInverse<E extends CoreEntity, C extends CoreEntity>(owner: E, collectionName: string) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const collection: Collection<C, any> = (owner as any)[collectionName];
  if (!collection.isInitialized()) await collection.init();
  await Series.forEach(collection.getItems(), async (existingItem: C) => {
    await addCollectionItem(existingItem, collectionName, owner);
  });
}

async function removeAllInverse<E extends CoreEntity, C extends CoreEntity>(owner: E, collectionName: string) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const collection: Collection<C, any> = (owner as any)[collectionName];
  if (!collection.isInitialized()) await collection.init();
  await Series.forEach(collection.getItems(), async (existingItem: C) => {
    await removeCollectionItem(existingItem, collectionName, owner);
  });
}

// types are determined here by the presence of operands or fieldNames
function getJsonSearchGroup<T>(searchFieldGroup: JsonSearchGroup | SearchField): FilterQuery<T> | undefined {
  if ((searchFieldGroup as JsonSearchGroup).operands?.length) {
    return getSearchGroupValues<T>(searchFieldGroup as JsonSearchGroup);
  }
  if ((searchFieldGroup as SearchField).fieldNames?.length) {
    const searchField = searchFieldGroup as SearchField;
    return getSearchFilterValues<T>(searchField.fieldNames, searchField.searchValue, searchField.operator);
  }
}

function getSearchGroupValues<T>(jsonSearchGroup: JsonSearchGroup): FilterQuery<T> {
  const filters = jsonSearchGroup.operands?.map((operand) => getJsonSearchGroup<T>(operand));
  const resolvedOp = jsonSearchGroup.operator === LogicalOperator.OR ? '$or' : '$and';
  return { [resolvedOp]: filters } as FilterQuery<T>;
}

function getSortFilterValues<T>(sortFields?: SortField[], defaultSort?: SortField): QueryOrderMap<T> {
  const resovledFields = !sortFields?.length ? (defaultSort ? [defaultSort] : []) : sortFields;
  return resovledFields.reduce<QueryOrderMap<T>>((results, sortField) => {
    const sortValue = getSortFilterValue<T>(sortField.fieldName, !!sortField.ascending);
    return { ...results, ...sortValue };
  }, {});
}

function getSortFilterValue<T>(fieldName: string, ascending: boolean): QueryOrderMap<T> {
  if (fieldName.indexOf('.') > -1) {
    const path = fieldName.split('.');
    if (path.length > 1) {
      let node = getSortFilterLeafValue(path[path.length - 1], ascending);
      for (let i = path.length - 2; i >= 0; i--) {
        node = { [path[i]]: node };
      }
      return node as QueryOrderMap<T>;
    }
  }
  return getSortFilterLeafValue(fieldName, ascending);
}

function getSortFilterLeafValue<T>(fieldName: string, ascending: boolean): QueryOrderMap<T> {
  const order = ascending ? QueryOrder.ASC_NULLS_FIRST : QueryOrder.DESC_NULLS_LAST;
  return { [fieldName]: order } as QueryOrderMap<T>;
}

function getSearchFilterValues<T>(
  fieldNames: string[],
  searchValue: unknown,
  operator: SearchOperator = SearchOperator.MATCH,
): FilterQuery<T> | undefined {
  const resolvedOp = operatorMap[operator];
  if (!fieldNames.length) return;
  if (fieldNames?.length > 1) {
    return {
      $or: fieldNames.map((fieldName) => getSearchFilterValue<T>(fieldName, searchValue, operator)),
    } as FilterQuery<T>;
  } else {
    return getSearchFilterValue<T>(fieldNames[0], searchValue, operator);
  }
}

// @TODO - whitelist these relational values
// when building 'path' for security's sake
function getSearchFilterValue<T>(
  fieldName: string,
  searchValue: unknown,
  operator: SearchOperator = SearchOperator.MATCH,
): FilterQuery<T> {
  if (fieldName.indexOf('.') > -1) {
    const path = fieldName.split('.');
    if (path.length > 1) {
      let node = getSearchFilterLeafValue(path[path.length - 1], searchValue, operator);
      for (let i = path.length - 2; i >= 0; i--) {
        node = { [path[i]]: node };
      }
      return node as FilterQuery<T>;
    }
  }
  return getSearchFilterLeafValue(fieldName, searchValue, operator);
}

/*
  @TODO prepare searchString for regex!
  strip out all non char and digit characters
 */
function getSearchFilterLeafValue<T>(
  fieldName: string,
  searchValue: unknown,
  operator: SearchOperator = SearchOperator.MATCH,
): FilterQuery<T> {
  const resolvedOp = operatorMap[operator];
  if (operator === SearchOperator.MATCH) {
    const cleanValue = String(searchValue).replace(/[^a-zA-Z0-9^\^^\$]/g, (match) => '\\' + match);
    return { [fieldName]: { [resolvedOp]: `(?i).*${cleanValue}.*` } } as FilterQuery<T>;
  }
  return { [fieldName]: { [resolvedOp]: searchValue } } as FilterQuery<T>;
}

const operatorMap = {
  [SearchOperator.MATCH]: '$re',
  [SearchOperator.EQ]: '$eq',
  [SearchOperator.NE]: '$ne',
  [SearchOperator.GT]: '$gt',
  [SearchOperator.GTE]: '$gte',
  [SearchOperator.LT]: '$lt',
  [SearchOperator.LTE]: '$lte',
  [SearchOperator.IN]: '$in',
  [SearchOperator.NIN]: '$nin',
  MATCH: '$re',
  EQ: '$eq',
  NE: '$ne',
  GT: '$gt',
  GTE: '$gte',
  LT: '$lt',
  LTE: '$lte',
  IN: '$in',
  NIN: '$nin',
};
