import {
  BlobClient,
  BlobSASPermissions,
  BlobServiceClient,
  generateBlobSASQueryParameters,
  StorageSharedKeyCredential,
} from '@azure/storage-blob';
import { AddAttachmentInput } from 'core/contracts/input/curator/AttachmentInput';
import { Readable } from 'stream';

export class BlobStorage {
  static STORAGE_KEY: string = process.env.STORAGE_SECRET || '';
  static STORAGE_ACCOUNT_NAME: string = process.env.STORAGE_ACCOUNT_NAME || '';
  static STORAGE_DOMAIN: string = process.env.STORAGE_DOMAIN || '';
  static MAX_BUFFERS = 20;
  static EXPIRE_URL_TIME = 1000 * 60 * 5; // 5 mins in millis

  /**
   * Create a container if it does not already exist
   * @param name container name
   * @returns whether or not the container already existed
   */
  static async createContainer(name: string): Promise<boolean> {
    const cleanName = name.toLowerCase();
    const blobServiceClient = this.getBlobServiceClient();
    const containerClient = blobServiceClient.getContainerClient(cleanName);
    const createContainerResponse = await containerClient.createIfNotExists();
    return createContainerResponse.succeeded;
  }

  /**
   * Upload a blob, creating the container if it doesn't already exist
   * @param input the attachment info
   * @param containerName container name
   * @param blobName blob name
   */
  static async uploadBlob(input: AddAttachmentInput, containerName: string, blobName: string): Promise<void> {
    const cleanContainerName = containerName.toLowerCase();
    const blobServiceClient = this.getBlobServiceClient();
    const containerClient = blobServiceClient.getContainerClient(cleanContainerName);
    await containerClient.createIfNotExists();
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    const stream = input.createReadStream();
    const response = await blockBlobClient.uploadStream(stream, stream.readableHighWaterMark, this.MAX_BUFFERS, {
      blobHTTPHeaders: { blobContentType: input.mimetype, blobContentEncoding: input.encoding },
    });
  }

  /**
   * Get a secure temporary url to retrieve the content
   * @param containerName the container name
   * @param blobName the blob name
   * @returns the url
   */
  static async getBlobUrl(containerName: string, blobName: string): Promise<string> {
    const cleanContainerName = containerName.toLowerCase();
    const now = Date.now();
    const blobServiceClient = this.getBlobServiceClient();
    const containerClient = blobServiceClient.getContainerClient(cleanContainerName);
    const credential = this.getCredential();
    const sasToken = generateBlobSASQueryParameters(
      {
        containerName: cleanContainerName,
        blobName,
        permissions: BlobSASPermissions.parse('r'),
        startsOn: new Date(now),
        expiresOn: new Date(now + this.EXPIRE_URL_TIME),
      },
      credential,
    ).toString();
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    return `${blockBlobClient.url}?${sasToken}`;
  }

  static async deleteBlob(containerName: string, blobName: string): Promise<void> {
    const cleanContainerName = containerName.toLowerCase();
    const blobServiceClient = this.getBlobServiceClient();
    const containerClient = blobServiceClient.getContainerClient(cleanContainerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    await blockBlobClient.deleteIfExists();
  }

  private static getBlobServiceClient(): BlobServiceClient {
    return new BlobServiceClient(`https://${this.STORAGE_ACCOUNT_NAME}.${this.STORAGE_DOMAIN}`, this.getCredential());
  }

  private static getCredential(): StorageSharedKeyCredential {
    return new StorageSharedKeyCredential(this.STORAGE_ACCOUNT_NAME, this.STORAGE_KEY);
  }
}
