import { AppContext } from 'core/core';
import { JwtPayload, PayloadKeys } from 'core/auth/JwtPayload';
import { AuthC<PERSON><PERSON> } from 'type-graphql';
import jwt from 'jsonwebtoken';
import { roleMap } from 'core/contracts/enums/RoleNames';
import { Resource, Scope } from 'core/contracts/input/base/CommonInput';
import { EntityManager } from '@mikro-orm/core';
import { UserController } from 'core/controllers/base/UserController';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { ResourceType } from 'core/contracts/enums/ResourceType';

const DEFAULT_EXPIRE_TIME = '3600';

export const authChecker: AuthChecker<AppContext> = ({ root, args, context, info }, roles: string[]): boolean => {
  const payload: JwtPayload | undefined = context.token;
  return checkRoles(payload, roles);
};

export const getToken = (payload: JwtPayload): { token: string; expiresAt: Date } => {
  // JWT_EXPIRE_TIME must be in seconds
  const expireSecs = parseInt(process.env.JWT_EXPIRE_TIME || DEFAULT_EXPIRE_TIME);
  const expiresAt = new Date(Date.now() + expireSecs * 1000);
  const token = jwt.sign(payload, process.env.JWT_SECRET as jwt.Secret, { expiresIn: expireSecs });
  return { token, expiresAt };
};

export const checkRoles = (payload: JwtPayload | undefined, roles?: string[]): boolean => {
  if (payload && payload[PayloadKeys.USER_KEY]) {
    if (roles?.length) {
      return (
        !!payload[PayloadKeys.ROLES_KEY]?.length &&
        roles.some((role) => payload[PayloadKeys.ROLES_KEY].includes(roleMap[role]))
      );
    }

    return true;
  }
  return false;
};

/*
export const checkRoles = async (context: AppContext, requiredRoles: string[]): Promise<boolean> => {
  if (context.token) {
    const { u: id, t: tenantId } = context.token;
    if (id && tenantId) {
      const em = context.em.fork();
      const user = await UserController.getUser(em, tenantId, id, ['roles']);
      if (user) {
        const userRoles = user.roles.getItems().map((role) => role.name) as string[];
        if (requiredRoles?.length) {
          return !!userRoles.length && requiredRoles.some((role) => userRoles.includes(role));
        }
        return true;
      }
    }
  }
  return false;
};
*/

export const verifyScope = async ({ scope, context }: { context: AppContext; scope: Scope }): Promise<boolean> => {
  if (context.token) {
    const { _1: id, _2: tenantId } = context.token;
    if (id && tenantId) {
      const user = await UserController.getUser(context.em.fork(), tenantId, id, ['privilegeGroups.privileges']);
      if (user) {
        const privilegeGroups = user.privilegeGroups.getItems() as PrivilegeGroup[];
        if (scope?.resources?.length) {
          return hasAllPrivileges(privilegeGroups, scope.resources);
        }
      }
    }
  }
  return false;
};

export const getScopeForUser = async ({
  context,
  resourceType,
}: {
  context: AppContext;
  resourceType?: ResourceType;
}): Promise<Scope | undefined> => {
  let privilegeGroups: PrivilegeGroup[] = [];
  if (context.token) {
    const { _1: id, _2: tenantId } = context.token;
    if (id && tenantId) {
      const user = await UserController.getUser(context.em.fork(), tenantId, id, ['privilegeGroups.privileges']);
      if (user) {
        privilegeGroups = user.privilegeGroups.getItems() as PrivilegeGroup[];
      }
    }
  }
  const resources = privilegeGroups
    .map((group) => {
      return group.privileges.getItems().reduce((acc: Resource[], privilege) => {
        if (resourceType) {
          if (privilege.resourceType === resourceType) {
            acc.push({ resourceType, resourceId: privilege.resourceId || '' });
          }
        } else {
          acc.push({ resourceType: privilege.resourceType, resourceId: privilege.resourceId || '' });
        }
        return acc;
      }, []);
    })
    .flat();
  return resources?.length ? { resources } : undefined;
};

export const hasPrivilege = (privilegeGroups: PrivilegeGroup[], resource: Resource): boolean => {
  return privilegeGroups.some((group) => group.hasPrivilege(resource));
};

export const hasAllPrivileges = (privilegeGroups: PrivilegeGroup[], resources: Resource[]): boolean => {
  return resources.every((resource) => hasPrivilege(privilegeGroups, resource));
};
