/*
  This class is used to manage permissions in the application. 
  It uses bitwise operations to store and manage permissions. 
*/
export class Permission {
  static READ = 1; // 0001
  static WRITE = 2; // 0010
  static DELETE = 4; // 0100
  static SHARE = 8; // 1000

  /*
    This method is used to create a permission. 
    It takes a list of permissions and combines them using the bitwise OR operator. 
  */
  static create(...permissions: number[]): number {
    return permissions.reduce((acc, p) => acc | p, 0);
  }

  /*
    This method is used to add permissions to a base permission. 
    It takes a base permission and a list of permissions to add. 
    It combines the base permission with the list of permissions using the bitwise OR operator. 
  */
  static add(base: number, ...permissions: number[]): number {
    return permissions.reduce((acc, p) => acc | p, base);
  }

  /*
    This method is used to remove permissions from a base permission. 
    It takes a base permission and a list of permissions to remove. 
    It combines the base permission with the list of permissions using the bitwise AND operator. 
  */
  static remove(base: number, ...permissions: number[]): number {
    return permissions.reduce((acc, p) => acc & ~p, base);
  }

  /*
    This method is used to check if a base permission has a list of permissions. 
    It takes a base permission and a list of permissions to check. 
    It checks if the base permission has all the permissions in the list using the bitwise AND operator. 
  */
  static has(base: number, ...permissions: number[]): boolean {
    return permissions.every((p) => (base & p) === p);
  }

  /*
    This method is used to check if a base permission has a specific permission. 
    It takes a base permission and a specific permission to check. 
    It checks if the base permission has the specific permission using the bitwise AND operator. 
  */
  static hasPermission(base: number, permission: number): boolean {
    return (base & permission) === permission;
  }
}
