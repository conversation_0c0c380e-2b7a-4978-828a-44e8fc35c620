import { IsE<PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { Stakeholder } from 'core/entities/Stakeholder';
import { Field, InputType } from 'type-graphql';

@InputType()
export class CreateStakeholderInput implements Partial<Stakeholder> {
  @Field({ nullable: true })
  name?: string;
  @Field({ nullable: true })
  org?: string;
}

@InputType()
export class UpdateStakeholderInput implements Partial<CreateStakeholderInput> {
  @Field({ nullable: true })
  name?: string;
  @Field({ nullable: true })
  org?: string;
}
