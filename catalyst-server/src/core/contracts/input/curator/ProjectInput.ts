import { Filter } from '@mikro-orm/core';
import { IsNotEmpty } from 'class-validator';
import { ProjectStakeholderType } from 'core/contracts/enums/ProjectStakeholderType';
import { ProjectStatus } from 'core/contracts/enums/ProjectStatus';
import { UpdateOperator } from 'core/contracts/enums/UpdateOperator';
import { Project } from 'core/entities/Project';
import { Field, InputType, Int } from 'type-graphql';
import { UpdateLinks } from '../base/CommonInput';

@InputType()
export class CreateProjectInput implements Partial<Project> {
  @Field()
  @IsNotEmpty({ message: 'Title is required' })
  title!: string;
  @Field(() => ProjectStatus, { nullable: true })
  status?: ProjectStatus;
  @Field({ nullable: true })
  summary?: string;
  @Field({ nullable: true })
  background?: string;
  @Field({ nullable: true })
  startDate?: Date;
  @Field({ nullable: true })
  endDate?: Date;
  @Field({ nullable: true })
  goals?: string;
  @Field({ nullable: true })
  type?: string;
  @Field({ nullable: true })
  otherType?: string;
  @Field({ nullable: true })
  statusNotes?: string;
}

@InputType()
export class UpdateProjectInput implements Partial<CreateProjectInput> {
  @Field({ nullable: true })
  @IsNotEmpty({ message: 'Title is required' })
  title?: string;
  @Field(() => ProjectStatus, { nullable: true })
  status?: ProjectStatus;
  @Field({ nullable: true })
  summary?: string;
  @Field({ nullable: true })
  background?: string;
  @Field({ nullable: true })
  startDate?: Date;
  @Field({ nullable: true })
  endDate?: Date;
  @Field({ nullable: true })
  goals?: string;
  @Field({ nullable: true })
  type?: string;
  @Field({ nullable: true })
  otherType?: string;
  @Field({ nullable: true })
  statusNotes?: string;
}

@InputType()
export class CreateProjectFromOpportunityInput {
  @Field()
  title!: string;
  @Field()
  opportunityId!: string;
  @Field({ nullable: true })
  includeProblemSolution?: boolean;
  @Field({ nullable: true })
  includeCategories?: boolean;
  @Field({ nullable: true })
  includeAttachments?: boolean;
}

@InputType()
export class CreateProjectLinks {
  @Field({ nullable: true })
  creatorId?: string;
}

@InputType()
export class UpdateProjectLinks {
  // @TODO use this in updateProject if we want to be able to
  // change the creator of a project
  @Field({ nullable: true })
  creatorId?: string;
  @Field(() => [UpdateLinks], { nullable: true })
  categories?: UpdateLinks[];
  @Field(() => [UpdateLinks], { nullable: true })
  opportunities?: UpdateLinks[];
  @Field(() => [ProjectStakeHolderUpdateLinks], { nullable: true })
  projectStakeholders?: ProjectStakeHolderUpdateLinks[];
}

@InputType()
export class ProjectStakeHolderUpdateLinks {
  @Field(() => UpdateOperator)
  operator!: UpdateOperator;
  @Field(() => [ProjectStakeHolderUpdateItem])
  items!: ProjectStakeHolderUpdateItem[];
}
@InputType()
export class ProjectStakeHolderUpdateItem {
  @Field()
  id!: string;
  @Field(() => ProjectStakeholderType)
  type!: ProjectStakeholderType;
}
