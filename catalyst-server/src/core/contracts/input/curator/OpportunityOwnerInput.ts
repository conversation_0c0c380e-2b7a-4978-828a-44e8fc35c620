import { Field, InputType } from 'type-graphql';

@InputType()
export class AssignOpportunityOwnerInput {
  @Field()
  opportunityId!: string;
  @Field()
  emailAddress!: string;
  @Field()
  firstName!: string;
  @Field()
  lastName!: string;
  @Field({ nullable: true })
  org1?: string;
  @Field({ nullable: true })
  org2?: string;
  @Field({ nullable: true })
  org3?: string;
  @Field({ nullable: true })
  org4?: string;
  @Field({ nullable: true })
  phone?: string;
  @Field({ nullable: true })
  altContact?: string;
  @Field({ nullable: true })
  organizationRole?: string;
}
