import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { User } from 'core/entities/User';
import { Field, InputType } from 'type-graphql';
import { UserOptionsInput } from '../base/UserInput';
import { UpdateLinks } from '../base/CommonInput';

/***
 *       ___      _      _ __            __  ____     __          ___
 *      / _ \____(_)  __(_) /__ ____ ___/ / /  _/__  / /____ ____/ _/__ ________ ___
 *     / ___/ __/ / |/ / / / _ `/ -_) _  / _/ // _ \/ __/ -_) __/ _/ _ `/ __/ -_|_-<
 *    /_/  /_/ /_/|___/_/_/\_, /\__/\_,_/ /___/_//_/\__/\__/_/ /_/ \_,_/\__/\__/___/
 *                        /___/
 */

@InputType()
export class CreateUserInput implements Partial<User> {
  //must be .mil address!
  @Field()
  @IsNotEmpty({ message: 'Email Address is required' })
  @IsEmail()
  emailAddress!: string;
  @Field()
  @MinLength(8, { message: 'Password must be atleast 8 characters' })
  password!: string;
  @Field()
  @IsNotEmpty({ message: 'First Name is required' })
  firstName!: string;
  @Field()
  @IsNotEmpty({ message: 'Last Name is required' })
  lastName!: string;
  @Field({ nullable: true })
  org1?: string;
  @Field({ nullable: true })
  org2?: string;
  @Field({ nullable: true })
  org3?: string;
  @Field({ nullable: true })
  org4?: string;
  @Field({ nullable: true })
  phone?: string;
  @Field({ nullable: true })
  @IsEmail()
  altContact?: string;
  @Field(() => VerifiedStatus, { nullable: true })
  status?: VerifiedStatus;
  @Field(() => UserOptionsInput, { nullable: true })
  options?: UserOptionsInput;
}

@InputType()
export class UpdateUserInput implements Partial<CreateUserInput> {
  @Field({ nullable: true })
  @MinLength(8, { message: 'Password must be atleast 8 characters' })
  password?: string;
  @Field({ nullable: true })
  firstName?: string;
  @Field({ nullable: true })
  lastName?: string;
  @Field({ nullable: true })
  org1?: string;
  @Field({ nullable: true })
  org2?: string;
  @Field({ nullable: true })
  org3?: string;
  @Field({ nullable: true })
  org4?: string;
  @Field({ nullable: true })
  phone?: string;
  @Field({ nullable: true })
  @IsEmail()
  altContact?: string;
  @Field(() => VerifiedStatus, { nullable: true })
  status?: VerifiedStatus;
  @Field(() => UserOptionsInput, { nullable: true })
  options?: UserOptionsInput;
}

@InputType()
export class UserLinks {
  @Field(() => [RoleNames], { nullable: true })
  roleNames?: RoleNames[];
  @Field(() => [UpdateLinks], { nullable: true })
  privilegeGroups?: UpdateLinks[];
}
