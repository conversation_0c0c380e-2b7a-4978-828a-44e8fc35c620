import { Readable } from 'stream';
import { Field, InputType } from 'type-graphql';

export interface AddAttachmentInput {
  filename: string;
  mimetype: string;
  encoding: string;
  displayName?: string;
  notes?: string;
  createReadStream: () => Readable;
}

@InputType()
export class UpdateAttachmentInput {
  @Field()
  id!: string;
  @Field({ nullable: true })
  displayName?: string;
  @Field({ nullable: true })
  notes?: string;
}

@InputType()
export class AttachmentLinks {
  @Field({ nullable: true })
  opportunityId?: string;
  @Field({ nullable: true })
  projectId?: string;
}
