import { Field, InputType } from 'type-graphql';
import { UpdateLinks } from '../base/CommonInput';
import { OwnershipStatus } from 'core/contracts/enums/OwnershipStatus';
import { IsEmail, IsNotEmpty } from 'class-validator';
import { OpportunityOwnerStatus } from 'core/entities/OpportunityOwnerStatus';

@InputType()
export class CreateOpportunityOwnerInput implements Partial<OpportunityOwnerStatus> {
  @Field(() => OwnershipStatus, { nullable: true })
  status?: OwnershipStatus;
  @Field(() => Date, { nullable: true })
  statusSetPreviousAt?: Date;
  @Field(() => Date, { nullable: true })
  statusSetRemovedAt?: Date;
  @Field()
  @IsNotEmpty({ message: 'Email Address is required' })
  @IsEmail()
  emailAddress!: string;
  @Field({ nullable: true })
  firstName?: string;
  @Field({ nullable: true })
  lastName?: string;
  @Field({ nullable: true })
  org1?: string;
  @Field({ nullable: true })
  org2?: string;
  @Field({ nullable: true })
  org3?: string;
  @Field({ nullable: true })
  org4?: string;
  @Field({ nullable: true })
  phone?: string;
  @Field({ nullable: true })
  @IsEmail()
  altContact?: string;
  @Field({ nullable: true })
  organizationRole?: string;
  @Field({ nullable: true })
  userId?: string;
  @Field({ nullable: true })
  opportunityId?: string;
}

@InputType()
export class UpdateOpportunityOwnerInput implements Partial<CreateOpportunityOwnerInput> {
  @Field(() => OwnershipStatus, { nullable: true })
  status?: OwnershipStatus;
  @Field(() => Date, { nullable: true })
  statusSetPreviousAt?: Date;
  @Field(() => Date, { nullable: true })
  statusSetRemovedAt?: Date;
  @Field({ nullable: true })
  organizationRole?: string;
}

@InputType()
export class OpportunityOwnerLinks {
  @Field(() => [UpdateLinks], { nullable: true })
  owner?: UpdateLinks[];
  @Field(() => [UpdateLinks], { nullable: true })
  opportunity?: UpdateLinks[];
}
