import { MinLength } from 'class-validator';
import { Category } from 'core/entities/Category';
import { Field, InputType } from 'type-graphql';

@InputType()
export class CreateCategoryInput implements Partial<Category> {
  @Field()
  @MinLength(1, { message: 'Category name cannot be empty' })
  name!: string;
}

@InputType()
export class UpdateCategoryInput implements Partial<CreateCategoryInput> {
  @Field()
  @MinLength(1, { message: 'Category name cannot be empty' })
  name!: string;
}
