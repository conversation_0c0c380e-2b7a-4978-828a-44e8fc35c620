import { IsNotEmpty } from 'class-validator';
import { ExistingSolution } from 'core/entities/ExistingSolution';
import { Field, InputType } from 'type-graphql';

@InputType()
export class CreateExistingSolutionInput implements Partial<ExistingSolution> {
  @Field()
  @IsNotEmpty({ message: 'Source is required' })
  source!: string;

  @Field({ nullable: true })
  title?: string;

  @Field({ nullable: true })
  organization?: string;

  @Field({ nullable: true })
  needsModification?: boolean;
}

@InputType()
export class UpdateExistingSolutionInput implements Partial<CreateExistingSolutionInput> {
  @Field({ nullable: true })
  source?: string;

  @Field({ nullable: true })
  title?: string;

  @Field({ nullable: true })
  organization?: string;

  @Field({ nullable: true })
  needsModification?: boolean;
}

@InputType()
export class ExistingSolutionLinks {
  @Field()
  @IsNotEmpty({ message: 'Opportunity ID is required' })
  opportunityId!: string;
}
