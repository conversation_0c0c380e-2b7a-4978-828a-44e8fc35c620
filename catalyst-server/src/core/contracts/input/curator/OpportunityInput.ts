import { IsNotEmpty } from 'class-validator';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';
import { Opportunity } from 'core/entities/Opportunity';
import { Field, InputType, Int } from 'type-graphql';
import { UpdateLinks } from '../base/CommonInput';
import { UpdateOperator } from 'core/contracts/enums/UpdateOperator';
import { RelatedOpportunityType } from 'core/contracts/enums/RelatedOpportunityType';
import { OpportunityVisibility } from 'core/contracts/enums/OpportunityVisibility';

/***
 *      ____                     __            _ __         ____     __          ___
 *     / __ \___  ___  ___  ____/ /___ _____  (_) /___ __  /  _/__  / /____ ____/ _/__ ________ ___
 *    / /_/ / _ \/ _ \/ _ \/ __/ __/ // / _ \/ / __/ // / _/ // _ \/ __/ -_) __/ _/ _ `/ __/ -_|_-<
 *    \____/ .__/ .__/\___/_/  \__/\_,_/_//_/_/\__/\_, / /___/_//_/\__/\__/_/ /_/ \_,_/\__/\__/___/
 *        /_/  /_/                                /___/
 */

@InputType()
export class CreateOpportunityInput implements Partial<Opportunity> {
  @Field()
  @IsNotEmpty({ message: 'Title is required' })
  title!: string;
  @Field()
  @IsNotEmpty({ message: 'Problem Statement is required' })
  statement!: string;
  @Field()
  @IsNotEmpty({ message: 'Problem Context is required' })
  context!: string;

  // optional
  @Field({ nullable: true })
  benefits?: string;
  @Field(() => OpportunityStatus, { nullable: true })
  status?: OpportunityStatus;
  @Field({ nullable: true })
  solutionConcepts?: string;
  @Field({ nullable: true })
  additionalNotes?: string;
  @Field({ nullable: true })
  campaign?: string;
  @Field({ nullable: true })
  function?: string;
  @Field({ nullable: true })
  campaignNotes?: string;
  @Field({ nullable: true })
  statusNotes?: string;
  @Field(() => Int, { nullable: true })
  priority?: number;
  @Field({ nullable: true })
  priorityNotes?: string;
  @Field({ nullable: true })
  solutionPathway?: string;
  @Field({ nullable: true })
  solutionPathwayDetails?: string;
  @Field({ nullable: true })
  permission?: string;
  @Field({ nullable: true })
  attachmentNotes?: string;
  @Field({ nullable: true })
  armyModernizationPriority?: string;
  @Field({ nullable: true })
  echelonApplicability?: string;
  @Field({ nullable: true })
  isTiCLOE?: boolean;
  @Field({ nullable: true })
  transitionInContactLineOfEffort?: string;
  @Field({ nullable: true })
  operationalRules?: string;
  @Field({ nullable: true })
  capabilityArea?: string;
  @Field({ nullable: true })
  initiatives?: string;
  @Field({ nullable: true })
  endorsements?: string;
  @Field(() => OpportunityVisibility, { nullable: true })
  visibility?: OpportunityVisibility;
}

@InputType()
export class UpdateOpportunityInput implements Partial<CreateOpportunityInput> {
  @Field({ nullable: true })
  title?: string;
  @Field({ nullable: true })
  statement?: string;
  @Field({ nullable: true })
  context?: string;
  @Field({ nullable: true })
  benefits?: string;
  @Field(() => OpportunityStatus, { nullable: true })
  status?: OpportunityStatus;
  @Field({ nullable: true })
  solutionConcepts?: string;
  @Field({ nullable: true })
  additionalNotes?: string;
  @Field({ nullable: true })
  campaign?: string;
  @Field({ nullable: true })
  function?: string;
  @Field({ nullable: true })
  campaignNotes?: string;
  @Field({ nullable: true })
  statusNotes?: string;
  @Field(() => Int, { nullable: true })
  priority?: number;
  @Field({ nullable: true })
  priorityNotes?: string;
  @Field({ nullable: true })
  solutionPathway?: string;
  @Field({ nullable: true })
  solutionPathwayDetails?: string;
  @Field({ nullable: true })
  permission?: string;
  @Field({ nullable: true })
  attachmentNotes?: string;
  @Field({ nullable: true })
  initiatives?: string;
  @Field({ nullable: true })
  endorsements?: string;
  @Field({ nullable: true })
  armyModernizationPriority?: string;
  @Field({ nullable: true })
  echelonApplicability?: string;
  @Field({ nullable: true })
  capabilityArea?: string;
  @Field({ nullable: true })
  isTiCLOE?: boolean;
  @Field({ nullable: true })
  transitionInContactLineOfEffort?: string;
  @Field({ nullable: true })
  operationalRules?: string;

  @Field(() => OpportunityVisibility, { nullable: true })
  visibility?: OpportunityVisibility;
}

@InputType()
export class CreateOpportunityLinks {
  @Field()
  userId!: string;
}

@InputType()
export class UpdateOpportunityLinks {
  // @TODO use this in updateOpportunity if want to be able to
  // change the owner of an opportunity
  @Field({ nullable: true })
  userId?: string;
  @Field(() => [UpdateLinks], { nullable: true })
  categories?: UpdateLinks[];
  @Field(() => [UpdateLinks], { nullable: true })
  stakeholders?: UpdateLinks[];
  @Field(() => [UpdateLinks], { nullable: true })
  opportunities?: UpdateLinks[];
  @Field(() => [RelatedOpportunityUpdateLinks], { nullable: true })
  relatedOpportunities?: RelatedOpportunityUpdateLinks[];
}

@InputType()
export class RelatedOpportunityUpdateLinks {
  @Field(() => UpdateOperator)
  operator!: UpdateOperator;
  @Field(() => [RelatedOpportunityUpdateItem])
  items!: RelatedOpportunityUpdateItem[];
}
@InputType()
export class RelatedOpportunityUpdateItem {
  @Field()
  id!: string;
  @Field(() => RelatedOpportunityType)
  type!: RelatedOpportunityType;
}
