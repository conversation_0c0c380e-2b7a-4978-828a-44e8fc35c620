import { Field, InputType } from 'type-graphql';

@InputType()
export class AddLinkInput {
  @Field()
  name!: string;
  @Field()
  url!: string;
  @Field({ nullable: true })
  notes?: string;
}

@InputType()
export class UpdateLinkInput {
  @Field()
  id!: string;
  @Field({ nullable: true })
  name?: string;
  @Field({ nullable: true })
  url?: string;
  @Field({ nullable: true })
  notes?: string;
}

@InputType()
export class LinkLinks {
  @Field({ nullable: true })
  opportunityId?: string;
  @Field({ nullable: true })
  projectId?: string;
}
