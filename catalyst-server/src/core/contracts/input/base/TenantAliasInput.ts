import { MinLength } from 'class-validator';
import { JSONType } from 'core/contracts/types/JSONType';
import { TenantAlias } from 'core/entities/TenantAlias';
import { Field, InputType } from 'type-graphql';

@InputType()
export class CreateTenantAliasInput implements Partial<TenantAlias> {
  @Field()
  @MinLength(1, { message: 'TenantAlias handle cannot be empty' })
  handle!: string;
  @Field({ nullable: true })
  name?: string;
  @Field(() => JSONType, { nullable: true })
  config?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  theme?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  content?: Record<string, unknown>;
}

@InputType()
export class UpdateTenantAliasInput implements Partial<CreateTenantAliasInput> {
  @Field({ nullable: true })
  handle?: string;
  @Field({ nullable: true })
  name?: string;
  @Field(() => JSONType, { nullable: true })
  config?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  theme?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  content?: Record<string, unknown>;
}
