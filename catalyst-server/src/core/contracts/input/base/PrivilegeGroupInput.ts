import { Privilege } from 'core/entities/Privilege';
import { Field, InputType } from 'type-graphql';
import { UpdateLinks } from './CommonInput';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { ResourceType } from 'core/contracts/enums/ResourceType';

@InputType()
export class CreatePrivilegeGroupInput implements Partial<PrivilegeGroup> {
  @Field({ nullable: true })
  name?: string;
}

@InputType()
export class UpdatePrivilegeGroupInput implements Partial<PrivilegeGroup> {
  @Field({ nullable: true })
  name?: string;
}

@InputType()
export class UpdatePrivilegeGroupLinks {
  @Field(() => [UpdateLinks], { nullable: true })
  privileges?: UpdateLinks[];
}

@InputType()
export class CreatePrivilegeInput implements Partial<Privilege> {
  @Field(() => ResourceType)
  resourceType!: ResourceType;
  @Field({ nullable: true })
  name?: string;
  @Field({ nullable: true })
  resourceId?: string;
  //@Field()
  //permissions?: number;
}

@InputType()
export class UpdatePrivilegeInput implements Partial<Privilege> {
  @Field(() => ResourceType)
  resourceType!: ResourceType;
  @Field({ nullable: true })
  name?: string;
  @Field({ nullable: true })
  resourceId?: string;
  //@Field()
  //permissions?: number;
}
