import { JsonType } from '@mikro-orm/core';
import { IsNotEmpty } from 'class-validator';
import { JSONType } from 'core/contracts/types/JSONType';
import { Tenant } from 'core/entities/Tenant';
import { Field, InputType } from 'type-graphql';

@InputType()
export class CreateTenantInput implements Partial<Tenant> {
  @Field()
  @IsNotEmpty({ message: 'name is required' })
  name!: string;
  @IsNotEmpty({ message: 'handle is required' })
  @Field()
  handle!: string;
  @Field({ nullable: true })
  label?: string;
  @Field(() => JSONType, { nullable: true })
  config?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  theme?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  content?: Record<string, unknown>;
}

@InputType()
export class UpdateTenantInput implements Partial<Tenant> {
  @Field({ nullable: true })
  @IsNotEmpty({ message: 'name is required' })
  name?: string;
  @IsNotEmpty({ message: 'handle is required' })
  @Field({ nullable: true })
  handle?: string;
  @Field({ nullable: true })
  label?: string;
  @Field(() => JSONType, { nullable: true })
  config?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  theme?: Record<string, unknown>;
  @Field(() => JSONType, { nullable: true })
  content?: Record<string, unknown>;
}
