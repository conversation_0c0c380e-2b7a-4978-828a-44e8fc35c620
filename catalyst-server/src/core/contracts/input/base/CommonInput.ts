import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'core/contracts/enums/CalcOperator';
import { LogicalOperator } from 'core/contracts/enums/LogicalOperator';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { UpdateOperator } from 'core/contracts/enums/UpdateOperator';
import { AnyScalar } from 'core/contracts/types/AnyScalar';
import { JSONType } from 'core/contracts/types/JSONType';
import { Field, InputType, Int } from 'type-graphql';

/**
 * Specifies the paging input for the query operation.
 */
@InputType({ description: 'Controls list paging' })
export class PagingInput {
  /**
   * The number of rows to request, per page.
   */
  @Field(() => Int, {
    nullable: true,
    description: 'The number of rows to request, per page.  Defaults to 100. Max 500',
  })
  pageSize?: number;
  /**
   * How many results to skip or the 'zero-based' starting index. Note: this is a 'string' type, but the value should be an integer
   */
  @Field({ nullable: true, description: `How many results to skip or the 'zero-based' starting index` })
  cursor?: string;
}

@InputType()
export class SearchSortInput {
  /**
   * Simple AND query for a set of fieldNames and operators.
   * Use JsonSearchGroup to create OR values and more complex queries
   */
  @Field(() => [SearchField], { nullable: true })
  searchFields?: SearchField[];
  // SearchFieldGroup allows for nesting more complex queries
  // these will be AND at the top level, but can be nested with OR values
  // NOTE: Any enums used within jsonSearchGroups must be represented as strings (the string values of the enum)
  /**
   * Complex query, allowing for nested AND/OR values
   */
  @Field(() => [JSONType], { nullable: true })
  jsonSearchGroups?: JsonSearchGroup[];
  /** Specifies the fields used for sorting the results  */
  @Field(() => [SortField], { nullable: true })
  sortFields?: SortField[];
}

@InputType()
export class Calculation {
  @Field(() => [FieldOperations])
  operations!: FieldOperations[];
  @Field({ nullable: true })
  distinct?: boolean;
}

@InputType()
export class FieldOperations {
  @Field({ nullable: true })
  fieldName?: string;
  @Field(() => CalcOperator, { nullable: true })
  operator?: CalcOperator;
}

@InputType()
export class ReportInput {
  @Field(() => [ReportQuery])
  queries!: ReportQuery[];
}

@InputType()
export class ReportQuery {
  @Field()
  reportName!: string;
  @Field({ nullable: true })
  label?: string;
  @Field(() => SearchSortInput, { nullable: true })
  searchSortInput?: SearchSortInput;
}

@InputType()
export class SearchField {
  @Field(() => [String])
  // array of 'OR' values
  fieldNames!: string[];
  @Field(() => SearchOperator, { nullable: true, defaultValue: SearchOperator.MATCH })
  operator?: SearchOperator;
  @Field(() => AnyScalar, { nullable: true })
  searchValue?: string | number | boolean | Date | unknown[] | null;
}

// These allow for more complex queries
export class JsonSearchGroup {
  //note SearchField is resused here but is not validated by the GraphQL schema because it is part of a JSONType
  operands?: (JsonSearchGroup | SearchField)[];
  operator?: LogicalOperator; // Must map to a LogicalOperator value
}

@InputType()
export class SortField {
  @Field()
  fieldName!: string;
  @Field({ nullable: true, defaultValue: true })
  ascending?: boolean;
}

@InputType()
export class UpdateLinks {
  @Field(() => UpdateOperator)
  operator!: UpdateOperator;
  @Field(() => [String])
  ids!: string[];
}

/**
 * Specifies the scope for the query operation
 */
@InputType()
export class Scope {
  @Field(() => [Resource])
  resources!: Resource[];
}

/**
 * Specifies the target resource and type
 */
@InputType()
export class Resource {
  @Field()
  resourceId!: string;
  @Field(() => ResourceType)
  resourceType!: ResourceType;
}
