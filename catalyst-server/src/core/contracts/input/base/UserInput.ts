import { IsEmail, <PERSON>NotEmpty, <PERSON>, MinLength } from 'class-validator';
import { User } from 'core/entities/User';
import { UserOptions } from 'core/entities/UserOptions';
import { Field, InputType } from 'type-graphql';

/***
 *      __  __              ____     __          ___
 *     / / / /__ ___ ____  /  _/__  / /____ ____/ _/__ ________ ___
 *    / /_/ (_-</ -_) __/ _/ // _ \/ __/ -_) __/ _/ _ `/ __/ -_|_-<
 *    \____/___/\__/_/   /___/_//_/\__/\__/_/ /_/ \_,_/\__/\__/___/
 *
 */

@InputType()
export class UserOptionsInput implements UserOptions {
  @Field({ nullable: true })
  lastUsedServerVersion?: string;
  @Field({ nullable: true })
  cookieAcceptance?: boolean;
  @Field({ nullable: true })
  submissionEmailOptOut?: boolean;
  @Field({ nullable: true })
  optOutOtherContact?: boolean;
  @Field({ nullable: true })
  optOutTeamUpdates?: boolean;
  @Field({ nullable: true })
  optOutAll?: boolean;
}

@InputType()
export class SubmitUserInput implements Partial<User> {
  //must be .mil address!
  @Field()
  @IsNotEmpty({ message: 'Email Address is required' })
  @IsEmail({}, { message: 'Email Address is invalid' })
  emailAddress!: string;
  @Field()
  @IsNotEmpty({ message: 'First Name is required' })
  firstName!: string;
  @Field()
  @IsNotEmpty({ message: 'Last Name is required' })
  lastName!: string;

  @Field({ nullable: true })
  org1?: string;
  @Field({ nullable: true })
  org2?: string;
  @Field({ nullable: true })
  org3?: string;
  @Field({ nullable: true })
  org4?: string;
  @Field({ nullable: true })
  // @IsPhoneNumber('US')
  phone?: string;
  @Field({ nullable: true })
  @IsEmail({}, { message: 'Email Address is invalid' })
  altContact?: string;
  @Field(() => UserOptionsInput, { nullable: true })
  options?: UserOptionsInput;
  @Field()
  tenantHandle!: string;
  // for data imports
  createdAt?: Date | undefined;
}

@InputType()
export class RegisterUserInput extends SubmitUserInput {
  @Field()
  @MinLength(15, { message: 'Password must be at least 15 characters long' })
  @Matches(/[A-Z]/, { message: 'Password must include at least one upper-case character' })
  @Matches(/[a-z]/, { message: 'Password must include at least one lower-case character' })
  @Matches(/\d/, { message: 'Password must include at least one number' })
  @Matches(/[^A-Za-z0-9]/, { message: 'Password must include at least one special character' })
  
  password!: string;
}

@InputType()
export class UpdateCurrentUserInput implements Partial<SubmitUserInput> {
  @Field({ nullable: true })
  @MinLength(15, { message: 'Password must be at least 15 characters long' })
  @Matches(/[A-Z]/, { message: 'Password must include at least one upper-case character' })
  @Matches(/[a-z]/, { message: 'Password must include at least one lower-case character' })
  @Matches(/\d/, { message: 'Password must include at least one number' })
  @Matches(/[^A-Za-z0-9]/, { message: 'Password must include at least one special character' })
  password?: string;
  @Field({ nullable: true })
  firstName?: string;
  @Field({ nullable: true })
  lastName?: string;
  @Field({ nullable: true })
  org1?: string;
  @Field({ nullable: true })
  org2?: string;
  @Field({ nullable: true })
  org3?: string;
  @Field({ nullable: true })
  org4?: string;
  @Field({ nullable: true })
  // @IsPhoneNumber('US')
  phone?: string;
  @Field({ nullable: true })
  @IsEmail({}, { message: 'Email Address Invalid' })
  altContact?: string;
  @Field(() => UserOptionsInput, { nullable: true })
  options?: UserOptionsInput;
}

@InputType()
export class UpdateCurrentUserLinks {
  @Field({ nullable: true })
  appMetaId?: string;
}
