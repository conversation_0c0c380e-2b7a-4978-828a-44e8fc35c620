import { Owner } from 'core/entities/Owner';
import { Field, InputType } from 'type-graphql';
import { UpdateLinks } from '../base/CommonInput';
import { IsEmail, IsNotEmpty } from 'class-validator';

@InputType()
export class CreateOwnerInput implements Partial<Owner> {
  @Field()
  @IsNotEmpty({ message: 'Email Address is required' })
  @IsEmail()
  emailAddress!: string;
  @Field()
  @IsNotEmpty({ message: 'First Name is required' })
  firstName!: string;
  @Field()
  @IsNotEmpty({ message: 'Last Name is required' })
  lastName!: string;
  @Field({ nullable: true })
  org1?: string;
  @Field({ nullable: true })
  org2?: string;
  @Field({ nullable: true })
  org3?: string;
  @Field({ nullable: true })
  org4?: string;
  @Field({ nullable: true })
  phone?: string;
  @Field({ nullable: true })
  @IsEmail()
  altContact?: string;
  @Field({ nullable: true })
  organizationRole?: string;
  @Field({ nullable: true })
  userId?: string;
  @Field({ nullable: true })
  opportunityId?: string;
}

@InputType()
export class UpdateOwnerInput implements Partial<CreateOwnerInput> {
  @Field({ nullable: true })
  organizationRole?: string;
}

@InputType()
export class OwnerLinks {
  @Field(() => [UpdateLinks], { nullable: true })
  user?: UpdateLinks[];
  @Field(() => [UpdateLinks], { nullable: true })
  opportunities?: UpdateLinks[];
}
