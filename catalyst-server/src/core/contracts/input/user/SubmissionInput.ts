import { IsNotEmpty } from 'class-validator';
import { Opportunity } from 'core/entities/Opportunity';
import { Submission } from 'core/entities/Submission';
import { Field, InputType, Int } from 'type-graphql';

/***
 *       ____     __         _         _             ____     __          ___
 *      / __/_ __/ /  __ _  (_)__ ___ (_)__  ___    /  _/__  / /____ ____/ _/__ ________ ___
 *     _\ \/ // / _ \/  ' \/ (_-<(_-</ / _ \/ _ \  _/ // _ \/ __/ -_) __/ _/ _ `/ __/ -_|_-<
 *    /___/\_,_/_.__/_/_/_/_/___/___/_/\___/_//_/ /___/_//_/\__/\__/_/ /_/ \_,_/\__/\__/___/
 *
 */

@InputType()
export class CreateSubmissionInput implements Partial<Submission>, Partial<Opportunity> {
  @Field()
  @IsNotEmpty({ message: 'Title is required' })
  title!: string;
  @Field()
  @IsNotEmpty({ message: 'Problem Statement is required' })
  statement!: string;
  @Field()
  @IsNotEmpty({ message: 'Problem Context is required' })
  context!: string;
  // optional
  @Field({ nullable: true })
  benefits?: string;
  @Field({ nullable: true })
  solutionConcepts?: string;
  @Field({ nullable: true })
  campaign?: string;
  @Field({ nullable: true })
  function?: string;

  // for data imports
  createdAt?: Date | undefined;
}

@InputType()
export class UpdateSubmissionInput implements Partial<CreateSubmissionInput> {
  @Field({ nullable: true })
  title?: string;
  @Field({ nullable: true })
  statement?: string;
  @Field({ nullable: true })
  context?: string;
  @Field({ nullable: true })
  benefits?: string;
  @Field({ nullable: true })
  solutionConcepts?: string;
  @Field({ nullable: true })
  campaign?: string;
  @Field({ nullable: true })
  function?: string;
}

@InputType()
export class NewSubmissionLinks {
  @Field()
  userId!: string;
}
