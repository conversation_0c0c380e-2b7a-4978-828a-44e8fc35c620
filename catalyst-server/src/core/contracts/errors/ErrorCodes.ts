// consider this approach
// https://stackoverflow.com/questions/57342284/how-to-handle-error-and-send-response-in-graphql

export interface ErrorCodeType {
  [index: string]: { message: string; code: number };
}

export const errorKeys = {
  INVALID_LOGIN: 'INVALID_LOGIN',
  USER_EXISTS_AS_VERIFIED: 'USER_EXISTS_AS_VERIFIED',
  UNVERIFIED_USER_NOT_FOUND: 'UNVERIFIED_USER_NOT_FOUND',
  UNAUTHORIZED: 'UNAUTHORIZED',
  ARGUMENT_VALIDATION_ERROR: 'Argument Validation Error',
  DEFAULT: 'DEFAULT',
  GRAPHQL_VALIDATION_ERROR: 'GRAPHQL_VALIDATION_ERROR',
  FAILED_TO_GENERATE_FILE: 'FAILED_TO_GENERATE_FILE',
  TENANT_NOT_FOUND: 'TENANT_NOT_FOUND',
  OBJECT_ALREADY_EXISTS: 'OBJECT_ALREADY_EXISTS',
  OBJECT_NOT_FOUND: 'OBJECT_NOT_FOUND',
};

export const errorCodes: ErrorCodeType = {
  [errorKeys.INVALID_LOGIN]: {
    message: 'Invalid Login',
    code: 401,
  },

  [errorKeys.USER_EXISTS_AS_VERIFIED]: {
    message: 'User already exists',
    code: 402,
  },

  [errorKeys.ARGUMENT_VALIDATION_ERROR]: {
    message: 'Invalid Input',
    code: 403,
  },

  [errorKeys.UNVERIFIED_USER_NOT_FOUND]: {
    message: 'Unverified User not found',
    code: 404,
  },

  [errorKeys.UNAUTHORIZED]: {
    message: 'You are not authorized to perform this action',
    code: 405,
  },

  [errorKeys.TENANT_NOT_FOUND]: {
    message: 'Tenant not found',
    code: 406,
  },

  [errorKeys.OBJECT_ALREADY_EXISTS]: {
    message: 'This item already exists',
    code: 407,
  },

  [errorKeys.OBJECT_NOT_FOUND]: {
    message: 'This item was not found',
    code: 408,
  },

  [errorKeys.GRAPHQL_VALIDATION_ERROR]: {
    message: 'GraphQL validation error',
    code: 409,
  },

  [errorKeys.DEFAULT]: {
    message: 'Something went wrong 😖',
    code: 500,
  },

  [errorKeys.FAILED_TO_GENERATE_FILE]: {
    message: 'Failed to generate file',
    code: 501,
  },
};
