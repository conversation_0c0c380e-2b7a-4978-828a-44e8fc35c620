/* eslint-disable @typescript-eslint/no-inferrable-types */
import { Attachment } from 'core/entities/Attachment';
import { Category } from 'core/entities/Category';
import { CurationEvent } from 'core/entities/CurationEvent';
import { Opportunity } from 'core/entities/Opportunity';
import { Project } from 'core/entities/Project';
import { Stakeholder } from 'core/entities/Stakeholder';
import { Tenant } from 'core/entities/Tenant';
import { TenantAlias } from 'core/entities/TenantAlias';
import { User } from 'core/entities/User';
import { ClassType, Field, Int, ObjectType } from 'type-graphql';
import { TenantInfo } from './TenantInfo';
import { Submission } from 'core/entities/Submission';
import { CoreEntity } from 'core/entities/CoreEntity';
import { Owner } from 'core/entities/Owner';
import { OpportunityOwnerStatus } from 'core/entities/OpportunityOwnerStatus';

@ObjectType({ simpleResolvers: true })
export class PageInfo {
  @Field()
  hasNext: boolean = false;
  @Field()
  hasPrevious: boolean = false;
  @Field()
  lastCursor?: string;
  @Field(() => Int)
  lastPageSize: number = 0;
  @Field(() => Int)
  retrievedCount: number = 0;
  @Field(() => Int)
  totalCount: number = 0;
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export default function Page<T>(TClass: ClassType<T>) {
  // `isAbstract` decorator option is mandatory to prevent registering in schema
  @ObjectType({ isAbstract: true, simpleResolvers: true })
  abstract class PageClass {
    @Field(() => [TClass])
    results: T[] = [];
    @Field(() => PageInfo)
    pageInfo!: PageInfo;
  }
  return PageClass;
}

/* Query Types */

export class AnyPage extends Page(CoreEntity) {}
@ObjectType({ simpleResolvers: true })
export class OpportunityPage extends Page(Opportunity) {}
@ObjectType({ simpleResolvers: true })
export class SubmissionPage extends Page(Submission) {}
@ObjectType({ simpleResolvers: true })
export class UserPage extends Page(User) {}
@ObjectType({ simpleResolvers: true })
export class CategoryPage extends Page(Category) {}
@ObjectType({ simpleResolvers: true })
export class StakeholderPage extends Page(Stakeholder) {}
@ObjectType({ simpleResolvers: true })
export class AttachementPage extends Page(Attachment) {}
@ObjectType({ simpleResolvers: true })
export class CurationEventPage extends Page(CurationEvent) {}
@ObjectType({ simpleResolvers: true })
export class TenantAliasPage extends Page(TenantAlias) {}
@ObjectType({ simpleResolvers: true })
export class ProjectPage extends Page(Project) {}
@ObjectType({ simpleResolvers: true })
export class TenantPage extends Page(Tenant) {}
@ObjectType({ simpleResolvers: true })
export class TenantInfoPage extends Page(TenantInfo) {}
@ObjectType({ simpleResolvers: true })
export class OpportunityOwnerPage extends Page(OpportunityOwnerStatus) {}
@ObjectType({ simpleResolvers: true })
export class OwnerPage extends Page(Owner) {}
