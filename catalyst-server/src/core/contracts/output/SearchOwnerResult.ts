import { Field, ObjectType } from 'type-graphql';

@ObjectType()
export class SearchOwnerResult {
  @Field()
  firstName!: string;

  @Field()
  lastName!: string;

  @Field()
  emailAddress!: string;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  org1?: string;

  @Field({ nullable: true })
  org2?: string;

  @Field({ nullable: true })
  org3?: string;

  @Field({ nullable: true })
  org4?: string;

  @Field({ nullable: true })
  altContact?: string;

  @Field({ nullable: true })
  organizationRole?: string;

  @Field(() => String, { nullable: true })
  source!: 'USER' | 'OWNER';

  @Field({ nullable: true })
  id?: string;
}
