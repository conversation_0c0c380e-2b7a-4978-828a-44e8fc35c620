import { Field, ObjectType } from 'type-graphql';
import { AnyScalar } from 'core/contracts/types/AnyScalar';

@ObjectType({ simpleResolvers: true })
export class CalcResponse {
  @Field(() => [OperationResult])
  operationResults: OperationResult[] = [];
}

@ObjectType({ simpleResolvers: true })
export class OperationResult {
  @Field(() => AnyScalar, { nullable: true })
  result?: string | number | boolean | Date | unknown[];
}
