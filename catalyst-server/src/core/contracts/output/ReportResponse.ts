import { Field, ObjectType } from 'type-graphql';
import { AnyScalar } from 'core/contracts/types/AnyScalar';
import { JSONType } from '../types/JSONType';

@ObjectType({ simpleResolvers: true })
export class ReportResponse {
    @Field(() => [Report])
    reports!: Report[];
}

@ObjectType({ simpleResolvers: true })
export class Report {
    @Field()
    name!: string;
    @Field()
    label!: string;
    @Field(() => JSONType, { nullable: true })
    data?: any;
}