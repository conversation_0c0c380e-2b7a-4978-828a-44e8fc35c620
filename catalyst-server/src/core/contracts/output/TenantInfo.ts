import { Tenant } from 'core/entities/Tenant';
import { TenantAlias } from 'core/entities/TenantAlias';
import { TenantMeta } from 'core/entities/TenantMeta';
import { Field, ID, ObjectType } from 'type-graphql';

@ObjectType({ simpleResolvers: true })
export class TenantInfo implements Partial<Tenant> {
  static serverVersion: string;

  @Field()
  tenantId!: string;
  @Field()
  name!: string;
  @Field()
  handle!: string;
  @Field({ nullable: true })
  label?: string;
  @Field(() => TenantMeta, { nullable: true })
  meta?: TenantMeta = TenantMeta.newTenantMeta({});
  @Field()
  serverVersion!: string;

  static get(tenant: Tenant, tenantAlias?: TenantAlias | null): TenantInfo {
    return {
      tenantId: tenant.id,
      name: tenantAlias?.name || tenant.name,
      handle: tenantAlias?.handle || tenant.handle,
      label: tenantAlias?.label || tenant.label,
      meta: tenantAlias?.meta || tenant.meta,
      serverVersion: TenantInfo.serverVersion,
    };
  }
}
