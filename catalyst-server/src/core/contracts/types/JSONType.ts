import { GraphQLScalarType, ValueNode, ObjectValueNode, ObjectFieldNode } from 'graphql';
import { Kind, print } from 'graphql/language';
import { Maybe } from 'type-graphql';

function ensureObject(value: unknown): unknown {
  if ((typeof(value) !== 'object' && !Array.isArray(value)) || value === null) {
    throw new TypeError(`JSONObject cannot represent non-object or array value: ${value}`);
  }
  return value;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function parseObject(typeName: string, ast: ValueNode, variables: Maybe<{ [key: string]: any }>) {
  const value = Object.create(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (ast as ObjectValueNode)?.fields.forEach((field: ObjectFieldNode) => {
    // eslint-disable-next-line no-use-before-define
    value[field.name.value] = parseLiteral(typeName, field.value, variables);
  });

  return value;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function parseLiteral(typeName: string, ast: ValueNode, variables: Maybe<{ [key: string]: any }>): Maybe<any> {
  switch (ast.kind) {
    case Kind.STRING:
    case Kind.BOOLEAN:
      return ast.value;
    case Kind.INT:
    case Kind.FLOAT:
      return parseFloat(ast.value);
    case Kind.OBJECT:
      return parseObject(typeName, ast, variables);
    case Kind.LIST:
      return ast.values.map((n) => parseLiteral(typeName, n, variables));
    case Kind.NULL:
      return null;
    case Kind.VARIABLE:
      return variables ? variables[ast.name.value] : undefined;
    default:
      throw new TypeError(`${typeName} cannot represent value: ${print(ast)}`);
  }
}

export const JSONType = new GraphQLScalarType({
  name: 'JSONObject',
  description:
    'The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).',
  specifiedByUrl: 'http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf',
  serialize: ensureObject,
  parseValue: ensureObject,
  parseLiteral: (ast, variables) => {
    if (ast.kind !== Kind.OBJECT && ast.kind !== Kind.LIST) {
      throw new TypeError(`JSONObject cannot represent non-object value: ${print(ast)}`);
    }

    return parseObject('JSONObject', ast, variables);
  },
});
