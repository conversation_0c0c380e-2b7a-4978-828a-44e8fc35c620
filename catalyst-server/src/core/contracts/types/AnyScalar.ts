import { GraphQLScalarType, Kind, ListValueNode } from 'graphql';

export const AnyScalar = new GraphQLScalarType({
  name: 'AnyScalar',
  description: 'Any scalar value (int, string, boolean, <date iso string>, or primitive array)',
  serialize(value?: unknown): string | boolean | number | Date | unknown[] | undefined {
    if (value === undefined) return value;
    if (typeof value !== 'string' && typeof value !== 'number' && typeof value !== 'boolean' && !Array.isArray(value)) {
      throw new Error('AnyScalar: not a string, number, boolean, or primative array');
    }
    if (typeof value === 'string') {
      if (value.endsWith('Z')) {
        // try to parse an ISO date string
        const dateVal = Date.parse(value);
        if (!isNaN(dateVal)) return new Date(dateVal);
      }
    }
    return value;
  },
  parseValue(value?: unknown): string | boolean | number | Date | unknown[] | undefined {
    if (value === undefined) return value;
    if (typeof value !== 'string' && typeof value !== 'number' && typeof value !== 'boolean' && !Array.isArray(value)) {
      throw new Error('AnyScalar: not a string, number, boolean, or primitive array');
    }
    if (typeof value === 'string') {
      if (value.endsWith('Z')) {
        // try to parse an ISO date string
        const dateVal = Date.parse(value);
        if (!isNaN(dateVal)) return new Date(dateVal);
      }
    }
    return value;
  },
  parseLiteral(ast): string | boolean | number | Date | unknown[] | undefined {
    if (
      ast.kind !== Kind.STRING &&
      ast.kind !== Kind.INT &&
      ast.kind !== Kind.FLOAT &&
      ast.kind !== Kind.BOOLEAN &&
      ast.kind !== Kind.ENUM &&
      ast.kind !== Kind.LIST
    ) {
      throw new Error('AnyScalar: Kind not STRING, INT, FLOAT, BOOLEAN, ENUM, or primative array');
    }
    if (ast.kind === Kind.STRING) {
      // try to parse an ISO date string
      if (ast.value.endsWith('Z')) {
        const dateVal = Date.parse(ast.value);
        if (!isNaN(dateVal)) return new Date(dateVal);
      }
    }
    if (ast.kind === Kind.LIST) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (ast as ListValueNode).values.map((item) => (item as any).value);
    }
    return ast.value;
  },
});
