/**
 * Specifies the type of search operation to perform with the given value.
 * Operators are 'equal', 'not equal', 'substring match', 'greater than',
 * 'less than', 'greater than or equal to', 'less than or equal to', 'in given array', 'not in given array'
 */
export enum SearchOperator {
  /** Equal to */
  EQ = '=',
  /** * Not equal to */
  NE = '!=',
  /** Matches the given substring */
  MATCH = '~',
  /** * Greater than */
  GT = '>',
  /** * Less than */
  LT = '<',
  /** * Greater than or equal to */
  GTE = '>=',
  /** * Less than or equal to */
  LTE = '<=',
  /** * Matches any value in the given array */
  IN = 'in',
  /** * Matches in value not in the given array */
  NIN = 'nin',
}
