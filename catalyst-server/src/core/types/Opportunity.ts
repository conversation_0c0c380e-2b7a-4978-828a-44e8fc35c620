import { Opportunity as _Opportunity } from 'core/entities/Opportunity';
import { Tenant } from './Tenant';
import { Attachment } from './Attachment';
import { User } from './User';
import { Category } from './Category';
import { Stakeholder } from './Stakeholder';
import { Submission } from './Submission';
import { OpportunityOwnerStatus } from 'core/entities/OpportunityOwnerStatus';
/**
 * An opportunity for improvement or innovation
 */
export interface Opportunity
  extends Pick<
    _Opportunity,
    | 'id'
    | 'createdAt'
    | 'updatedAt'
    | 'lastCurated'
    | 'title'
    | 'statement'
    | 'status'
    | 'context'
    | 'org1'
    | 'org2'
    | 'org3'
    | 'org4'
    | 'benefits'
    | 'solutionConcepts'
    | 'additionalNotes'
    | 'campaign'
    | 'campaignNotes'
    | 'function'
    | 'statusNotes'
    | 'priority'
    | 'priorityNotes'
    | 'solutionPathway'
    | 'armyModernizationPriority'
    | 'solutionPathwayDetails'
    | 'attachmentNotes'
    | 'initiatives'
    | 'endorsements'
    | 'echelonApplicability'
    | 'isTiCLOE'
    | 'transitionInContactLineOfEffort'
    | 'operationalRules'
    | 'capabilityArea'
  > {
  tenant: Tenant;
  user: User;
  categories: Category[];
  stakeholders: Stakeholder[];
  opportunityOwnerStatuses: OpportunityOwnerStatus[];
  linkedOpportunityIds: string[];
  parentOpportunitityIds: string[];
  childOpportunityIds: string[];
  attachments: Attachment[];
  submissions: Submission[];
}
