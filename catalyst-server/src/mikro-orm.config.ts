import { MikroORM, ReflectMetadataProvider } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { ChangeEventSubscriber } from 'core/subscribers/change-event';

const usingTsNodeDev: boolean = process.env.NODE_DEV === 'true';
const isProduction: boolean = process.env.NODE_ENV === 'production';

export default {
  // debug: ['query', 'query-params'],
  metadataProvider: ReflectMetadataProvider,
  subscribers: [ChangeEventSubscriber],
  migrations: {
    path: process.env.MIGRATION_PATH,
    tableName: 'migrations',
    transactional: true,
    disableForeignKeys: false,
    allOrNothing: true,
  },
  tsNode: usingTsNodeDev,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  dbName: process.env.POSTGRES_DB,
  host: process.env.POSTGRES_HOST,
  port: 5432,
  entities: ['dist/**/entities/*.js'],
  entitiesTs: ['src/**/entities/*.ts'],
  driver: PostgreSqlDriver,
  driverOptions: {
    connection: {
      ssl: isProduction,
    },
  },
} as Parameters<typeof MikroORM.init>[0];
