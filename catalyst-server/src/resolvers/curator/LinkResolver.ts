import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { AddLinkInput, LinkLinks, UpdateLinkInput } from 'core/contracts/input/curator/LinkInput';
import { LinkController } from 'core/controllers/curator/LinkController';
import { AppContext } from 'core/core';
import { Link } from 'core/entities/Link';
import { Arg, Authorized, Ctx, Mutation, Resolver } from 'type-graphql';

@Resolver()
export class LinkResolver {
  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Link)
  async addLink(
    @Arg('input') input: AddLinkInput,
    @Arg('links') links: LinkLinks,
    @Ctx() ctx: AppContext,
  ): Promise<Link> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    if (!userId) throw new Error(errorKeys.UNAUTHORIZED);
    return LinkController.addLink({
      em: ctx.em,
      input,
      tenantId,
      userId,
      opportunityId: links.opportunityId,
      projectId: links.projectId,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Link, { description: 'Update an existing Link' })
  async updateLink(@Arg('input') input: UpdateLinkInput, @Ctx() ctx: AppContext): Promise<Link> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    if (!userId) throw new Error(errorKeys.UNAUTHORIZED);
    return LinkController.updateLink(ctx.em, input, tenantId, userId);
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete the Link' })
  public async deleteLink(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    return LinkController.deleteLink(ctx, id);
  }
}
