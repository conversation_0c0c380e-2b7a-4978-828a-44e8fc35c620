import { AppContext } from 'core/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CurationEventPage } from 'core/contracts/output/Page';
import { CurationEvent } from 'core/entities/CurationEvent';
import { Arg, Authorized, Ctx, Info, Int, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import {
  caseInsensitiveMatchValue,
  combineSortFields,
  defaultSecondarySortFields,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
} from 'core/storage/queryUtils';
import { GraphQLResolveInfo } from 'graphql';
import { PayloadKeys } from 'core/auth/JwtPayload';

@Resolver(() => CurationEvent)
export class CurationEventResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized(RoleNames.CURATOR)
  @Query(() => CurationEventPage, { description: 'Get a page of matching CurationEvents' })
  public async queryCurationEvents(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
  ): Promise<CurationEventPage> {
    const relationPaths = fieldsToRelations(info, { root: 'results' }) as never;
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter = {
      $and: [
        { user: { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } } },
        ...getSearchFilter<CurationEvent>(searchSortInput?.searchFields),
      ],
    };
    const orderBy = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields(), [
        { fieldName: 'name', ascending: true },
      ]),
    );
    const [results, totalCount] = await ctx.em
      .getRepository(CurationEvent)
      .findAndCount(filter, { populate: relationPaths, orderBy, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => CurationEvent, { nullable: true, description: 'Get the CurationEvent' })
  public async getCurationEvent(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<CurationEvent | null> {
    const relationPaths = fieldsToRelations(info) as never;
    return ctx.em
      .getRepository(CurationEvent)
      .findOne({ id, user: { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } } }, { populate: relationPaths });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing CurationEvent' })
  public async deleteCurationEvent(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const curationEvent = await ctx.em
      .getRepository(CurationEvent)
      .findOneOrFail({ id, user: { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } } }, { fields: ['id'] });
    await ctx.em.removeAndFlush(curationEvent);
    return true;
  }
}
