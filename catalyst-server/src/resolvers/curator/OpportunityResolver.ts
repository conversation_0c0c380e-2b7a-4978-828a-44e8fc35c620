import { QueryOrder } from '@mikro-orm/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { verifyScope } from 'core/auth/authUtils';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { QueryEntity } from 'core/contracts/enums/QueryEntity';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, Calculation, Scope, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import {
  CreateOpportunityInput,
  CreateOpportunityLinks,
  UpdateOpportunityInput,
  UpdateOpportunityLinks,
} from 'core/contracts/input/curator/OpportunityInput';
import { CalcResponse } from 'core/contracts/output/CalcResponse';
import { CurationInfo } from 'core/contracts/output/CurationInfo';
import { OpportunityPage } from 'core/contracts/output/Page';
import { getTenantOpportunityFilterForScope, isEmptyScope } from 'core/controllers/controllers';
import { CurationEventController } from 'core/controllers/curator/CurationEventController';
import { OpportunityController } from 'core/controllers/curator/OpportunityController';
import { ReportController } from 'core/controllers/curator/ReportController';
import { AppContext } from 'core/core';
import { CurationEvent } from 'core/entities/CurationEvent';
import { Opportunity } from 'core/entities/Opportunity';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import { Series } from 'core/utils/Async';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, FieldResolver, Info, Mutation, Query, Resolver, Root } from 'type-graphql';

/*
  Note on CurationEvents:

*/

@Resolver(() => Opportunity)
export class OpportunityResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized(RoleNames.CURATOR)
  @Query(() => [Opportunity], {
    description: 'Get all Opportunities',
    deprecationReason: 'User queryOpportunities() instead',
  })
  public async getOpportunities(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<Opportunity[]> {
    if (isEmptyScope(scope)) return []; // empty scope returns empty array
    if (scope && !(await verifyScope({ scope, context: ctx }))) throw new Error(errorKeys.UNAUTHORIZED);
    const opportunityVisiblityFilter = await getTenantOpportunityFilterForScope(ctx, scope);
    const filter = {
      $and: [opportunityVisiblityFilter],
    };
    // exclude curationInfo because it's a virtual field
    const relationPaths = fieldsToRelations(info, { excludeFields: ['curationInfo'] }) as never;
    return ctx.em
      .getRepository(Opportunity)
      .find(filter, { populate: relationPaths, orderBy: { createdAt: QueryOrder.DESC } });
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => OpportunityPage, { description: 'Get a page of matching Opportunities' })
  public async queryOpportunities(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<OpportunityPage> {
    //const start = Date.now();
    // exclude curationInfo and include derived fields
    const relationPaths = fieldsToRelations(info, {
      root: 'results',
      excludeFields: ['curationInfo'],
      includeFields: [
        'relatedOpportunityCount',
        'childOpportunityCount',
        'parentOpportunityCount',
        'linkedOpportunityCount',
      ],
    });
    //console.log(`perf:DB:queryOpportunities took: ${Date.now() - start}ms`);
    return OpportunityController.queryOpportunities({ ctx, pagingInput, searchSortInput, relationPaths, scope });
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => CalcResponse, { description: 'Perform a calculation on Opportunities' })
  public async opportunityCalculation(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('calculation') calculation: Calculation,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<CalcResponse> {
    const customTenantFilter = await getTenantOpportunityFilterForScope(ctx, scope);
    return ReportController.calculation({
      ctx,
      calculation,
      searchSortInput,
      scope,
      entityName: QueryEntity.OPPORTUNITY,
      customTenantFilter: customTenantFilter,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => Opportunity, { nullable: true, description: 'Get the Opportunity' })
  public async getOpportunity(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Opportunity | null> {
    //const start = Date.now();
    // exclude curationInfo and include derived fields
    const relationPaths = fieldsToRelations(info, {
      excludeFields: ['curationInfo'],
      includeFields: [
        'relatedOpportunityCount',
        'childOpportunityCount',
        'parentOpportunityCount',
        'linkedOpportunityCount',
      ],
    }) as never;
    // console.log(`perf:DB:getOpportunity took ${Date.now() - start}ms`);
    return OpportunityController.getOpportunity({ id, ctx, relationPaths });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Opportunity, { description: 'Create a new Opportunity' })
  public async createOpportunity(
    @Arg('input') input: CreateOpportunityInput,
    @Arg('links') links: CreateOpportunityLinks,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Opportunity> {
    const tenant = await ctx.em.getRepository(Tenant).findOne({ id: ctx.token?.[PayloadKeys.TENANT_KEY] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const user = await ctx.em
      .getRepository(User)
      .findOneOrFail(
        { id: links.userId, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } },
        { populate: fieldsToRelations(info, { root: 'user' }) as never },
      );
    const opportunity = Opportunity.newOpportunity(
      {
        ...input,
        org1: user.org1,
        org2: user.org2,
        org3: user.org3,
        org4: user.org4,
      },
      tenant,
    );
    opportunity.user = user;
    await ctx.em.persist(opportunity).flush();
    /*
      NOTE: We only have a CREATE CurationEvent if the Opportunity is directly created by the Curator
    */
    await CurationEventController.updateCurationEvent(
      ctx.em,
      user,
      EntityType.OPPORTUNITY,
      opportunity.id,
      CurationEventType.CREATE,
    );
    return opportunity;
  }

  /*
    @TODO add ability to modify 'user' relationship
  */
  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Opportunity, { description: 'Update an existing Opportunity' })
  public async updateOpportunity(
    @Arg('input') input: UpdateOpportunityInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: UpdateOpportunityLinks,
  ): Promise<Opportunity> {
    //const start = Date.now();
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    if (!userId) throw new Error(errorKeys.UNAUTHORIZED);

    // exclude curationInfo because it's a virtual field
    const relationPaths = fieldsToRelations(info, { excludeFields: ['curationInfo'] }) as never;
    const result = await OpportunityController.updateOpportunity(
      ctx.em,
      id,
      tenantId,
      userId,
      input,
      relationPaths,
      fieldsToRelations(info, { root: 'categories' }),
      fieldsToRelations(info, { root: 'stakeholders' }),
      fieldsToRelations(info, { root: 'opportunities' }),
      links,
    );
    // console.log(`perf:DB:updateOpportunity took: ${Date.now() - start}ms`);
    return result;
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing Opportunity' })
  public async deleteOpportunity(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const opportunity = await ctx.em.getRepository(Opportunity).findOneOrFail(
      { id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } },
      {
        populate: [
          'opportunities',
          'ownedOpportunities.target.owningOpportunities',
          'owningOpportunities.source.ownedOpportunities',
        ],
      },
    );
    await Series.forEach(opportunity.opportunities.getItems(), async (relatedOpp) => {
      if (!relatedOpp.opportunities.isInitialized()) await relatedOpp.opportunities.init();
      relatedOpp.opportunities.remove(opportunity);
    });

    // Cascade all takes care of ownedOpportunities and owningOpportunities (only the association class)

    const oppId = opportunity.id;
    await ctx.em.removeAndFlush(opportunity);
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const user = ctx.em.getRepository(User).getReference(ctx.token![PayloadKeys.USER_KEY]);
    await CurationEventController.updateCurationEvent(
      ctx.em,
      user,
      EntityType.OPPORTUNITY,
      oppId,
      CurationEventType.DELETE,
      new Date(),
    );
    return true;
  }

  @Authorized(RoleNames.CURATOR)
  @FieldResolver(() => CurationInfo, { nullable: true })
  async curationInfo(@Root() opportunity: Opportunity, @Ctx() ctx: AppContext): Promise<CurationInfo> {
    const curationEvents = await ctx.em.getRepository(CurationEvent).find(
      {
        entityId: opportunity.id,
        entityType: EntityType.OPPORTUNITY,
      },
      {
        populate: ['user'],
        orderBy: { updatedAt: QueryOrder.DESC },
      },
    );
    if (!curationEvents?.length) return { users: [] };
    const users = new Set();

    let lastCurated: Date | null | undefined = null;
    curationEvents.forEach((curationEvent: CurationEvent) => {
      if (curationEvent.user) {
        users.add(curationEvent.user);
      }
      if (curationEvent.type === CurationEventType.UPDATE) {
        if (!lastCurated || (curationEvent.updatedAt && curationEvent.updatedAt.getTime() > lastCurated.getTime())) {
          lastCurated = curationEvent.updatedAt;
        }
      }
    });

    return { users: [...users] as User[], lastCurated };
  }
}
