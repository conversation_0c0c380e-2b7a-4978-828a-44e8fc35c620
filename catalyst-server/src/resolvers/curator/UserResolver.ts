import { AppContext } from 'core/core';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { CreateUserInput, UpdateUserInput, UserLinks } from 'core/contracts/input/curator/UserInput';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { EntityManager, QueryOrder } from '@mikro-orm/core';
import { Role } from 'core/entities/Role';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { UserPage } from 'core/contracts/output/Page';
import {
  caseInsensitiveMatchValue,
  combineSortFields,
  defaultSecondarySortFields,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
} from 'core/storage/queryUtils';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { UserController } from 'core/controllers/curator/UserController';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { PayloadKeys } from 'core/auth/JwtPayload';

@Resolver(() => User)
export class UserResolver {
  /***
   *       ___      _      _ __       __            __  ____
   *      / _ \____(_)  __(_) /__ ___/ /__ ____ ___/ / / __ \___  ___
   *     / ___/ __/ / |/ / / / -_) _  / _ `/ -_) _  / / /_/ / _ \(_-<
   *    /_/  /_/ /_/|___/_/_/\__/\_,_/\_, /\__/\_,_/  \____/ .__/___/
   *                                 /___/                /_/
   */

  @Authorized(RoleNames.CURATOR)
  @Query(() => [User], {
    description: 'Retrieves Users for this Tenant',
    deprecationReason: 'User queryUsers() instead',
  })
  public async getUsers(@Ctx() ctx: AppContext, @Info() info: GraphQLResolveInfo): Promise<User[]> {
    const relationPaths = fieldsToRelations(info) as never;
    const tenant = await ctx.em
      .getRepository(Tenant)
      .findOne(
        { id: ctx.token?.[PayloadKeys.TENANT_KEY] },
        { fields: ['id'], orderBy: { createdAt: QueryOrder.DESC } },
      );
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    return ctx.em.getRepository(User).find({ tenant }, { populate: relationPaths });
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => UserPage, { description: 'Get a page of matching Users' })
  public async queryUsers(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
  ): Promise<UserPage> {
    const relationPaths = fieldsToRelations(info, { root: 'results' }) as never;
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter = {
      $and: [
        { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } },
        ...getSearchFilter<User>(searchSortInput?.searchFields),
      ],
    };
    const orderBy = getSortFilter(combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields()));
    const [results, totalCount] = await ctx.em
      .getRepository(User)
      .findAndCount(filter, { populate: relationPaths, orderBy, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => User, { nullable: true, description: 'Retrieve a User' })
  public async getUser(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<User | null> {
    const relationPaths = fieldsToRelations(info) as never;
    const tenant = await ctx.em
      .getRepository(Tenant)
      .findOne({ id: ctx.token?.[PayloadKeys.TENANT_KEY] }, { fields: ['id'] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    return ctx.em.getRepository(User).findOne({ id, tenant }, { populate: relationPaths });
  }

  /***
   *       ___     __      _        ____
   *      / _ |___/ /_ _  (_)__    / __ \___  ___
   *     / __ / _  /  ' \/ / _ \  / /_/ / _ \(_-<
   *    /_/ |_\_,_/_/_/_/_/_//_/  \____/ .__/___/
   *                                  /_/
   */

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => User, { description: '(Admin) Create a new User' })
  public async createUser(
    @Arg('input') input: CreateUserInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: UserLinks,
  ): Promise<User> {
    const relationPaths = fieldsToRelations(info);
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw Error(errorKeys.TENANT_NOT_FOUND);

    const userRelationPaths = fieldsToRelations(info, { root: 'user' }) as never;
    const user: User | null = await ctx.em.getRepository(User).findOne(
      {
        emailAddress: caseInsensitiveMatchValue(input.emailAddress),
        tenant: { id: tenantId },
        status: VerifiedStatus.VERIFIED,
      },
      { populate: userRelationPaths },
    );
    if (user) throw Error(errorKeys.USER_EXISTS_AS_VERIFIED);

    return UserController.createUser(ctx.em, tenantId, input, links, relationPaths);
  }

  // @TODO add user links
  // @TODO if we allow emailAddress change, check for existing email
  @Authorized(RoleNames.ADMIN)
  @Mutation(() => User, { description: '(Admin) Update an existing User' })
  public async updateUser(
    @Arg('input') input: UpdateUserInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: UserLinks,
  ): Promise<User> {
    const relationPaths = fieldsToRelations(info);
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw Error(errorKeys.TENANT_NOT_FOUND);
    return UserController.updateUser(ctx.em, tenantId, id, input, links, relationPaths);
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => Boolean, { description: '(Admin) Delete an existing User' })
  public async deleteUser(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const tenant = await ctx.em
      .getRepository(Tenant)
      .findOne({ id: ctx.token?.[PayloadKeys.TENANT_KEY] }, { fields: ['id'] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const user = await ctx.em.getRepository(User).findOneOrFail({ id, tenant });
    await ctx.em.remove(user).flush();
    return true;
  }
}
