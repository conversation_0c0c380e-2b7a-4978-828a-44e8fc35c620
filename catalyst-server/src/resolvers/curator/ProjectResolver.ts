import { FilterQuery, QueryOrder } from '@mikro-orm/core';
import { AppContext } from 'core/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import {
  CreateProjectFromOpportunityInput,
  CreateProjectInput,
  CreateProjectLinks,
  UpdateProjectInput,
  UpdateProjectLinks,
} from 'core/contracts/input/curator/ProjectInput';
import { CurationInfo } from 'core/contracts/output/CurationInfo';
import { ProjectPage } from 'core/contracts/output/Page';
import { ProjectController } from 'core/controllers/curator/ProjectController';
import { CurationEvent } from 'core/entities/CurationEvent';
import { Project } from 'core/entities/Project';
import { User } from 'core/entities/User';
import { GraphQLResolveInfo } from 'graphql';
import { combineSortFields, defaultSecondarySortFields, getQueryBounds, getSearchFilter, getSortFilter } from 'core/storage/queryUtils';
import { Arg, Authorized, Ctx, FieldResolver, Info, Mutation, Query, Resolver, Root } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';

/*
  Note on CurationEvents:

*/

@Resolver(() => Project)
export class ProjectResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized(RoleNames.CURATOR)
  @Query(() => ProjectPage, { description: 'Get a page of matching Projects' })
  public async queryProjects(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
  ): Promise<ProjectPage> {
    // exclude curationInfo because it's a virtual field
    const relationPaths = fieldsToRelations(info, { root: 'results', excludeFields: ['curationInfo'] });
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter: FilterQuery<Project> = {
      $and: [
        { creator: { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } } },
        ...getSearchFilter<Project>(searchSortInput?.searchFields),
      ],
    };
    const sortFilter = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, [
        { fieldName: 'lastCurated', ascending: false },
        { fieldName: 'id', ascending: true },
      ])
    );
    return ProjectController.getProjectPage(ctx.em, filter, relationPaths, sortFilter, limit, offset);
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => Project, { nullable: true, description: 'Get the Project' })
  public async getProject(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Project | null> {
    const relationPaths = fieldsToRelations(info, { excludeFields: ['curationInfo'] }) as never;
    return ctx.em
      .getRepository(Project)
      .findOne({ id, creator: { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } } }, { populate: relationPaths });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Project, { description: 'Create a new Project from an Opportunity' })
  public async createProjectFromOpportunity(
    @Arg('input') input: CreateProjectFromOpportunityInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Project> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    const creatorId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    if (!creatorId) throw new Error(errorKeys.UNAUTHORIZED);
    return ProjectController.createProjectFromOpportunity({
      em: ctx.em,
      input,
      tenantId,
      creatorId,
      userPaths: fieldsToRelations(info, { root: 'creator' }),
      opportunityPaths: fieldsToRelations(info, { root: 'opportunities' }),
      categoriesPaths: fieldsToRelations(info, { root: 'categories' }),
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Project, { description: 'Create a new Project' })
  public async createProject(
    @Arg('input') input: CreateProjectInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: CreateProjectLinks,
  ): Promise<Project> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    const creatorId = links?.creatorId || ctx.token?.[PayloadKeys.USER_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    if (!creatorId) throw new Error(errorKeys.UNAUTHORIZED);
    return ProjectController.createProject(
      ctx.em,
      tenantId,
      creatorId,
      input,
      fieldsToRelations(info, { root: 'creator' }),
    );
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Project, { description: 'Update an existing Project' })
  public async updateProject(
    @Arg('input') input: UpdateProjectInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: UpdateProjectLinks,
  ): Promise<Project> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    if (!userId) throw new Error(errorKeys.UNAUTHORIZED);
    // exclude curationInfo because it's a virtual field
    const relationPaths = fieldsToRelations(info, { excludeFields: ['curationInfo'] }) as never;
    return ProjectController.updateProject(
      ctx.em,
      id,
      tenantId,
      userId,
      input,
      relationPaths,
      fieldsToRelations(info, { root: 'categories' }),
      fieldsToRelations(info, { root: 'opportunities' }),
      links,
    );
  }

  // @TODO - no need to fetch all the project fields here...
  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing Project' })
  public async deleteProject(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    if (!userId) throw new Error(errorKeys.UNAUTHORIZED);
    return ProjectController.deleteProject(ctx.em, id, tenantId, userId);
  }

  @Authorized(RoleNames.CURATOR)
  @FieldResolver(() => CurationInfo, { nullable: true })
  async curationInfo(@Root() project: Project, @Ctx() ctx: AppContext): Promise<CurationInfo> {
    const curationEvents = await ctx.em.getRepository(CurationEvent).find(
      {
        entityId: project.id,
        entityType: EntityType.PROJECT,
      },
      {
        populate: ['user'],
        orderBy: { updatedAt: QueryOrder.DESC },
      },
    );
    if (!curationEvents?.length) return { users: [] };
    const users = new Set();

    let lastCurated: Date | null | undefined = null;
    curationEvents.forEach((curationEvent: CurationEvent) => {
      if (curationEvent.user) {
        users.add(curationEvent.user);
      }
      if (curationEvent.type === CurationEventType.UPDATE) {
        if (!lastCurated || (curationEvent.updatedAt && curationEvent.updatedAt.getTime() > lastCurated.getTime())) {
          lastCurated = curationEvent.updatedAt;
        }
      }
    });

    return { users: [...users] as User[], lastCurated };
  }
}
