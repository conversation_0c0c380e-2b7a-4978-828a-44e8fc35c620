import { QueryEntity } from 'core/contracts/enums/QueryEntity';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { Calculation, ReportInput, Scope, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CalcResponse } from 'core/contracts/output/CalcResponse';
import { ReportResponse } from 'core/contracts/output/ReportResponse';
import { ReportController } from 'core/controllers/curator/ReportController';
import { AppContext } from 'core/core';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Query, Resolver } from 'type-graphql';

@Resolver()
export class ReportResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */
  @Authorized(RoleNames.CURATOR)
  @Query(() => CalcResponse, { description: 'Create a report' })
  public async calculation(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('calculation') calculation: Calculation,
    @Arg('entityName') entityName: QueryEntity,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<CalcResponse> {
    return ReportController.calculation({
      ctx, calculation, searchSortInput, scope, entityName
    });
  }

  @Authorized(RoleNames.ANALYST)
  @Query(() => ReportResponse, { description: 'Create a report' })
  public async report(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('reportInput') reportInput: ReportInput,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<ReportResponse> {
    return ReportController.report({
      ctx, reportInput, scope
    });
  }

}