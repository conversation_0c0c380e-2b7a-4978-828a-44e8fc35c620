import { QueryOrder } from '@mikro-orm/core';
import { AppContext } from 'core/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CreateCategoryInput, UpdateCategoryInput } from 'core/contracts/input/curator/CategoryInput';
import { CategoryPage } from 'core/contracts/output/Page';
import { Category } from 'core/entities/Category';
import { Tenant } from 'core/entities/Tenant';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Int, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import {
  caseInsensitiveMatchValue,
  combineSortFields,
  defaultSecondarySortFields,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
} from 'core/storage/queryUtils';
import { PayloadKeys } from 'core/auth/JwtPayload';

@Resolver(() => Category)
export class CategoryResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized(RoleNames.CURATOR)
  @Query(() => CategoryPage, { description: 'Get a page of matching Categories' })
  public async queryCategories(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
  ): Promise<CategoryPage> {
    const relationPaths = fieldsToRelations(info, { root: 'results' }) as never;
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter = {
      $and: [
        { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } },
        ...getSearchFilter<Category>(searchSortInput?.searchFields),
      ],
    };
    const orderBy = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields(), [
        { fieldName: 'name', ascending: true },
      ]),
    );
    const [results, totalCount] = await ctx.em
      .getRepository(Category)
      .findAndCount(filter, { populate: relationPaths, orderBy, limit, offset });
    return { results, pageInfo: getPageInfo(totalCount, results.length, limit, offset) };
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => Category, { nullable: true, description: 'Get the Category' })
  public async getCategory(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('id', { nullable: true }) id?: string,
    @Arg('name', { nullable: true }) name?: string,
  ): Promise<Category | null> {
    const relationPaths = fieldsToRelations(info) as never;
    const filter = id ? { id } : { name: caseInsensitiveMatchValue(name) };
    return ctx.em
      .getRepository(Category)
      .findOne({ ...filter, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } }, { populate: relationPaths });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Category, { description: 'Create a new Category' })
  public async createCategory(
    @Arg('input') input: CreateCategoryInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Category> {
    const tenant = await ctx.em.getRepository(Tenant).findOne({ id: ctx.token?.[PayloadKeys.TENANT_KEY] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    const existingCategory = await ctx.em
      .getRepository(Category)
      .findOne({ name: caseInsensitiveMatchValue(input.name), tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } });
    if (existingCategory) return existingCategory;
    const category = Category.newCategory(input);
    category.tenant = tenant;
    await ctx.em.persist(category).flush();
    return category;
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Category, { description: 'Update an existing Category' })
  public async updateCategory(
    @Arg('input') input: UpdateCategoryInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Category> {
    const relationPaths = fieldsToRelations(info) as never;
    const category = await ctx.em
      .getRepository(Category)
      .findOne({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } }, { populate: relationPaths });
    if (!category) throw Error(errorKeys.OBJECT_NOT_FOUND);
    category.modify(input);
    await ctx.em.persist(category).flush();
    await ctx.em.populate(category, relationPaths);
    return category;
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing Category' })
  public async deleteCategory(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const category = await ctx.em
      .getRepository(Category)
      .findOneOrFail({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } });
    await ctx.em.remove(category).flush();
    return true;
  }
}
