import { AppContext } from 'core/core';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { PagingInput, Scope, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { OpportunityOwnerPage } from 'core/contracts/output/Page';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PayloadKeys } from 'core/auth/JwtPayload';
import {
  CreateOpportunityOwnerInput,
  UpdateOpportunityOwnerInput,
  OpportunityOwnerLinks,
} from 'core/contracts/input/curator/OpportunityOwnerStatus';
import { OpportunityOwnerController } from 'core/controllers/curator/OpportunityOwnerController';
import { verifyScope } from 'core/auth/authUtils';
import { isEmptyScope } from 'core/controllers/controllers';
import { getEmptyPageInfo } from 'core/storage/queryUtils';
import { OpportunityOwnerStatus } from 'core/entities/OpportunityOwnerStatus';
import { Tenant } from 'core/entities/Tenant';

type VerifyEntry = {
  tenantId: string;
  creatorId: string;
};

@Resolver(() => OpportunityOwnerStatus)
export class OpportunityOwnerResolver {
  @Query(() => OpportunityOwnerPage, { description: 'Get a page of matching Owners' })
  public async queryOpportunityOwners(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<OpportunityOwnerPage> {
    if (isEmptyScope(scope)) return { results: [], pageInfo: getEmptyPageInfo() }; // empty scope returns empty page
    if (scope && !(await verifyScope({ scope, context: ctx }))) throw new Error(errorKeys.UNAUTHORIZED);
    const { tenantId } = OpportunityOwnerResolver.verifyTenant(ctx);
    const relationPaths = fieldsToRelations(info, { root: 'results' });
    return OpportunityOwnerController.queryOpportunityOwners({
      em: ctx.em,
      tenantId,
      pagingInput,
      searchSortInput,
      relationPaths,
    });
  }

  @Query(() => OpportunityOwnerStatus, { nullable: true, description: 'Retrieve an Owner by its owner id' })
  public async getOpportunityOwner(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<OpportunityOwnerStatus | null> {
    const { tenantId } = OpportunityOwnerResolver.verifyTenant(ctx);
    const relationPaths = fieldsToRelations(info) as never;
    return OpportunityOwnerController.getOpportunityOwner({ em: ctx.em, id, tenantId, relationPaths });
  }

  @Mutation(() => OpportunityOwnerStatus, { description: 'Create a new Owner for opportunity' })
  public async createOpportunityOwner(
    @Arg('input') input: CreateOpportunityOwnerInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: OpportunityOwnerLinks,
  ): Promise<OpportunityOwnerStatus> {
    const { tenantId } = OpportunityOwnerResolver.verifyTenant(ctx);
    const opportunityId = input.opportunityId;
    if (!opportunityId) throw Error(errorKeys.ARGUMENT_VALIDATION_ERROR);
    const relationPaths = fieldsToRelations(info);
    const ownerPath = fieldsToRelations(info, { root: 'owner' });
    const opportunityPaths = fieldsToRelations(info, { root: 'opportunities' });

    const { em } = ctx;
    const tenant = await em.getRepository(Tenant).findOne({ id: tenantId });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);
    return OpportunityOwnerController.createOpportunityOwner(
      ctx.em,
      tenantId,
      input,
      relationPaths,
      ownerPath,
      opportunityPaths,
      links,
    );
  }

  @Mutation(() => OpportunityOwnerStatus, { description: 'Update an existing Owner' })
  public async updateOpportunityOwner(
    @Arg('input') input: UpdateOpportunityOwnerInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: OpportunityOwnerLinks,
  ): Promise<OpportunityOwnerStatus> {
    const { tenantId } = OpportunityOwnerResolver.verifyTenant(ctx);
    const relationPaths = fieldsToRelations(info);
    const ownerPath = fieldsToRelations(info, { root: 'owner' });
    const opportunityPaths = fieldsToRelations(info, { root: 'opportunities' });

    return OpportunityOwnerController.updateOpportunityOwner(
      ctx.em,
      tenantId,
      id,
      input,
      relationPaths,
      ownerPath,
      opportunityPaths,
      links,
    );
  }

  private static verifyTenant(ctx: AppContext): VerifyEntry {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw Error(errorKeys.TENANT_NOT_FOUND);
    const creatorId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!creatorId) throw new Error(errorKeys.UNVERIFIED_USER_NOT_FOUND);
    return { tenantId, creatorId };
  }
}
