import { PayloadKeys } from 'core/auth/JwtPayload';
import { OpportunityOwnerStatus } from 'core/contracts/enums/OpportunityOwnerStatus';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { AssignOpportunityOwnerInput } from 'core/contracts/input/curator/OpportunityOwnerInput';
import { SearchOwnerPage } from 'core/contracts/output/Page';
import { SearchOwnerResult } from 'core/contracts/output/SearchOwnerResult';
import { AppContext } from 'core/core';
import { Opportunity } from 'core/entities/Opportunity';
import { OpportunityOwner } from 'core/entities/OpportunityOwner';
import { Owner } from 'core/entities/Owner';
import { Tenant } from 'core/entities/Tenant';
import { User } from 'core/entities/User';
import {
  combineSortFields,
  defaultSecondarySortFields,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
} from 'core/storage/queryUtils';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';

@Resolver(() => OpportunityOwner)
export class OpportunityOwnerResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */
  @Authorized(RoleNames.CURATOR)
  @Mutation(() => OpportunityOwner, {
    description: 'Adds a new "CURRENT" Opportunity Owner, and sets previous owners to "PREVIOUS" status',
  })
  public async assignOwner(
    @Arg('input') input: AssignOpportunityOwnerInput,
    @Ctx() ctx: AppContext,
  ): Promise<OpportunityOwner> {
    const now = new Date();
    const em = ctx.em;
    const opportunity = await em.findOneOrFail(Opportunity, input.opportunityId);
    const currentOwners = await em.find(OpportunityOwner, {
      status: OpportunityOwnerStatus.CURRENT,
      opportunity: input.opportunityId,
    });

    // Mark all previous owners as PREVIOUS
    for (const owner of currentOwners) {
      if (owner.status !== OpportunityOwnerStatus.INITIAL) {
        owner.status = OpportunityOwnerStatus.PREVIOUS;
        owner.madePreviousAt = now;
      }
    }

    const owner = new Owner();
    owner.emailAddress = input.emailAddress;
    owner.firstName = input.firstName;
    owner.lastName = input.lastName;
    owner.org1 = input.org1;
    owner.org2 = input.org2;
    owner.org3 = input.org3;
    owner.org4 = input.org4;
    owner.phone = input.phone;
    owner.altContact = input.altContact;
    owner.organizationRole = input.organizationRole;

    const newOpportunityOwner = new OpportunityOwner();
    newOpportunityOwner.owner = owner;
    newOpportunityOwner.opportunity = opportunity;
    newOpportunityOwner.status = OpportunityOwnerStatus.CURRENT;
    newOpportunityOwner.addedAt = now;

    em.persist([owner, newOpportunityOwner]);
    await em.flush();

    const freshNewOwner = await em.findOneOrFail(OpportunityOwner, newOpportunityOwner.id, {
      populate: ['owner'],
    });
    return freshNewOwner;
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => SearchOwnerPage, {
    description: 'Paginated search across Users and Opportunity Owners',
  })
  public async searchOwners(
    @Info() info: GraphQLResolveInfo,
    @Ctx() ctx: AppContext,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
  ): Promise<SearchOwnerPage> {
    const relationPaths = fieldsToRelations(info, { root: 'results' }) as never;
    const { limit, offset } = getQueryBounds(pagingInput);

    const filter = {
      $and: [
        { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } },
        ...getSearchFilter<User & Owner>(searchSortInput?.searchFields),
      ],
    };

    const orderBy = getSortFilter(combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields()));

    const [userResults] = await ctx.em
      .getRepository(User)
      .findAndCount(filter, { populate: relationPaths, orderBy, limit, offset });

    const mappedUsers: SearchOwnerResult[] = userResults.map((user) => ({
      firstName: user.firstName,
      lastName: user.lastName,
      emailAddress: user.emailAddress,
      phone: user.phone,
      altContact: user.altContact,
      org1: user.org1,
      org2: user.org2,
      org3: user.org3,
      org4: user.org4,
      source: 'USER',
      id: user.id,
    }));

    const [ownerResults] = await ctx.em
      .getRepository(Owner)
      .findAndCount(filter, { populate: relationPaths, orderBy, limit, offset });

    const mappedOwners: SearchOwnerResult[] = ownerResults.map((o) => ({
      firstName: o.firstName,
      lastName: o.lastName,
      emailAddress: o.emailAddress,
      org1: o.org1,
      org2: o.org2,
      org3: o.org3,
      org4: o.org4,
      altContact: o.altContact,
      organizationRole: o.organizationRole,
      phone: o.phone,
      source: 'OWNER',
      id: o.id,
    }));

    const seenOwnerEmails = new Set<string>();
    const dedupedOwners: SearchOwnerResult[] = [];
    for (const o of ownerResults) {
      const email = o.emailAddress?.toLowerCase();
      if (email && !seenOwnerEmails.has(email)) {
        seenOwnerEmails.add(email);
        dedupedOwners.push({
          firstName: o.firstName,
          lastName: o.lastName,
          emailAddress: o.emailAddress,
          phone: o.phone,
          source: 'OWNER',
          id: o.id,
        });
      }
    }
    const ownerEmails = new Set(mappedOwners.map((o) => o.emailAddress?.toLowerCase()));

    const filteredUsers = mappedUsers.filter((user) => !ownerEmails.has(user.emailAddress?.toLowerCase()));

    let combined = [...filteredUsers, ...dedupedOwners];

    combined.sort((a, b) => a.lastName.localeCompare(b.lastName));

    const totalCount = combined.length;
    const results = combined.slice(offset, offset + limit);

    return {
      results,
      pageInfo: getPageInfo(totalCount, results.length, limit, offset),
    };
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => OpportunityOwner, { description: 'Toggle isRemoved on an existing OpportunityOwner' })
  public async toggleRemovedOwner(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<OpportunityOwner> {
    const opportunityOwner = await ctx.em.getRepository(OpportunityOwner).findOneOrFail({ id });
    const newRemoved = !opportunityOwner.isRemoved;
    if (newRemoved) {
      opportunityOwner.modify({ isRemoved: newRemoved, removedAt: new Date() });
    } else {
      opportunityOwner.modify({ isRemoved: newRemoved, removedAt: null });
    }

    await ctx.em.persist(opportunityOwner).flush();

    return opportunityOwner;
  }
}
