import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { AddAttachmentInput, AttachmentLinks } from 'core/contracts/input/curator/AttachmentInput';
import { Location } from 'core/contracts/output/Location';
import { AttachmentController } from 'core/controllers/curator/AttachmentController';
import { AppContext } from 'core/core';
import { Attachment } from 'core/entities/Attachment';
import { GraphQLResolveInfo } from 'graphql';
import { GraphQLUpload } from 'graphql-upload';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';

@Resolver()
export class AttachmentResolver {
  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Attachment)
  async addAttachment(
    @Arg('input', () => GraphQLUpload) input: AddAttachmentInput,
    @Arg('links') links: AttachmentLinks,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Attachment> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    return AttachmentController.addAttachment({
      em: ctx.em,
      input,
      tenantId,
      opportunityId: links.opportunityId,
      projectId: links.projectId,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => Location, { description: 'Get the Attachment Location' })
  public async getAttachmentLocation(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<Location | null> {
    return AttachmentController.getAttachmentLocation(ctx, id);
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete the Attachment' })
  public async deleteAttachment(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    return AttachmentController.deleteAttachment(ctx, id);
  }
}
