import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  CreateExistingSolutionInput,
  UpdateExistingSolutionInput,
  ExistingSolutionLinks,
} from 'core/contracts/input/curator/ExistingSolutionInput';
import { ExistingSolutionController } from 'core/controllers/curator/ExistingSolutionController';
import { AppContext } from 'core/core';
import { ExistingSolution } from 'core/entities/ExistingSolution';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';

@Resolver(() => ExistingSolution)
export class ExistingSolutionResolver {
  @Authorized(RoleNames.CURATOR)
  @Query(() => ExistingSolution, { nullable: true, description: 'Get an ExistingSolution by ID' })
  public async getExistingSolution(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution | null> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as string[];
    return ExistingSolutionController.getExistingSolution({
      em: ctx.em,
      id,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => [ExistingSolution], { description: 'Get all ExistingSolutions for an Opportunity' })
  public async getExistingSolutionsByOpportunity(
    @Arg('opportunityId') opportunityId: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution[]> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as string[];
    return ExistingSolutionController.getExistingSolutionsByOpportunity({
      em: ctx.em,
      opportunityId,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => ExistingSolution, { description: 'Create a new ExistingSolution' })
  public async createExistingSolution(
    @Arg('input') input: CreateExistingSolutionInput,
    @Arg('links') links: ExistingSolutionLinks,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as string[];
    return ExistingSolutionController.createExistingSolution({
      em: ctx.em,
      input,
      opportunityId: links.opportunityId,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => ExistingSolution, { description: 'Update an existing ExistingSolution' })
  public async updateExistingSolution(
    @Arg('id') id: string,
    @Arg('input') input: UpdateExistingSolutionInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info);

    return ExistingSolutionController.updateExistingSolution({
      em: ctx.em,
      id,
      input,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing ExistingSolution' })
  public async deleteExistingSolution(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    return ExistingSolutionController.deleteExistingSolution({
      em: ctx.em,
      id,
      tenantId,
    });
  }
}
