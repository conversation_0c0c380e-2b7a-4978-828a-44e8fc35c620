import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  CreateExistingSolutionInput,
  UpdateExistingSolutionInput,
  ExistingSolutionLinks,
} from 'core/contracts/input/curator/ExistingSolutionInput';
import { AppContext } from 'core/core';
import { ExistingSolution } from 'core/entities/ExistingSolution';
import { Opportunity } from 'core/entities/Opportunity';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';

@Resolver(() => ExistingSolution)
export class ExistingSolutionResolver {
  @Authorized(RoleNames.CURATOR)
  @Query(() => ExistingSolution, { nullable: true, description: 'Get an ExistingSolution by ID' })
  public async getExistingSolution(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution | null> {
    const relationPaths = fieldsToRelations(info) as never;
    return ctx.em.getRepository(ExistingSolution).findOne(
      {
        id,
        opportunity: { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } },
      },
      { populate: relationPaths },
    );
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => [ExistingSolution], { description: 'Get all ExistingSolutions for an Opportunity' })
  public async getExistingSolutionsByOpportunity(
    @Arg('opportunityId') opportunityId: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution[]> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as never;
    return ctx.em.getRepository(ExistingSolution).find(
      {
        opportunity: {
          id: opportunityId,
          tenant: { id: tenantId },
        },
      },
      { populate: relationPaths },
    );
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => ExistingSolution, { description: 'Create a new ExistingSolution' })
  public async createExistingSolution(
    @Arg('input') input: CreateExistingSolutionInput,
    @Arg('links') links: ExistingSolutionLinks,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    // Verify the opportunity exists and belongs to the tenant
    const opportunity = await ctx.em.getRepository(Opportunity).findOne({
      id: links.opportunityId,
      tenant: { id: tenantId },
    });

    if (!opportunity) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    const existingSolution = ExistingSolution.newExistingSolution({
      ...input,
      opportunity,
    });

    await ctx.em.persist(existingSolution).flush();

    const relationPaths = fieldsToRelations(info) as never;
    await ctx.em.populate(existingSolution, relationPaths);

    return existingSolution;
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => ExistingSolution, { description: 'Update an existing ExistingSolution' })
  public async updateExistingSolution(
    @Arg('id') id: string,
    @Arg('input') input: UpdateExistingSolutionInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ExistingSolution> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as never;
    const existingSolution = await ctx.em.getRepository(ExistingSolution).findOne(
      {
        id,
        opportunity: { tenant: { id: tenantId } },
      },
      { populate: relationPaths },
    );

    if (!existingSolution) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    existingSolution.modify(input);
    await ctx.em.persist(existingSolution).flush();
    await ctx.em.populate(existingSolution, relationPaths);

    return existingSolution;
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing ExistingSolution' })
  public async deleteExistingSolution(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const existingSolution = await ctx.em.getRepository(ExistingSolution).findOne({
      id,
      opportunity: { tenant: { id: tenantId } },
    });

    if (!existingSolution) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    await ctx.em.remove(existingSolution).flush();
    return true;
  }
}
