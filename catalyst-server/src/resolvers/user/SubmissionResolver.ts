import { QueryOrder } from '@mikro-orm/core';
import { AppContext } from 'core/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  CreateSubmissionInput,
  NewSubmissionLinks,
  UpdateSubmissionInput,
} from 'core/contracts/input/user/SubmissionInput';
import { SubmissionController } from 'core/controllers/user/SubmissionController';
import { Submission } from 'core/entities/Submission';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';

@Resolver(() => Submission)
export class SubmissionResolver {
  /***
   *      __  __               __  __            __  _          __         __  ____
   *     / / / /__  ___ ___ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *    / /_/ / _ \/ _ `/ // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    \____/_//_/\_,_/\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                             /_/
   */

  @Mutation(() => Submission, { description: '(Unauthenticated) Submit an unverified Submission' })
  public async newSubmission(
    @Arg('input') input: CreateSubmissionInput,
    @Arg('links') links: NewSubmissionLinks,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Submission> {
    const userPaths = fieldsToRelations(info, { root: 'user' });
    return SubmissionController.newSubmission(ctx.em, input, links, userPaths);
  }

  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized()
  @Query(() => [Submission], { description: "Get the current User's Submissions" })
  public async getSubmissions(@Ctx() ctx: AppContext, @Info() info: GraphQLResolveInfo): Promise<Submission[]> {
    const user = ctx.token?.[PayloadKeys.USER_KEY];
    const relationPaths = fieldsToRelations(info) as never;
    return ctx.em
      .getRepository(Submission)
      .find({ user }, { populate: relationPaths, orderBy: { createdAt: QueryOrder.DESC } });
  }

  @Authorized()
  @Query(() => Submission, { nullable: true, description: 'Get the Submission (if owner)' })
  public async getSubmission(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Submission | null> {
    const user = ctx.token?.[PayloadKeys.USER_KEY];
    const relationPaths = fieldsToRelations(info) as never;
    return ctx.em.getRepository(Submission).findOne({ id, user }, { populate: relationPaths });
  }

  @Authorized()
  @Mutation(() => Submission, { description: 'Submit a verfied Submission from a logged-in User' })
  public async createSubmission(
    @Arg('input') input: CreateSubmissionInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Submission> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);
    const userPaths = fieldsToRelations(info, { root: 'user' });
    const userId = ctx.token?.[PayloadKeys.USER_KEY];
    return SubmissionController.createSubmission(ctx.em, input, userPaths, tenantId, userId);
  }

  @Authorized()
  @Mutation(() => Submission, { description: 'Update the Submission (if owner)' })
  public async updateSubmission(
    @Arg('input') input: UpdateSubmissionInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Submission> {
    const user = ctx.token?.[PayloadKeys.USER_KEY];
    const relationPaths = fieldsToRelations(info) as never;
    const submission = await ctx.em.getRepository(Submission).findOneOrFail({ id, user }, { populate: relationPaths });
    submission.assign(input);
    await ctx.em.persist(submission).flush();
    await ctx.em.populate(submission, relationPaths);
    return submission;
  }

  @Authorized()
  @Mutation(() => Boolean, { description: 'Delete the Submission (if owner)' })
  public async deleteSubmission(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const user = ctx.token?.[PayloadKeys.USER_KEY];
    const submission = await ctx.em.getRepository(Submission).findOneOrFail({ id, user }, { fields: ['id'] });
    await ctx.em.remove(submission).flush();
    return true;
  }
}
