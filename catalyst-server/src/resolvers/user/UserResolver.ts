import { AppContext } from 'core/core';
import { SubmitUserInput } from 'core/contracts/input/base/UserInput';
import { UserController } from 'core/controllers/user/UserController';
import { User } from 'core/entities/User';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Ctx, Info, Mutation, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';

@Resolver(() => User)
export class UserResolver {
  /***
   *      __  __               __  __            __  _          __         __  ____
   *     / / / /__  ___ ___ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *    / /_/ / _ \/ _ `/ // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    \____/_//_/\_,_/\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                             /_/
   */

  /**
   * Create a new user (as an unauthorized User) via registration
   */
  @Mutation(() => User, { description: '(Unauthenicated) Submit an unverified user' })
  public async submitUser(
    @Arg('input') input: SubmitUserInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<User> {
    const userRelationPaths = fieldsToRelations(info);
    return UserController.submitUser(ctx.em, input, userRelationPaths);
  }
}
