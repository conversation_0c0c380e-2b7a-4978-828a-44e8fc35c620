import { AppContext } from 'core/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ApplicationMetaInput } from 'core/contracts/input/base/ApplicationMetaInput';
import { ApplicationMetaController } from 'core/controllers/base/ApplicationMetaController';
import { ApplicationMeta } from 'core/entities/ApplicationMeta';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';

@Resolver(() => ApplicationMeta)
export class ApplicationMetaResolver {
  @Authorized(RoleNames.CURATOR)
  @Query(() => ApplicationMeta, { nullable: true, description: 'Get the ApplicationMeta' })
  public async getApplicationMeta(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('id') id: string,
  ): Promise<ApplicationMeta | null> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return ApplicationMetaController.getApplicationMeta(
      ctx.em,
      id,
      ctx.token[PayloadKeys.TENANT_KEY],
      fieldsToRelations(info) as never,
    );
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => ApplicationMeta, { description: 'Create a new ApplicationMeta' })
  public async createApplicationMeta(
    @Arg('input') input: ApplicationMetaInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ApplicationMeta> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return ApplicationMetaController.createApplicationMeta(ctx.em, input, ctx.token[PayloadKeys.TENANT_KEY]);
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => ApplicationMeta, { description: 'Update an existing ApplicationMeta' })
  public async updateApplicationMeta(
    @Arg('input') input: ApplicationMetaInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<ApplicationMeta> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return ApplicationMetaController.updateApplicationMeta(
      ctx.em,
      id,
      input,
      ctx.token[PayloadKeys.TENANT_KEY],
      fieldsToRelations(info) as never,
    );
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing ApplicationMeta' })
  public async deleteApplicationMeta(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return ApplicationMetaController.deleteApplicationMeta(ctx.em, id, ctx.token[PayloadKeys.TENANT_KEY]);
  }
}
