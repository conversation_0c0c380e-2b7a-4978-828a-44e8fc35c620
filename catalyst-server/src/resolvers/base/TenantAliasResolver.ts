import { AppContext } from 'core/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CreateTenantAliasInput, UpdateTenantAliasInput } from 'core/contracts/input/base/TenantAliasInput';
import { TenantAliasPage } from 'core/contracts/output/Page';
import { TenantAlias } from 'core/entities/TenantAlias';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { TenantAliasController } from 'core/controllers/base/TenantAliasController';
import { PayloadKeys } from 'core/auth/JwtPayload';

@Resolver(() => TenantAlias)
export class TenantAliasResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized(RoleNames.ADMIN)
  @Query(() => TenantAliasPage, { description: 'Get a page of matching TenantAliases' })
  public async queryTenantAliases(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
  ): Promise<TenantAliasPage> {
    const relationPaths = fieldsToRelations(info, { root: 'results' }) as never;
    return TenantAliasController.queryTenantAliases(
      ctx.em,
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      ctx.token![PayloadKeys.TENANT_KEY],
      pagingInput,
      searchSortInput,
      relationPaths,
    );
  }

  @Authorized(RoleNames.ADMIN)
  @Query(() => TenantAlias, { nullable: true, description: 'Get the TenantAlias' })
  public async getTenantAlias(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('id', { nullable: true }) id?: string,
    @Arg('handle', { nullable: true }) handle?: string,
  ): Promise<TenantAlias | null> {
    const relationPaths = fieldsToRelations(info) as never;
    return TenantAliasController.getTenantAlias({
      em: ctx.em,
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      tenantId: ctx.token![PayloadKeys.TENANT_KEY],
      id,
      handle,
      relationPaths,
    });
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => TenantAlias, { description: 'Create a new TenantAlias' })
  public async createTenantAlias(
    @Arg('input') input: CreateTenantAliasInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<TenantAlias> {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return TenantAliasController.createTenantAlias(ctx.em, input, ctx.token![PayloadKeys.TENANT_KEY]);
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => TenantAlias, { description: 'Update an existing TenantAlias' })
  public async updateTenantAlias(
    @Arg('input') input: UpdateTenantAliasInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<TenantAlias> {
    const relationPaths = fieldsToRelations(info) as never;
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return TenantAliasController.updateTenantAlias(
      ctx.em,
      id,
      input,
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      ctx.token![PayloadKeys.TENANT_KEY],
      relationPaths,
    );
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => Boolean, { description: 'Delete an existing TenantAlias' })
  public async deleteTenantAlias(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return TenantAliasController.deleteTenantAlias(ctx.em, id, ctx.token![PayloadKeys.TENANT_KEY]);
  }
}
