import { AppContext } from 'core/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { Tenant } from 'core/entities/Tenant';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { TenantController } from 'core/controllers/base/TenantController';
import { CreateTenantInput, UpdateTenantInput } from 'core/contracts/input/base/TenantInput';
import { TenantInfo } from 'core/contracts/output/TenantInfo';
import { TenantPage } from 'core/contracts/output/Page';

@Resolver(() => Tenant)
export class TenantResolver {
  @Query(() => Tenant, { nullable: true, description: 'Get the Tenant' })
  public async getTenant(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('id', { nullable: true }) id?: string,
    @Arg('handleOrAlias', { nullable: true }) handle?: string,
  ): Promise<Tenant | null> {
    const relationPaths = fieldsToRelations(info) as never;
    return id
      ? TenantController.getTenant(ctx.em, id, relationPaths)
      : handle
      ? TenantController.getTenantByHandleOrAlias(ctx.em, handle, relationPaths)
      : null;
  }

  @Query(() => TenantInfo, { nullable: true, description: 'Get the TenantInfo' })
  public async getTenantInfo(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('handleOrAlias', { nullable: true }) handleOrAlias: string,
  ): Promise<TenantInfo | null> {
    const relationPaths = fieldsToRelations(info) as never;
    return TenantController.getTenantInfoByHandleOrAlias(ctx.em, handleOrAlias, relationPaths);
  }

  @Query(() => TenantPage, { description: 'Get a page of matching Tenants' })
  public async queryTenants(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
  ): Promise<TenantPage> {
    const relationPaths = fieldsToRelations(info, { root: 'results' }) as never;
    return TenantController.queryTenants(ctx.em, pagingInput, searchSortInput, relationPaths);
  }

  @Query(() => [TenantInfo], { description: 'Returns matching Tenants and Tenant Aliases' })
  public async findTenantInfoByName(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('name') name: string,
  ): Promise<TenantInfo[]> {
    return TenantController.findTenantInfoByName(ctx.em, name);
  }

  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/uper_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  // TODO - This should be a super admin
  @Authorized(RoleNames.ADMIN)
  @Mutation(() => Tenant, { description: 'Create a new Tenant' })
  public async createTenant(
    @Arg('input') input: CreateTenantInput,
    @Arg('adminPass') adminPass: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Tenant> {
    return (await TenantController.createTenant(ctx.em, input, adminPass)).tenant;
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => Tenant, { description: 'Update an existing Tenant' })
  public async updateTenant(
    @Arg('input') input: UpdateTenantInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Tenant> {
    return TenantController.updateTenant(ctx.em, id, input, fieldsToRelations(info) as never);
  }

  // No delete yet as it will have to be comprehensive

  // TODO - this should be a super admin
  @Authorized(RoleNames.ADMIN)
  @Mutation(() => Boolean, { description: 'Delete an existing Tenant' })
  public async deleteTenant(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    return TenantController.deleteTenant(ctx.em, id);
  }
}
