import { AppContext } from 'core/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import {
  CreatePrivilegeGroupInput,
  UpdatePrivilegeGroupInput,
  UpdatePrivilegeGroupLinks,
} from 'core/contracts/input/base/PrivilegeGroupInput';
import { PrivilegeController } from 'core/controllers/base/PrivilegeController';
import { Privilege } from 'core/entities/Privilege';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';

@Resolver(() => Privilege)
export class PrivilegeResolver {
  @Authorized(RoleNames.ADMIN)
  @Query(() => PrivilegeGroup, { nullable: true, description: 'Get the PrivilegeGroup' })
  public async getPrivilegeGroup(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('id', { nullable: true }) id?: string,
    @Arg('name', { nullable: true }) name?: string,
  ): Promise<PrivilegeGroup | null> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return PrivilegeController.getPrivilegeGroup({
      em: ctx.em,
      id,
      tenantId: ctx.token[PayloadKeys.TENANT_KEY],
      name,
      relationPaths: fieldsToRelations(info) as never,
    });
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => PrivilegeGroup, { description: 'Create a new PrivilegeGroup' })
  public async createPrivilegeGroup(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('input') input: CreatePrivilegeGroupInput,
  ): Promise<PrivilegeGroup> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return PrivilegeController.createPrivilegeGroup(ctx.em, ctx.token[PayloadKeys.TENANT_KEY], input);
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => PrivilegeGroup, { description: 'Update an existing PrivilegeGroup' })
  public async updatePrivilegeGroup(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('input') input: UpdatePrivilegeGroupInput,
    @Arg('id') id: string,
    @Arg('links', { nullable: true }) links?: UpdatePrivilegeGroupLinks,
  ): Promise<PrivilegeGroup> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return PrivilegeController.updatePrivilegeGroup(
      ctx.em,
      ctx.token[PayloadKeys.TENANT_KEY],
      id,
      input,
      fieldsToRelations(info) as never,
      fieldsToRelations(info, { root: 'privileges' }),
      links,
    );
  }

  @Authorized(RoleNames.ADMIN)
  @Mutation(() => Boolean, { description: 'Delete an existing PrivilegeGroup' })
  public async deletePrivilegeGroup(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    if (!ctx.token?.[PayloadKeys.TENANT_KEY]) throw Error(errorKeys.TENANT_NOT_FOUND);
    return PrivilegeController.deletePrivilegeGroup(ctx.em, ctx.token[PayloadKeys.TENANT_KEY], id);
  }
}
