import { AppContext } from 'core/core';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { getToken } from 'core/auth/authUtils';
import { roleMap } from 'core/contracts/enums/RoleNames';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { RegisterUserInput, UpdateCurrentUserInput, UpdateCurrentUserLinks } from 'core/contracts/input/base/UserInput';
import { AuthResponse } from 'core/contracts/output/AuthResponse';
import { TenantController } from 'core/controllers/base/TenantController';
import { ApplicationMeta } from 'core/entities/ApplicationMeta';
import { User } from 'core/entities/User';
import { GraphQLResolveInfo } from 'graphql';
import { caseInsensitiveMatchValue } from 'core/storage/queryUtils';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { UserController } from 'core/controllers/base/UserController';

@Resolver(() => User)
export class UserResolver {
  /***
   *      __  __               __  __            __  _          __         __  ____
   *     / / / /__  ___ ___ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *    / /_/ / _ \/ _ `/ // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    \____/_//_/\_,_/\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                             /_/
   */

  /**
   * Create a new authenticated user, via registration
   */
  @Mutation(() => AuthResponse, { description: '(Unauthenticated) Register a new user with a password' })
  public async register(
    @Arg('input') input: RegisterUserInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<AuthResponse> {
    // find tenant
    const tenant = await TenantController.getTenantByHandleOrAlias(ctx.em, input.tenantHandle);
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    //find verfied user
    const userRelationPaths = fieldsToRelations(info, { root: 'user' }) as never;
    let user: User | null = await ctx.em
      .getRepository(User)
      .findOne(
        { emailAddress: caseInsensitiveMatchValue(input.emailAddress), tenant, status: VerifiedStatus.VERIFIED },
        { populate: userRelationPaths },
      );
    if (user) throw Error(errorKeys.USER_EXISTS_AS_VERIFIED);

    // NOTE if the user exists as unverified, that account is left in place and a new,
    // verified account is created as well
    user = User.newUser(input);
    user.tenant = tenant;
    // @TODO This needs to be set after email validation in the future!
    user.status = VerifiedStatus.VERIFIED;
    await ctx.em.persist(user).flush();
    await ctx.em.populate(user, userRelationPaths);

    // jwt
    const { token, expiresAt } = getToken({
      [PayloadKeys.USER_KEY]: user.id,
      [PayloadKeys.TENANT_KEY]: tenant.id,
      [PayloadKeys.ROLES_KEY]: [],
    });
    return { user, token, expiresAt };
  }

  @Mutation(() => AuthResponse, { description: 'Authenticate a User and receive a token' })
  public async login(
    @Arg('userName') userName: string,
    @Arg('password') password: string,
    @Arg('tenantHandle') tenantHandle: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<AuthResponse> {
    // find tenant
    const userRelationPaths = fieldsToRelations(info, { root: 'user' });
    return UserController.login({ ctx, userName, password, tenantHandle, paths: userRelationPaths });
  }

  @Mutation(() => AuthResponse, { description: 'Authenticate a User and receive a token' })
  public async renew(@Ctx() ctx: AppContext, @Info() info: GraphQLResolveInfo): Promise<AuthResponse> {
    const userRelationPaths = fieldsToRelations(info, { root: 'user' });
    const user = await ctx.em
      .getRepository(User)
      .findOne(
        { id: ctx.token?.[PayloadKeys.USER_KEY], tenant: ctx.token?.[PayloadKeys.TENANT_KEY] },
        { populate: [...userRelationPaths, 'roles'] as never },
      );
    if (!user) throw new Error(errorKeys.UNAUTHORIZED);
    const mappedRoles = user.roles.getItems().map((role) => roleMap[role.name]);
    const { token, expiresAt } = getToken({
      [PayloadKeys.USER_KEY]: user?.id,
      [PayloadKeys.TENANT_KEY]: user.tenant.id,
      [PayloadKeys.ROLES_KEY]: mappedRoles,
    });
    return { user, token, expiresAt };
  }

  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized()
  @Query(() => User, { nullable: true, description: 'Get the current User account' })
  public async getCurrentUser(@Ctx() ctx: AppContext, @Info() info: GraphQLResolveInfo): Promise<User | null> {
    const id = ctx.token?.[PayloadKeys.USER_KEY];
    const relationPaths = fieldsToRelations(info) as never;
    return ctx.em
      .getRepository(User)
      .findOne({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } }, { populate: relationPaths });
  }

  @Authorized()
  @Mutation(() => User, { description: 'Update the current User account' })
  public async updateCurrentUser(
    @Arg('input') input: UpdateCurrentUserInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: UpdateCurrentUserLinks,
  ): Promise<User> {
    const id = ctx.token?.[PayloadKeys.USER_KEY];
    const relationPaths = fieldsToRelations(info) as never;
    const user = await ctx.em
      .getRepository(User)
      .findOneOrFail({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } }, { populate: relationPaths });
    user.modify(input);
    if (links?.appMetaId) {
      const appMeta = await ctx.em.getRepository(ApplicationMeta).findOneOrFail(links.appMetaId);
      user.appMeta = appMeta;
    }
    await ctx.em.persist(user).flush();
    await ctx.em.populate(user, relationPaths);
    return user;
  }

  @Authorized()
  @Mutation(() => Boolean, { description: 'Delete the current User account' })
  public async deleteCurrentUser(@Ctx() ctx: AppContext): Promise<boolean> {
    const id = ctx.token?.[PayloadKeys.USER_KEY];
    const user = await ctx.em
      .getRepository(User)
      .findOneOrFail({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } });
    await ctx.em.remove(user).flush();
    return true;
  }
}
