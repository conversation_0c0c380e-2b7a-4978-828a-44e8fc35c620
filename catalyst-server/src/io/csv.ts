import * as csv from 'fast-csv';

// @TODO convert this interface to match that of excel writer
export class Csv {
  private stream = csv.format();

  constructor(readonly headers: string[]) {
    this.stream = csv.format({ headers: headers, delimiter: '\t' });
    this.stream.on('error', (err) => {
      console.error('Error writing to csv stream:');
      console.error(err);
      this.stream.end();
    });
  }

  open(outStream: NodeJS.WritableStream): void {
    this.stream.pipe(outStream);
  }

  writeItems(items: unknown[][]): void {
    items.forEach((item) => this.stream.write(item));
  }

  close(): void {
    this.stream.end();
  }
}
