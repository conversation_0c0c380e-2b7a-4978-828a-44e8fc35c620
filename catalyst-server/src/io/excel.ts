import { stream } from 'exceljs';

export class Excel {
  private workbook;
  private worksheet;

  constructor(outStream: NodeJS.WritableStream, headers: { header: string; key?: string; width?: number }[]) {
    this.workbook = new stream.xlsx.WorkbookWriter({ stream: outStream as never });
    this.worksheet = this.workbook.addWorksheet('Sheet 1', { properties: { defaultColWidth: 20 } });
    this.worksheet.columns = headers;
  }

  writeItems(items: unknown[][]): void {
    items.forEach((item) => this.worksheet.addRow(item).commit());
  }

  async close(): Promise<void> {
    return this.workbook.commit();
  }
}
