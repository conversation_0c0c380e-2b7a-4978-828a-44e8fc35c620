import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';
import { ProjectStakeholderType } from 'core/contracts/enums/ProjectStakeholderType';
import { ProjectStatus } from 'core/contracts/enums/ProjectStatus';
import { RelatedOpportunityType } from 'core/contracts/enums/RelatedOpportunityType';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { UpdateOperator } from 'core/contracts/enums/UpdateOperator';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { LogicalOperator } from 'core/contracts/enums/LogicalOperator';
import { registerEnumType } from 'type-graphql';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { CalcOperator } from 'core/contracts/enums/CalcOperator';
import { QueryEntity } from 'core/contracts/enums/QueryEntity';
import { AgregateFn } from 'core/contracts/enums/AgregateFn';
import { OpportunityVisibility } from 'core/contracts/enums/OpportunityVisibility';
import { OpportunityOwnerStatus } from 'core/contracts/enums/OpportunityOwnerStatus';

registerEnumType(OpportunityVisibility, {
  name: 'OpportunityVisibility',
  description: 'Sets the visibility status of an opportunity',
});
registerEnumType(OpportunityStatus, {
  name: 'OpportunityStatus',
  description: 'Status of the Opportunity',
});
registerEnumType(VerifiedStatus, {
  name: 'VerifiedStatus',
  description: "User or opportunity's verification status",
});
registerEnumType(RoleNames, {
  name: 'RoleNames',
  description: 'User role',
});
registerEnumType(SearchOperator, {
  name: 'SearchOperator',
  description: 'The search operation to apply to a field value',
});
registerEnumType(LogicalOperator, {
  name: 'LogicalOperator',
  description: 'The logical operation to apply to field values',
});
registerEnumType(UpdateOperator, {
  name: 'UpdateOperator',
  description: 'The update operation to apply to a field value',
});
registerEnumType(CurationEventType, {
  name: 'CurationEventType',
  description: 'The type of curation event',
});
registerEnumType(EntityType, {
  name: 'EntityType',
  description: 'The type of entity targeted',
});
registerEnumType(ProjectStakeholderType, {
  name: 'ProjectStakeholderType',
  description: 'The type of project stakeholder',
});
registerEnumType(ProjectStatus, {
  name: 'ProjectStatus',
  description: 'The status of the project',
});
registerEnumType(RelatedOpportunityType, {
  name: 'RelatedOpportunityType',
  description: 'The type of related opportunity',
});
registerEnumType(ResourceType, {
  name: 'ResourceType',
  description: 'The type of resource targeted',
});
registerEnumType(CalcOperator, {
  name: 'CalcOperator',
  description: 'The type of query calculation',
});
registerEnumType(QueryEntity, {
  name: 'QueryEntity',
  description: 'The type of entity to query',
});
registerEnumType(AgregateFn, {
  name: 'AgregateFn',
  description: 'The type of operation to apply',
});
registerEnumType(OpportunityOwnerStatus, {
  name: 'OpportunityOwnerStatus',
  description: 'The status of the opportunity owner',
});
