/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { GraphQLError } from 'graphql';

export interface ErrorResult {
  readonly errors: {}[];
  readonly statusCode: number;
}

export interface ResponseError {
  locations?: readonly {}[];
  path?: readonly {}[];
  positions?: readonly {}[];
  message?: string;
  code?: number;
  extensions?: Error | { validationErrors?: {}[] } | null;
}

export class ErrorHandler {
  static handleError(error: Error): ErrorResult {
    if (!(error instanceof GraphQLError)) {
      return { errors: [error], statusCode: 500 };
    }

    const responseError: ResponseError = { locations: error.locations, path: error.path, positions: error.positions };

    const key = error.originalError?.message;
    const errorInfo = key ? errorCodes[key] : undefined;

    // if no error code found, throw general error
    if (!errorInfo) {
      responseError.message = error.message || errorCodes[errorKeys.GRAPHQL_VALIDATION_ERROR].message;
      responseError.code = errorCodes[errorKeys.GRAPHQL_VALIDATION_ERROR].code;
      responseError.extensions = error.originalError;
      return { errors: [responseError], statusCode: 400 };
    }

    // build the error
    responseError.message = errorInfo.message;
    responseError.code = errorInfo.code;
    const validationErrors = (error.originalError as any)?.validationErrors;
    if (validationErrors?.length) {
      responseError.extensions = { validationErrors };
    }

    return { errors: [responseError], statusCode: 400 };
  }
}
