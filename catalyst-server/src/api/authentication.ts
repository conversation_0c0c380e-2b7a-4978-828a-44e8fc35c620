import { auth<PERSON><PERSON><PERSON>, checkRoles } from "core/auth/authUtils";
import { errorKeys } from "core/contracts/errors/ErrorCodes";
import * as express from "express";

export async function expressAuthentication(
  request: express.Request,
  securityName: string,
  scopes?: string[]
): Promise<void> {
  if (securityName === "jwt") {
    const token = (request as any).context.token;
    if (!checkRoles(token, scopes)) {
        throw new Error(errorKeys.UNAUTHORIZED);
    }
  }
}