import { PagingInput, Scope, SearchSortInput } from '../../core/contracts/input/base/CommonInput';
/**
  Defines the query input for the search operation.  

  Example: 

  Find by substring matching "test" in 'title' or 'statement' and created since a specific date

  ```
  {
    "pagingInput": {
        "pagesize": 20,
        "cursor": "0"
    },
    "searchSortInput": {
        "searchFields": [
            {
                "fieldNames": [ "title", "statement" ],
                "operator": "~",
                "searchvalue": "test"
            },
            {
                "fieldNames": [ "createdAt" ],
                "operator": ">=",
                "searchValue": "2021-10-10T00:00:00.000Z"
            }
        ]
     }
  }
  ```  

  Example: 
  
  Filter by Approved and Archived statuses - rows 100-199

  ```
  {
      "pagingInput": {
          "pageSize": 100,
          "cursor": "100"
      },
      "searchSortInput": {
          "searchSortInput": {
              "searchFields": [
                  {
                      "fieldNames": [ "status" ],
                      "operator": "in",
                      "searchValue": [ "Approved", "Archived" ]
                  }
              ]
          }
      }
  }
  ```

  Example:

 Advanced AND/OR search using 'JsonSearchGroups'  
 Filter by "Approved" or "Archived" statuses and category name substring matching "category_1" or "category_2"

  ```
  {
      "pagingInput": {
          "pageSize": 100,
          "cursor": "100"
      },
      "searchSortInput": {
          "jsonSearchGroups": [
              { "operator": "and",
                  "operands": [
                      {
                          "operator": "or",
                          "operands": [
                              {
                                  "fieldNames": [ "status" ],
                                  "operator": "=",
                                  "searchValue": "Approved"
                              },
                              {
                                  "fieldNames": [ "status" ],
                                  "operator": "=",
                                  "searchValue": "Archived"
                              }
                          ]
                      },
                      {
                          "operator": "or",
                          "operands": [
                              {
                                  "fieldNames": [ "categories.name" ],
                                  "operator": "~",
                                  "searchValue": "category_1"
                              },
                              {
                                  "fieldNames": [ "categories.name" ],
                                  "operator": "~",
                                  "searchValue": "category_2"
                              }
                          ]
                      }
                  ]
              }
          ]
      }
  }
  ```


 */
export interface QueryInput {
  /** 
  Specifies the paging params for the query operation
  */
  pagingInput?: PagingInput;
  /** 
  # Specifies the search and sort input for the query operation  
  */
  searchSortInput?: SearchSortInput;
  /** Allows for requesting access to additional resources (i.e. other Tenants' data) */
  scope?: Scope;
}
