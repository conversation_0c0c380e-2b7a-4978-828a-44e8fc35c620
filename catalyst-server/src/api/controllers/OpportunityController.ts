import { QueryInput } from 'api/input/QueryInput';
import { Page } from 'api/output/Page';
import { instanceToPlain } from 'class-transformer';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { OpportunityController as CoreController } from 'core/controllers/curator/OpportunityController';
import { AppContext } from 'core/core';
import { Opportunity } from 'core/types/Opportunity';
import { Body, Controller, Get, Path, Post, Request, Route, Security, Tags } from 'tsoa';

@Route('opportunities')
@Tags('Opportunities')
export class OpportunityController extends Controller {
  /**
   * Get a single opportunity by id
   * @param id the opportunity id
   * @returns An opportunity object if found
   */
  @Get('{opportunityId}')
  @Security('bearerAuth', [RoleNames.CURATOR])
  public async getOpportunity(
    @Path('opportunityId') id: string,
    @Request() request: Express.Request,
  ): Promise<Opportunity> {
    const opportunity = await CoreController.getOpportunity({
      id,
      ctx: (request as any).context as AppContext,
      relationPaths: [
        'categories',
        'stakeholders',
        'user',
        'submissions',
        'tenant',
        'attachments',
        'ownedOpportunities',
        'owningOpportunities',
      ],
    });
    if (!opportunity) throw new Error(errorKeys.OBJECT_NOT_FOUND);
    const model = instanceToPlain(opportunity) as Opportunity;
    return model;
  }

  /**
    * Query for a Page (set) of Opportunities with the supplied filter

    Example:

    Get a page of 100 opportunities starting at index 0 and omit results with a 'Deleted' status
    The 'token' returned from the auth call must be present in the Authorization header.  The value is prefixed with "Bearer "

    ```
    curl -X POST https://api.soldierinnovation.com/opportunities \
    -H 'Content-Type: application/json' \
    -H "Authorization: Bearer eyJhbGciO<token truncated for readability>" \
    -d '
      {
        "pagingInput": { "pageSize": 100, "cursor": "0" },
        "searchSortInput": {
          "searchFields": [
            {"fieldNames": ["status"], "operator": "nin", "searchValue": ["Deleted"]}
          ]
        }
      }
    '
    ```
    Response:
    ```
    {
      "results": [
          {
              "id": "00000000-0000-0000-0000-000000000002",
              "lastCurated": null,
              "title": "Synchronised radical open system",
              <full Opp here ...>
          },
          <next Opp here ...>,
          <next Opp here ...>,
      ],
      "pageInfo": {
          "totalCount": 383,
          "retrievedCount": 99,
          "hasNext": true,
          "hasPrevious": false,
          "lastCursor": "0",
          "lastPageSize": 100
      }
    }
    ```

    Example:  

    Same as above example but queries across multiple Tenants' data


    ```
    curl -X POST https://api.soldierinnovation.com/opportunities \
    -H 'Content-Type: application/json' \
    -H "Authorization: Bearer eyJhbGciOiJI<token truncated for readability>" \
    -d '
      {
        "pagingInput": { "pageSize": 100, "cursor": "0" },
        "searchSortInput": {
          "searchFields": [
            {"fieldNames": ["status"], "operator": "nin", "searchValue": ["Deleted"]}
          ]
        },
        "scope": {
          "resources": [
            {"resourceId": "00000000-0000-0000-0000-000000000001", "resourceType": "TENANT" },
            {"resourceId": "00000000-0000-0000-0000-000000000002", "resourceType": "TENANT" }
          ]
        }
      }
    '
    ```

    Response:   

    ```
    Same as above results but will include both Tenants' data
    ```
    <br>
    ## (See the QueryInput object 'Schema' below for more examples)

    * @param requestBody the query input object
    * @returns the page of matching opportunities
    */
  @Post()
  @Security('bearerAuth', [RoleNames.CURATOR])
  public async getOpportunities(
    @Body() requestBody: QueryInput,
    @Request() request: Express.Request,
  ): Promise<Page<Opportunity>> {
    const opportunityPage = await CoreController.queryOpportunities({
      ctx: (request as any).context as AppContext,
      pagingInput: requestBody.pagingInput,
      searchSortInput: requestBody.searchSortInput,
      relationPaths: [
        'categories',
        'stakeholders',
        'user',
        'submissions',
        'tenant',
        'attachments',
        'ownedOpportunities',
        'owningOpportunities',
      ],
      scope: requestBody.scope,
    });
    const page = {
      results: opportunityPage.results.map((o) => instanceToPlain(o) as Opportunity),
      pageInfo: opportunityPage.pageInfo,
    };
    return page;
  }
}
