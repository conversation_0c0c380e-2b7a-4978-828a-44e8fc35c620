import { RequestInput } from 'api/input/RequestInput';
import { Location } from 'api/output/Location';
import { instanceToPlain } from 'class-transformer';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { Location as _Location } from 'core/contracts/output/Location';
import { AttachmentController as CoreController } from 'core/controllers/curator/AttachmentController';
import { AppContext } from 'core/core';
import { Body, Controller, Get, Path, Post, Request, Route, Security, Tags } from 'tsoa';

@Route('attachments')
@Tags('Attachments')
export class AttachmentController extends Controller {
  /**
   * Get the location (uri) of an attachment. These URLs are temporary and will expire after a short period of time
   * @param id the unique identifier of the attachment
   * @returns the an object with the location (uri) of the attachment
   */
  @Get('{attachmentId}')
  @Security('bearerAuth', [RoleNames.CURATOR])
  public async getAttachmentLocation(
    @Path('attachmentId') id: string,
    @Request() request: Express.Request,
  ): Promise<Location | null> {
    const location = CoreController.getAttachmentLocation((request as any).context as AppContext, id);
    if (!location) throw new Error('Not Found');
    const model = instanceToPlain(location) as Location
    return model;
  }
}
