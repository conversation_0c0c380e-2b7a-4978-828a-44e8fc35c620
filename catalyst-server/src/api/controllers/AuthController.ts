import { Controller, Get, Path, Post, Request, Route, Body, Tags } from 'tsoa';
import { UserController as CoreController } from 'core/controllers/base/UserController';
import { User as _User } from 'core/entities/User';
import { AppContext } from 'core/core';
import { instanceToPlain } from 'class-transformer';
import { AuthInput } from 'api/input/AuthInput';
import { AuthResponse as _AuthResponse } from 'core/contracts/output/AuthResponse';
import { AuthResponse } from 'api/output/AuthResponse';

@Route('login')
@Tags('Authentication')
export class UserController extends Controller {
  /**
    Authenticate a user and return a token for subsequent requests and list of allowed tenants (as privileges).
    These privileges may be returned in the 'scope' parameter of [QueryInput](#/Opportunities/GetOpportunities) to filter by allowed Tenants.   
       
    Example:  


      ```
      curl -X POST https://api.soldierinnovation.com/login -H 'Content-Type: application/json' -d '
      { "userName": "<EMAIL>", "password": "a.password", "tenantHandle": "monumentsMen" }
      '
      ```
     Response:   
      ```
      {
        "user": {
            "id": "00000000-0000-0000-0000-000000000005",
            "emailAddress": "<EMAIL>",
            "firstName": "Abigale",
            "lastName": "Deckow",
            "org1": "Technical Operations",
            <additional fields ...>
            "privilegeGroups": [
                {
                    "id": "00000000-0000-0000-0000-000000000001",
                    "name": "All Tenants Group",
                    "privileges": [
                        {
                            "id": "f65621c3-038b-4d91-8669-380d86a052b5",
                            "name": "Starship Troopers",
                            "resourceId": "00000000-0000-0000-0000-000000000002",
                            "resourceType": "TENANT"
                        },
                        {
                            "id": "bf689b68-a594-4006-b779-e16dc6da6fe5",
                            "name": "The Monuments Men",
                            "resourceId": "00000000-0000-0000-0000-000000000001",
                            "resourceType": "TENANT"
                        }
                    ]
                }
            ]
        },
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfMSI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwNSIsIl8yIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAxIiwiXzMiOlsiMSIsIjMiXSwiaWF0IjoxNzEyODYyNjQ4LCJleHAiOjE3MTI4Njk4NDh9.oVwNRruTk9-QOZx2rbJEKCjBMeaudOofMaOYHnU6vsk",
        "expiresAt": "2024-04-11T21:10:48.702Z"
      }
      ```
    * @param requestBody the user's credentials
    * @returns a response object containing credentials for subsequent requests
    */
  @Post()
  public async login(@Body() requestBody: AuthInput, @Request() request: Express.Request): Promise<AuthResponse> {
    const response = await CoreController.login({
      ctx: (request as any).context as AppContext,
      userName: requestBody.userName,
      password: requestBody.password,
      tenantHandle: requestBody.tenantHandle,
      paths: ['privilegeGroups', 'privilegeGroups.privileges'],
    });
    return instanceToPlain(response, { groups: ['user_full'] }) as AuthResponse;
  }
}
