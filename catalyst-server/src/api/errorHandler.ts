/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { GraphQLError } from 'graphql';
import { ValidateError  } from 'tsoa';

export interface ErrorResult {
  readonly errors: {}[];
  readonly statusCode: number;
}

export class ErrorHandler {
  static handleError(error: Error): ErrorResult {
    return { errors: [error], statusCode: 500 };
  }
}
