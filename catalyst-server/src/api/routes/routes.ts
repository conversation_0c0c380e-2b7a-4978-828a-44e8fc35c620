/* tslint:disable */
/* eslint-disable */
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import type { TsoaRoute } from '@tsoa/runtime';
import {  fetchMiddlewares, ExpressTemplateService } from '@tsoa/runtime';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { UserController } from './../controllers/AuthController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { OpportunityController } from './../controllers/OpportunityController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { AttachmentController } from './../controllers/AttachmentController';
import { expressAuthentication } from './../authentication';
// @ts-ignore - no great way to install types from subpackage
import type { Request as ExRequest, Response as ExResponse, RequestHandler, Router } from 'express';

const expressAuthenticationRecasted = expressAuthentication as (req: ExRequest, securityName: string, scopes?: string[], res?: ExResponse) => Promise<any>;


// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

const models: TsoaRoute.Models = {
    "Privilege": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"any","required":true},
            "name": {"dataType":"any","required":true},
            "resourceId": {"dataType":"any","required":true},
            "resourceType": {"dataType":"any","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "PrivilegeGroup": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "name": {"dataType":"string","required":true},
            "privileges": {"dataType":"array","array":{"dataType":"refObject","ref":"Privilege"},"required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "User": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "emailAddress": {"dataType":"string","required":true},
            "firstName": {"dataType":"string","required":true},
            "lastName": {"dataType":"string","required":true},
            "org1": {"dataType":"string"},
            "org2": {"dataType":"string"},
            "org3": {"dataType":"string"},
            "org4": {"dataType":"string"},
            "phone": {"dataType":"string"},
            "altContact": {"dataType":"string"},
            "privilegeGroups": {"dataType":"array","array":{"dataType":"refObject","ref":"PrivilegeGroup"},"required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "AuthResponse": {
        "dataType": "refObject",
        "properties": {
            "user": {"ref":"User"},
            "token": {"dataType":"string","required":true},
            "expiresAt": {"dataType":"datetime","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "AuthInput": {
        "dataType": "refObject",
        "properties": {
            "userName": {"dataType":"string","required":true},
            "password": {"dataType":"string","required":true},
            "tenantHandle": {"dataType":"string","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Tenant": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "name": {"dataType":"string","required":true},
            "handle": {"dataType":"string","required":true},
            "label": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Category": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "name": {"dataType":"string","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Stakeholder": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "name": {"dataType":"string"},
            "org": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Attachment": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "name": {"dataType":"string","required":true},
            "encoding": {"dataType":"string"},
            "mimetype": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Submission": {
        "dataType": "refObject",
        "properties": {
            "function": {"dataType":"string"},
            "id": {"dataType":"string","required":true},
            "createdAt": {"dataType":"datetime","default":"2025-06-11T02:21:37.343Z","required":true},
            "updatedAt": {"dataType":"datetime"},
            "title": {"dataType":"string","required":true},
            "statement": {"dataType":"string","required":true},
            "context": {"dataType":"string","required":true},
            "benefits": {"dataType":"string"},
            "solutionConcepts": {"dataType":"string"},
            "campaign": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "OpportunityStatus": {
        "dataType": "refEnum",
        "enums": ["Pending","Approved","Archived","Deleted"],
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Opportunity": {
        "dataType": "refObject",
        "properties": {
            "function": {"dataType":"string"},
            "id": {"dataType":"string","required":true},
            "org1": {"dataType":"string"},
            "org2": {"dataType":"string"},
            "org3": {"dataType":"string"},
            "org4": {"dataType":"string"},
            "createdAt": {"dataType":"datetime","default":"2025-06-11T02:21:37.345Z","required":true},
            "updatedAt": {"dataType":"datetime"},
            "lastCurated": {"dataType":"datetime"},
            "title": {"dataType":"string","required":true},
            "statement": {"dataType":"string","required":true},
            "status": {"ref":"OpportunityStatus","default":"Pending","required":true},
            "context": {"dataType":"string","required":true},
            "benefits": {"dataType":"string"},
            "solutionConcepts": {"dataType":"string"},
            "additionalNotes": {"dataType":"string"},
            "campaign": {"dataType":"string"},
            "campaignNotes": {"dataType":"string"},
            "statusNotes": {"dataType":"string"},
            "priority": {"dataType":"double","default":0},
            "priorityNotes": {"dataType":"string"},
            "solutionPathway": {"dataType":"string"},
            "solutionPathwayDetails": {"dataType":"string"},
            "attachmentNotes": {"dataType":"string"},
            "initiatives": {"dataType":"string"},
            "endorsements": {"dataType":"string"},
            "tenant": {"ref":"Tenant","required":true},
            "user": {"ref":"User","required":true},
            "categories": {"dataType":"array","array":{"dataType":"refObject","ref":"Category"},"required":true},
            "stakeholders": {"dataType":"array","array":{"dataType":"refObject","ref":"Stakeholder"},"required":true},
            "linkedOpportunityIds": {"dataType":"array","array":{"dataType":"string"},"required":true},
            "parentOpportunitityIds": {"dataType":"array","array":{"dataType":"string"},"required":true},
            "childOpportunityIds": {"dataType":"array","array":{"dataType":"string"},"required":true},
            "attachments": {"dataType":"array","array":{"dataType":"refObject","ref":"Attachment"},"required":true},
            "submissions": {"dataType":"array","array":{"dataType":"refObject","ref":"Submission"},"required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "PageInfo": {
        "dataType": "refObject",
        "properties": {
            "hasNext": {"dataType":"boolean","default":false},
            "hasPrevious": {"dataType":"boolean","default":false},
            "lastCursor": {"dataType":"string"},
            "lastPageSize": {"dataType":"double","default":0},
            "retrievedCount": {"dataType":"double","default":0},
            "totalCount": {"dataType":"double","default":0},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Page_Opportunity_": {
        "dataType": "refObject",
        "properties": {
            "results": {"dataType":"array","array":{"dataType":"refObject","ref":"Opportunity"},"required":true},
            "pageInfo": {"ref":"PageInfo","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "PagingInput": {
        "dataType": "refObject",
        "properties": {
            "pageSize": {"dataType":"double"},
            "cursor": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "SearchOperator": {
        "dataType": "refEnum",
        "enums": ["=","!=","~",">","<",">=","<=","in","nin"],
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "SearchField": {
        "dataType": "refObject",
        "properties": {
            "fieldNames": {"dataType":"array","array":{"dataType":"string"},"required":true},
            "operator": {"ref":"SearchOperator"},
            "searchValue": {"dataType":"union","subSchemas":[{"dataType":"string"},{"dataType":"double"},{"dataType":"boolean"},{"dataType":"datetime"},{"dataType":"array","array":{"dataType":"any"}},{"dataType":"enum","enums":[null]}]},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "JsonSearchGroup": {
        "dataType": "refObject",
        "properties": {
            "operands": {"dataType":"array","array":{"dataType":"union","subSchemas":[{"ref":"JsonSearchGroup"},{"ref":"SearchField"}]}},
            "operator": {"ref":"LogicalOperator"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "LogicalOperator": {
        "dataType": "refEnum",
        "enums": ["or","and"],
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "SortField": {
        "dataType": "refObject",
        "properties": {
            "fieldName": {"dataType":"string","required":true},
            "ascending": {"dataType":"boolean"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "SearchSortInput": {
        "dataType": "refObject",
        "properties": {
            "searchFields": {"dataType":"array","array":{"dataType":"refObject","ref":"SearchField"}},
            "jsonSearchGroups": {"dataType":"array","array":{"dataType":"refObject","ref":"JsonSearchGroup"}},
            "sortFields": {"dataType":"array","array":{"dataType":"refObject","ref":"SortField"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "ResourceType": {
        "dataType": "refEnum",
        "enums": ["TENANT"],
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Resource": {
        "dataType": "refObject",
        "properties": {
            "resourceId": {"dataType":"string","required":true},
            "resourceType": {"ref":"ResourceType","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Scope": {
        "dataType": "refObject",
        "properties": {
            "resources": {"dataType":"array","array":{"dataType":"refObject","ref":"Resource"},"required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "QueryInput": {
        "dataType": "refObject",
        "properties": {
            "pagingInput": {"ref":"PagingInput"},
            "searchSortInput": {"ref":"SearchSortInput"},
            "scope": {"ref":"Scope"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Location": {
        "dataType": "refObject",
        "properties": {
            "location": {"dataType":"string","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
};
const templateService = new ExpressTemplateService(models, {"noImplicitAdditionalProperties":"throw-on-extras","bodyCoercion":true});

// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa




export function RegisterRoutes(app: Router) {

    // ###########################################################################################################
    //  NOTE: If you do not see routes for all of your controllers in this file, then you might not have informed tsoa of where to look
    //      Please look into the "controllerPathGlobs" config option described in the readme: https://github.com/lukeautry/tsoa
    // ###########################################################################################################


    
        const argsUserController_login: Record<string, TsoaRoute.ParameterSchema> = {
                requestBody: {"in":"body","name":"requestBody","required":true,"ref":"AuthInput"},
                request: {"in":"request","name":"request","required":true,"dataType":"object"},
        };
        app.post('/login',
            ...(fetchMiddlewares<RequestHandler>(UserController)),
            ...(fetchMiddlewares<RequestHandler>(UserController.prototype.login)),

            async function UserController_login(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsUserController_login, request, response });

                const controller = new UserController();

              await templateService.apiHandler({
                methodName: 'login',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsOpportunityController_getOpportunity: Record<string, TsoaRoute.ParameterSchema> = {
                id: {"in":"path","name":"opportunityId","required":true,"dataType":"string"},
                request: {"in":"request","name":"request","required":true,"dataType":"object"},
        };
        app.get('/opportunities/:opportunityId',
            authenticateMiddleware([{"bearerAuth":["curator"]}]),
            ...(fetchMiddlewares<RequestHandler>(OpportunityController)),
            ...(fetchMiddlewares<RequestHandler>(OpportunityController.prototype.getOpportunity)),

            async function OpportunityController_getOpportunity(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsOpportunityController_getOpportunity, request, response });

                const controller = new OpportunityController();

              await templateService.apiHandler({
                methodName: 'getOpportunity',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsOpportunityController_getOpportunities: Record<string, TsoaRoute.ParameterSchema> = {
                requestBody: {"in":"body","name":"requestBody","required":true,"ref":"QueryInput"},
                request: {"in":"request","name":"request","required":true,"dataType":"object"},
        };
        app.post('/opportunities',
            authenticateMiddleware([{"bearerAuth":["curator"]}]),
            ...(fetchMiddlewares<RequestHandler>(OpportunityController)),
            ...(fetchMiddlewares<RequestHandler>(OpportunityController.prototype.getOpportunities)),

            async function OpportunityController_getOpportunities(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsOpportunityController_getOpportunities, request, response });

                const controller = new OpportunityController();

              await templateService.apiHandler({
                methodName: 'getOpportunities',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsAttachmentController_getAttachmentLocation: Record<string, TsoaRoute.ParameterSchema> = {
                id: {"in":"path","name":"attachmentId","required":true,"dataType":"string"},
                request: {"in":"request","name":"request","required":true,"dataType":"object"},
        };
        app.get('/attachments/:attachmentId',
            authenticateMiddleware([{"bearerAuth":["curator"]}]),
            ...(fetchMiddlewares<RequestHandler>(AttachmentController)),
            ...(fetchMiddlewares<RequestHandler>(AttachmentController.prototype.getAttachmentLocation)),

            async function AttachmentController_getAttachmentLocation(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsAttachmentController_getAttachmentLocation, request, response });

                const controller = new AttachmentController();

              await templateService.apiHandler({
                methodName: 'getAttachmentLocation',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa


    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

    function authenticateMiddleware(security: TsoaRoute.Security[] = []) {
        return async function runAuthenticationMiddleware(request: any, response: any, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            // keep track of failed auth attempts so we can hand back the most
            // recent one.  This behavior was previously existing so preserving it
            // here
            const failedAttempts: any[] = [];
            const pushAndRethrow = (error: any) => {
                failedAttempts.push(error);
                throw error;
            };

            const secMethodOrPromises: Promise<any>[] = [];
            for (const secMethod of security) {
                if (Object.keys(secMethod).length > 1) {
                    const secMethodAndPromises: Promise<any>[] = [];

                    for (const name in secMethod) {
                        secMethodAndPromises.push(
                            expressAuthenticationRecasted(request, name, secMethod[name], response)
                                .catch(pushAndRethrow)
                        );
                    }

                    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

                    secMethodOrPromises.push(Promise.all(secMethodAndPromises)
                        .then(users => { return users[0]; }));
                } else {
                    for (const name in secMethod) {
                        secMethodOrPromises.push(
                            expressAuthenticationRecasted(request, name, secMethod[name], response)
                                .catch(pushAndRethrow)
                        );
                    }
                }
            }

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            try {
                request['user'] = await Promise.any(secMethodOrPromises);

                // Response was sent in middleware, abort
                if (response.writableEnded) {
                    return;
                }

                next();
            }
            catch(err) {
                // Show most recent error as response
                const error = failedAttempts.pop();
                error.status = error.status || 401;

                // Response was sent in middleware, abort
                if (response.writableEnded) {
                    return;
                }
                next(error);
            }

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        }
    }

    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
}

// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
