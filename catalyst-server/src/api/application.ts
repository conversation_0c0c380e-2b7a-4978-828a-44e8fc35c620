// src/app.ts
import express, { json, urlencoded, Response, Request } from 'express';
import cors from 'cors';
import { Server } from 'http';
import { RegisterRoutes } from 'api/routes/routes';
import { JwtPayload, Secret } from 'jsonwebtoken';
import { AppContext } from 'core/core';
import { Connection, CreateRequestContext, DriverException, IDatabaseDriver, MikroORM, TableNotFoundException } from '@mikro-orm/core';
import ormConfig from 'mikro-orm.config';
import { TenantInfo } from 'core/contracts/output/TenantInfo';
import version from 'version.json';
import swaggerUi from 'swagger-ui-express';
import { expressjwt } from 'express-jwt';
import { ErrorHandler } from './errorHandler';
import { Tenant } from 'core/entities/Tenant';

export default class Application {
  public orm?: MikroORM<IDatabaseDriver<Connection>>;
  public app?: express.Application;
  public server?: Server;
  public readonly tenantServerConfigs: Map<string, unknown> = new Map<string, unknown>();

  @CreateRequestContext()
  async getAllTenantServerConfigs(): Promise<Map<string, unknown>> {
    if(!this.orm?.em) return Promise.resolve(this.tenantServerConfigs);
    try {
      const allTenants = await this.orm?.em.getRepository(Tenant).findAll({populate: ['id', 'meta'] });
      allTenants.map(tenant => {
        if(tenant.meta && tenant.meta.serverConfig) {
          this.tenantServerConfigs.set(tenant.id, tenant.meta?.serverConfig);
        }
      });
    }
    catch(error) {
      const { code, name } = error as DriverException;
      if(code === '42P01') {
        // We are swallowing this error as it should only occur if we haven't bootstrapped the db or set the schema
        const tableNotFoundExc = error as TableNotFoundException;
        console.log('Tenant table has not been loaded. Application context will not set serverConfig data.', tableNotFoundExc);
        return Promise.resolve(this.tenantServerConfigs);
      }
      throw `Error loading Tenant entity.\r\n${error}`;
    }
    return Promise.resolve(this.tenantServerConfigs);
  }
  
  public init = async (): Promise<void> => {
    try {
      this.orm = await MikroORM.init(ormConfig);
      await this.getAllTenantServerConfigs();
    } catch (error) {
      console.error('📌 Could not connect to the database', error);
      throw Error('Server Error');
    }
    TenantInfo.serverVersion = version?.serverVersion;
    this.registerShutdown();
  };

  public start = async (): Promise<void> => {
    this.app = express();
    this.app.use(
      urlencoded({
        extended: true,
      }),
    );

    this.app.use(cors());
    this.app.use(json());
    // Jwt Setup
    this.app.use(
      expressjwt({
        secret: process.env.JWT_SECRET as Secret,
        algorithms: ['HS256'],
        credentialsRequired: false,
      }),
    );

    this.app.use((req, res, next) => {
      (req as any).context = {
        req,
        res,
        em: this.orm?.em.fork(),
        token: (<any>req)?.auth as JwtPayload,
        serverConfigs: this.tenantServerConfigs,
        //token: { _1: '00000000-0000-0000-0000-000000000001', _2: '00000000-0000-0000-0000-000000000001', _3: ['1', '3'], },
      } as AppContext;
      next();
    });

    // user
    this.app.get(
      ['/swagger.json'],
      (req: Request, res: Response) => {
        res.sendFile(__dirname + '/swagger.json');
      },
    );

    try {
      RegisterRoutes(this.app);

      /***
       *       ____                       _____          ____
       *      / __/__ _____  _____ ____  / ___/__  ___  / _(_)__ _
       *     _\ \/ -_) __/ |/ / -_) __/ / /__/ _ \/ _ \/ _/ / _ `/
       *    /___/\__/_/  |___/\__/_/    \___/\___/_//_/_//_/\_, /
       *                                                   /___/
       */

      this.app.use('/', swaggerUi.serve, async (_req: express.Request, res: express.Response) => {
        return res.send(swaggerUi.generateHTML(await import('api/swagger.json')));
      });

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction): void => {
        console.error('📌 Something went wrong', error);
        const errorResult = ErrorHandler.handleError(error);
        res.status(errorResult.statusCode).send({ errors: errorResult.errors });
        res.status(400).send({ errors: [error] });
      });

      const port = process.env.PORT || 4000;
      this.server = this.app.listen(port as number, '0.0.0.0', () => {
        console.log(`🚀 API Server version ${TenantInfo.serverVersion} started at http://localhost:${port}`);
        console.log(`🚀 API docs at http://localhost:${port}`);
      });
    } catch (error) {
      console.error('📌 Could not start server', error);
    }
  };

  private registerShutdown() {
    /***
     *       ____                       _______
     *      / __/__ _____  _____ ____  / ___/ /__ ___ ____  __ _____
     *     _\ \/ -_) __/ |/ / -_) __/ / /__/ / -_) _ `/ _ \/ // / _ \
     *    /___/\__/_/  |___/\__/_/    \___/_/\__/\_,_/_//_/\_,_/ .__/
     *                                                        /_/
     */
    const server = this.server;
    const orm = this.orm;

    // quit on ctrl-c when running docker in terminal
    process.on('SIGINT', function onSigint() {
      console.info('Got SIGINT (aka ctrl-c ). Waiting for shutdown...', new Date().toISOString());
      shutdown();
    });

    // quit properly on docker stop
    process.on('SIGTERM', function onSigterm() {
      console.info('Got SIGTERM (docker container stop). Graceful shutdown ', new Date().toISOString());
      shutdown();
    });

    // shut down server
    function shutdown() {
      disconnectAll().then((results) => {
        if (results.some((result) => result.status === 'rejected')) {
          results.forEach((result) => {
            if (result.status === 'rejected') console.error(`Shutdown error:`, result.reason);
          });
          process.exit(1);
        }
        console.info('Shutdown completed successfully.', new Date().toISOString());
        process.exit(0);
      });
    }

    async function disconnectAll(): Promise<[PromiseSettledResult<void>, PromiseSettledResult<void>]> {
      return Promise.allSettled([shutdownServer(), orm ? orm.close() : Promise.resolve()]);
    }

    async function shutdownServer() {
      if (!server) return Promise.resolve();
      return new Promise<void>((resolve, reject) => {
        server?.close((err) => {
          err ? reject(err) : resolve();
        });
      });
    }
  }
}
