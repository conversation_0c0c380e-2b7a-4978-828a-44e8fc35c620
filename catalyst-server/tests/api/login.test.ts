import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'api/application';
import { expect } from 'chai';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe(')))))))))))))) API Opportunity Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    restoreDbFromLocalBackup();
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should login the user', async () => {
    const response = await request
      .post('/login')
      .send({
        userName: '<EMAIL>',
        password: 'passcurator',
        tenantHandle: 'monumentsMen',
      })
      .expect(200);
    expect(response.body).to.be.a('object');
    expect(response.body.user.id).to.equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.token).to.not.be.null;
  });
  it('should fail to login the user', async () => {
    const response = await request
      .post('/login')
      .send({
        userName: '<EMAIL>',
        password: 'psscurator',
        tenantHandle: 'monumentsMen',
      })
      .expect(500);
  });
});
