import { getToken } from 'core/auth/authUtils';
import { ID_PREFIX_0 } from 'loadFixtures';
import { Id } from 'core/utils/Id';
import axios from 'axios';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

// this is the prefix for users for monumentsMen (see loadFixtures.ts)
const curatorToken = getToken({
  [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
  [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
  [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
}).token;

const endpoint = 'http://127.0.0.1:8080/curator';
const query = {
  query: `query {
          getOpportunity(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id
            createdAt
            lastCurated
            additionalNotes
            title
            statement
            context
            status
            benefits
            solutionConcepts
            campaign
            campaignNotes
            statusNotes
            priority
            priorityNotes
            solutionPathway
            solutionPathwayDetails
            permission
            attachmentNotes
            initiatives
            endorsements
            submissions {
              id
              title
              statement
              context
              benefits
              solutionConcepts
              campaign
              user {
                id
                altContact
                createdAt
                emailAddress
                firstName
                lastName
                org1
                org2
                org3
                org4
                phone
                status
                updatedAt
              }
            }
            categories {
              id
              name
            }
            stakeholders {
              id
              name
              org
            }
            opportunities {
              id
              title
            }
            ownedOpportunities {
              id
              type
              target {
                id
                title
              }
            }
            owningOpportunities {
              id
              type
              source {
                id
                title
              }
            }
            user {
              id
              altContact
              createdAt
              emailAddress
              firstName
              lastName
              org1
              org2
              org3
              org4
              phone
              status
              updatedAt
            }
            attachments {
              id
              createdAt
              updatedAt
              name
              encoding
              mimetype
            }
            curationInfo {
              users {
                id
                firstName
                lastName
              }
              lastCurated
            }
            projects {
              id
              title
            }
          }
        }
        `,
};

const doQuery = async () => {
  const start = Date.now();
  const response = await axios.post(endpoint, query, {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${curatorToken}`,
    },
  });

  if (response.status !== 200) {
    throw new Error(`GraphQL request failed with status code ${response.status}`);
  }
  const finish = Date.now() - start;
  //console.log(JSON.stringify(response.data));
  //console.log(`Request took ${finish} ms`);
  return finish;
};

const doQueries = async (numRequests: number) => {
  //console.log(`Running ${numRequests} simultaneous requests...`);
  let avgTime = 0;
  const results: Array<Promise<number>> = [];
  for (let i = 0; i < numRequests; i++) {
    results.push(doQuery());
  }
  const times = await Promise.all(results);
  return times.reduce((prev, cur) => prev + cur, 0) / times.length;
};

doQueries(1).then((avg) => {
  console.log(`1 Finshed w/ avg of ${avg} ms`);
  doQueries(10).then((avg) => {
    console.log(`10 Finshed w/ avg of ${avg} ms`);
    doQueries(50).then((avg) => {
      console.log(`50 Finshed w/ avg of ${avg} ms`);
      doQueries(100).then((avg) => {
        console.log(`100 Finshed w/ avg of ${avg} ms`);
        doQueries(500).then((avg) => {
          console.log(`500 Finshed w/ avg of ${avg} ms`);
        });
      });
    });
  });
});
