import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { Role } from 'core/entities/Role';
import { USERS_PER_TENANT, USER_SUBSTRING, ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;
let adminToken: string;
let superToken: string;
let adminTokenTenant2: string;

('{ "query" : "{ getTenant(handleOrAlias: \"monumentsMen\") { id name handle meta { config theme } } }" }');

describe('))))))))))))) Curator User Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    adminToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(4, 0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.ADMIN]],
    }).token;
    superToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(5, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR], roleMap[RoleNames.ANALYST]],
    }).token;
    adminTokenTenant2 = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(4, 0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(2),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.ADMIN]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should login user', async () => {
    const response = await request
      .post('/curator')
      .send({
        query:
          'mutation { login ( userName: "<EMAIL>", password: "passcurator", tenantHandle: "monumentsMen",){ user { id } token } }',
      })
      .expect(200);

    expect(response.body.data.login).to.be.a('object');
    expect(response.body.data.login.user.id).to.not.be.null;
    expect(response.body.data.login.token).to.not.be.null;
    curatorToken = response.body.data.login.token;
  });

  it('search users w/ Admin role', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryUsers(
          searchSortInput: {
            searchFields: [{ fieldNames: ["roles.name"], operator: IN, searchValue: ["admin"] }]
          }
        ) {
          results {          
            id emailAddress firstName lastName
            roles {
              name
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryUsers.results.length).equal(1);
    expect(response.body.data.queryUsers.results[0].roles.map((role: Role) => role.name)).contain('ADMIN');
    expect(response.body.data.queryUsers.results[0].roles.map((role: Role) => role.name)).contain('CURATOR');
  });

  it('should get current user w/ roles', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getCurrentUser {
            id emailAddress firstName lastName org1 org2 status
            roles {
              name
            }
            submissions {
              title
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getCurrentUser).to.be.a('object');
    expect(response.body.data.getCurrentUser.id).equal(Id.simpleId(3));
    expect(response.body.data.getCurrentUser.roles[0].name).equal('CURATOR');
  });

  it('should get current user w/ roles', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${superToken}`)
      .send({
        query: `query {
          getCurrentUser {
            id emailAddress firstName lastName org1 org2 status
            privilegeGroups {
              name
              privileges {
                name
                resourceType
                resourceId
              }
            }
            submissions {
              title
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getCurrentUser).to.be.a('object');
    expect(response.body.data.getCurrentUser.id).equal(Id.simpleId(5));
    expect(response.body.data.getCurrentUser.privilegeGroups.length).equal(1);
  });

  it('get users', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        getUsers {
          id emailAddress firstName lastName org1 org2 status
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getUsers).to.be.a('array');
    expect(response.body.data.getUsers.length).equal(USERS_PER_TENANT);
  });

  it('search users for lastName substring', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryUsers(
          searchSortInput: {
            searchFields: [{ fieldNames: ["lastName"], operator: MATCH, searchValue: "${USER_SUBSTRING}" }]
          }
        ) {
          results {          
            id emailAddress firstName lastName
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryUsers.results.length).equal(2);
    expect(response.body.data.queryUsers.results[0].lastName).contains(USER_SUBSTRING);
    expect(response.body.data.queryUsers.results[1].lastName).contains(USER_SUBSTRING);
  });

  it('page users and sort', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryUsers(
          pagingInput: { pageSize: 2, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "emailAddress", ascending: true }]
          }
        ) {
          results {          
            id emailAddress
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryUsers.results).to.be.a('array');
    expect(response.body.data.queryUsers.results.length).equal(2);
    expect(response.body.data.queryUsers.pageInfo.hasNext).equal(true);
    expect(response.body.data.queryUsers.pageInfo.hasPrevious).equal(false);
    expect(response.body.data.queryUsers.pageInfo.lastCursor).equal('0');
    expect(response.body.data.queryUsers.pageInfo.lastPageSize).equal(2);
    expect(response.body.data.queryUsers.pageInfo.retrievedCount).equal(2);
    expect(response.body.data.queryUsers.pageInfo.totalCount).equal(5);
    expect(response.body.data.queryUsers.results[0].emailAddress).contains('admin');
  });

  it('should login user w/ tenant alias', async () => {
    const response = await request
      .post('/curator')
      .send({
        query: `mutation {
          login (
              userName: "<EMAIL>",
              password: "passcurator",
              tenantHandle: "1_tenantAlias"
            ){
              user {
                id
              }
              token
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.login).to.be.a('object');
    expect(response.body.data.login.user.id).to.not.be.null;
    expect(response.body.data.login.token).to.not.be.null;
    curatorToken = response.body.data.login.token;
  });

  it('should get user by id', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getUser(id: "${Id.simpleId(1)}") {
            id emailAddress firstName lastName org1 org2 status
            submissions {
              title
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getUser).to.be.a('object');
    expect(response.body.data.getUser.id).equal(Id.simpleId(1));
  });

  it('should register user', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .send({
        query: `mutation {
          register (
            input: {
              emailAddress: "<EMAIL>",
              password: "someP@ss1abcd1234",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "monumentsMen"
            },
            ){
              user {
                id
              } token
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.register).to.be.a('object');
    expect(response.body.data.register.user.id).to.not.be.null;
    expect(response.body.data.register.token).to.not.be.null;
  });

  /***
   *       ___     __      _        ____
   *      / _ |___/ /_ _  (_)__    / __ \___  ___
   *     / __ / _  /  ' \/ / _ \  / /_/ / _ \(_-<
   *    /_/ |_\_,_/_/_/_/_/_//_/  \____/ .__/___/
   *                                  /_/
   */

  it('should create user', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          createUser (
            input: {
              emailAddress: "<EMAIL>",
              password: "someP@ss1",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true }
            },
            links: {
              roleNames: [CURATOR]
            }
            ){
              id emailAddress firstName lastName org1 org2 status roles { name }
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createUser).to.be.a('object');
    expect(response.body.data.createUser.id).to.not.be.null;
  });

  it('should update user and add roles', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
        updateUser (input: {
          org1 : "33333",
          org2 : "995959",
          options: { optOutAll: true }
        },
        links: {
          roleNames: [CURATOR, ADMIN]
        }
        id: "${Id.simpleId(1)}") {
              id emailAddress firstName lastName org1 org2 status roles { name }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateUser).to.be.a('object');
    expect(response.body.data.updateUser.org1).equal('33333');
    expect(response.body.data.updateUser.id).equal(Id.simpleId(1));
    expect(response.body.data.updateUser.roles.map((role: Role) => role.name)).contain('CURATOR');
    expect(response.body.data.updateUser.roles.map((role: Role) => role.name)).contain('ADMIN');
  });

  it('should update user and set (remove) roles', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
        updateUser (input: {
          org1 : "33333",
          org2 : "995959",
          options: { optOutAll: true }
        },
        links: {
          roleNames: [CURATOR]
        }
        id: "${Id.simpleId(1)}") {
              id emailAddress firstName lastName org1 org2 status roles { name }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateUser).to.be.a('object');
    expect(response.body.data.updateUser.org1).equal('33333');
    expect(response.body.data.updateUser.id).equal(Id.simpleId(1));
    expect(response.body.data.updateUser.roles.length).equal(1);
    expect(response.body.data.updateUser.roles.map((role: Role) => role.name)).contain('CURATOR');
    expect(response.body.data.updateUser.roles.map((role: Role) => role.name)).not.contain('ADMIN');
  });

  it('should remove user privilege groups from user group', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
        updateUser (input: {
          org1 : "33333",
          org2 : "995959",
          options: { optOutAll: true }
        },
        links: {
          roleNames: [CURATOR]
        }
        id: "${Id.simpleId(1)}") {
              id emailAddress firstName lastName org1 org2 status roles { name } privilegeGroups { name }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateUser).to.be.a('object');
    expect(response.body.data.updateUser.id).equal(Id.simpleId(1));
    expect(response.body.data.updateUser.privilegeGroups.length).equal(0);
  });

  it('Analyst role update with privilege groups multi assoc', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
        updateUser (input: {
          org1 : "33333",
          org2 : "995959",
          options: { optOutAll: true }
        },
        links: {
          roleNames: [ANALYST, CURATOR]
        }
        id: "${Id.simpleId(1)}") {
              id emailAddress firstName lastName org1 org2 status roles { name } privilegeGroups { name }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateUser).to.be.a('object');
    expect(response.body.data.updateUser.id).equal(Id.simpleId(1));
    expect(response.body.data.updateUser.privilegeGroups.length).equal(2);
  });

  it('Admin role update with privilege groups multi assoc', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
        updateUser (input: {
          org1 : "33333",
          org2 : "995959",
          options: { optOutAll: true }
        },
        links: {
          roleNames: [ADMIN, ANALYST, CURATOR]
        }
        id: "${Id.simpleId(1)}") {
              id emailAddress firstName lastName org1 org2 status roles { name } privilegeGroups { name }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateUser).to.be.a('object');
    expect(response.body.data.updateUser.id).equal(Id.simpleId(1));
    expect(response.body.data.updateUser.privilegeGroups.length).equal(2);
  });

  it('should add additional privilege groups association for new role', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminTokenTenant2}`)
      .send({
        query: `mutation {
        updateUser (input: {
          org1 : "33333",
          org2 : "995959",
          options: { optOutAll: true }
        },
        links: {
          roleNames: [ANALYST, CURATOR]
        }
        id: "${Id.simpleId(2, 1)}") {
              id emailAddress firstName lastName org1 org2 status roles { name } privilegeGroups { name }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateUser).to.be.a('object');
    expect(response.body.data.updateUser.id).equal(Id.simpleId(2, 1));
    expect(response.body.data.updateUser.privilegeGroups.length).equal(2);
  });

  it('should delete user', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          deleteUser (id: "${Id.simpleId(1)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteUser).to.be.true;
  });
});
