import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let adminToken: string;

/*
  @TODO: Finish test this with privileges and links
*/

describe('))))))))))))) Privilege Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    adminToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(4, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.ADMIN]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });
  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should get the PrivilegeGroup by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `query {
          getPrivilegeGroup(id: "${Id.simpleId(0, ID_PREFIX_0)}") {
            id
            name
            privileges {
              id
              name
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getPrivilegeGroup).to.be.a('object');
    expect(response.body.data.getPrivilegeGroup.id).equal(Id.simpleId(0, ID_PREFIX_0));
    expect(response.body.data.getPrivilegeGroup.name).equal('Monuments Men Only');
    expect(response.body.data.getPrivilegeGroup.privileges).to.be.a('array');
    expect(response.body.data.getPrivilegeGroup.privileges[0].name).equal('The Monuments Men');
  });

  it('should create privilegeGroup', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          createPrivilegeGroup (
            input: {
              name: "New Group" 
            },
          ){
            id
            name
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createPrivilegeGroup).to.be.a('object');
    expect(response.body.data.createPrivilegeGroup.id).to.not.be.null;
    expect(response.body.data.createPrivilegeGroup.name).equal('New Group');
  });

  /*
    @TODO  finish testing this with links
  */
  it('should update privilegeGroup', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
            updatePrivilegeGroup (input: {
              name: "Renamed Group" 
        }, id: "${Id.simpleId(0, ID_PREFIX_0)}") {
            id name
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updatePrivilegeGroup).to.be.a('object');
    expect(response.body.data.updatePrivilegeGroup.id).to.not.be.null;
    expect(response.body.data.updatePrivilegeGroup.name).equal('Renamed Group');
  });

  it('should delete privilegeGroup', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          deletePrivilegeGroup (id: "${Id.simpleId(0, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deletePrivilegeGroup).to.be.true;
  });
});
