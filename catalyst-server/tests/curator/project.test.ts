import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { Category } from 'core/entities/Category';
import { Stakeholder } from 'core/entities/Stakeholder';
import {
  ATTACHMENT_FILE_NAME,
  ID_PREFIX_0,
  MATCH_SUBSTRING,
  PROJS_PER_TENANT,
  restoreDbFromLocalBackup,
} from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { DEFAULT_PAGE_SIZE } from 'core/storage/queryUtils';
import { User } from 'core/entities/User';
import { Attachment } from 'core/entities/Attachment';
import { ProjectStakeholder } from 'core/entities/ProjectStakeholder';
import { Opportunity } from 'core/entities/Opportunity';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe(')))))))))))))) Curator Project Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should get project by id with CurationInfo', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getProject(id: "${Id.simpleId(3, ID_PREFIX_0)}") {
            id title status background startDate endDate
            curationInfo {
              lastCurated
              users {
                id emailAddress
              }
            }
            creator {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getProject).to.be.a('object');
    expect(response.body.data.getProject.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.getProject.curationInfo).to.be.a('object');
    expect(response.body.data.getProject.curationInfo.users).to.be.a('array');
    expect(response.body.data.getProject.curationInfo.users.map((u: User) => u.id)).to.contain(
      Id.simpleId(3, ID_PREFIX_0),
    );
  });

  it('search projects for string OR', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryProjects(
          searchSortInput: {
            searchFields: [{ fieldNames: ["background", "goals"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"}]
          }
        ) {
          results {          
            id title status background startDate endDate
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryProjects.results.length).equal(4);
  });

  it('search projects for string AND', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryProjects(
          searchSortInput: {
            searchFields: [
              { fieldNames: ["background"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"},
              { fieldNames: ["goals"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"}
            ]
          }
        ) {
          results {          
            id title status background startDate endDate
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryProjects.results.length).equal(2);
  });

  it('filter projects by date (since <some date>)', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryProjects(
          searchSortInput: {
            searchFields: [{ fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }]
          }
        ) {
          results {          
            id title status background startDate endDate
            categories {
                name
            }
            attachments {
                name
            }
            opportunities {
                id
            }
            curationInfo {
              lastCurated
              users {
                id emailAddress
              }
            }
            creator {
              id emailAddress
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryProjects.results).to.be.a('array');
    expect(response.body.data.queryProjects.pageInfo.hasNext).equal(false);
    expect(response.body.data.queryProjects.pageInfo.hasPrevious).equal(false);
    expect(response.body.data.queryProjects.pageInfo.lastCursor).equal('0');
    expect(response.body.data.queryProjects.pageInfo.lastPageSize).equal(DEFAULT_PAGE_SIZE);
    expect(response.body.data.queryProjects.pageInfo.retrievedCount).equal(PROJS_PER_TENANT);
    // should be all bootstraped projects
    expect(response.body.data.queryProjects.results.length).equal(PROJS_PER_TENANT);
  });

  it('page projects and sort', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryProjects(
          pagingInput: { pageSize: 20, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: false }, { fieldName: "title", ascending: true}]
          }
        ) {
          results {          
            id title status background startDate endDate
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryProjects.results).to.be.a('array');
    expect(response.body.data.queryProjects.results.length).equal(PROJS_PER_TENANT);
    expect(response.body.data.queryProjects.pageInfo.hasNext).equal(false);
    expect(response.body.data.queryProjects.pageInfo.hasPrevious).equal(false);
    expect(response.body.data.queryProjects.pageInfo.lastCursor).equal('0');
    expect(response.body.data.queryProjects.pageInfo.lastPageSize).equal(20);
    expect(response.body.data.queryProjects.pageInfo.retrievedCount).equal(20);
    expect(response.body.data.queryProjects.pageInfo.totalCount).equal(20);
    expect(response.body.data.queryProjects.results[0].id).equal(Id.simpleId(PROJS_PER_TENANT, ID_PREFIX_0));
    expect(response.body.data.queryProjects.results[19].id).equal(Id.simpleId(1, ID_PREFIX_0));
  });

  it('page projects with offset', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryProjects(
          pagingInput: { pageSize: 3, cursor: "9" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: true } ]
          }
        ) {
          results {          
            id title status background startDate endDate
            categories {
                name
            }
            projectStakeholders {
                stakeholder {
                    name
                }
                type
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryProjects.results).to.be.a('array');
    expect(response.body.data.queryProjects.results.length).equal(3);
    expect(response.body.data.queryProjects.pageInfo.hasNext).equal(true);
    expect(response.body.data.queryProjects.pageInfo.hasPrevious).equal(true);
    expect(response.body.data.queryProjects.pageInfo.lastCursor).equal('9');
    expect(response.body.data.queryProjects.pageInfo.lastPageSize).equal(3);
    expect(response.body.data.queryProjects.pageInfo.retrievedCount).equal(3);
    expect(response.body.data.queryProjects.pageInfo.totalCount).equal(20);
    expect(response.body.data.queryProjects.results[0].id).equal(Id.simpleId(10, ID_PREFIX_0));
    expect(response.body.data.queryProjects.results[2].id).equal(Id.simpleId(12, ID_PREFIX_0));
    expect(response.body.data.queryProjects.results[0].categories.map((c: Category) => c.name)).to.include.members([
      'category_10',
    ]);
    expect(
      response.body.data.queryProjects.results[2].projectStakeholders.map(
        (p: ProjectStakeholder) => p.stakeholder.name,
      ),
    ).to.include.members(['stakeholder_2']);
  });

  it('should create a project', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createProject (
            input: {
              title: "Ministry Of Truth",
              background: "Testing a background",
              status: PENDING,
              startDate: "2022-04-30T00:00:00.527Z",
              endDate: "2022-05-31T00:00:00.527Z",
              goals: "Some goals",
              type: "Pathfinder Makerspace",
              statusNotes: "Some status notes"
            },
            links: {
              creatorId: "${Id.simpleId(2, ID_PREFIX_0)}",
            }
          ){
            id title status background startDate endDate
            curationInfo {
              lastCurated
              users {
                id emailAddress
              }
            }
            creator {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createProject).to.be.a('object');
    expect(response.body.data.createProject.id).to.not.be.null;
    expect(response.body.data.createProject.creator.emailAddress).to.not.be.null;
    expect(response.body.data.createProject.background).to.equal('Testing a background');
    expect(response.body.data.createProject.startDate).to.equal('2022-04-30T00:00:00.527Z');
  });

  it('should create a project from an opportunity', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createProjectFromOpportunity (
            input: {
              title: "Ministry Of Truth",
              opportunityId: "${Id.simpleId(1, ID_PREFIX_0)}",
              includeProblemSolution: true,
              includeCategories: true,
              includeAttachments: true
            },
          ){
            id title status background startDate endDate
            categories {
                name
            }
            attachments {
                name
            }
            opportunities {
                id
            }
            curationInfo {
              lastCurated
              users {
                id emailAddress
              }
            }
            creator {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createProjectFromOpportunity).to.be.a('object');
    expect(response.body.data.createProjectFromOpportunity.id).to.not.be.null;
    expect(response.body.data.createProjectFromOpportunity.creator.emailAddress).to.not.be.null;
    expect(response.body.data.createProjectFromOpportunity.categories.length).equal(1);
    expect(response.body.data.createProjectFromOpportunity.categories.map((c: Category) => c.name)).to.include.members([
      'category_1',
    ]);
    expect(
      response.body.data.createProjectFromOpportunity.categories.map((c: Category) => c.name),
    ).not.to.include.members(['category_4']);
    expect(response.body.data.createProjectFromOpportunity.opportunities.length).equal(1);
    expect(response.body.data.createProjectFromOpportunity.opportunities[0].id).to.equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.createProjectFromOpportunity.attachments.length).equal(3);
    expect(
      response.body.data.createProjectFromOpportunity.attachments.map((a: Attachment) => a.name),
    ).to.include.members([ATTACHMENT_FILE_NAME]);
  });

  it('should update project and add/remove categories, ops, and stakeholders (which are ordered) ', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateProject (input: {
              background: "Updated Background",
              status: PENDING,
              startDate: "2022-07-31T00:00:00.527Z",
              endDate: "2022-08-31T00:00:00.527Z",
              type: "Pathfinder Seed",
              statusNotes: "Updated status notes"
              goals: "Updated Goals"
        }, links: {
           projectStakeholders: [
            { operator: ADD, items: [
              { id: "${Id.simpleId(7, ID_PREFIX_0)}", type: PERFORMER },
              { id: "${Id.simpleId(8, ID_PREFIX_0)}", type: PERFORMER },
              { id: "${Id.simpleId(4, ID_PREFIX_0)}", type: PERFORMER },
            ]},
            { operator: RM, items: [
              { id: "${Id.simpleId(3, ID_PREFIX_0)}", type: TRANSITION },
              { id: "${Id.simpleId(8, ID_PREFIX_0)}", type: TRANSITION },
            ]},
           ],
            categories: [
            { operator: ADD, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
            { operator: RM, ids: ["${Id.simpleId(3, ID_PREFIX_0)}"] }
          ],
            opportunities: [
            { operator: ADD, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
            { operator: RM, ids: ["${Id.simpleId(3, ID_PREFIX_0)}"] }
          ],
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id title status background startDate endDate
          curationInfo {
            lastCurated
          }
          creator {
            id emailAddress
          }
          projectStakeholders {
              type
              stakeholder {
                  name
              }
          }
          categories {
              name
          }
          opportunities {
              id
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateProject).to.be.a('object');
    expect(response.body.data.updateProject.background).equal('Updated Background');
    expect(response.body.data.updateProject.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateProject.creator.emailAddress).to.not.be.null;
    expect(response.body.data.updateProject.projectStakeholders.length).equal(3);
    expect(
      response.body.data.updateProject.projectStakeholders.map((p: ProjectStakeholder) => p.stakeholder.name),
    ).to.eql(['stakeholder_7', 'stakeholder_8', 'stakeholder_4']);
    expect(
      response.body.data.updateProject.projectStakeholders.map((p: ProjectStakeholder) => p.stakeholder.name),
    ).not.to.include.members(['stakeholder_3']);
    expect(response.body.data.updateProject.categories.length).equal(2);
    expect(response.body.data.updateProject.categories.map((c: Category) => c.name)).to.include.members([
      'category_4',
      'category_5',
    ]);
    expect(response.body.data.updateProject.categories.map((c: Category) => c.name)).not.to.include.members([
      'category_3',
    ]);
    expect(response.body.data.updateProject.opportunities.length).equal(2);
    expect(response.body.data.updateProject.opportunities.map((o: Opportunity) => o.id)).to.include.members([
      Id.simpleId(4, ID_PREFIX_0),
      Id.simpleId(5, ID_PREFIX_0),
    ]);
    expect(response.body.data.updateProject.opportunities.map((o: Opportunity) => o.id)).not.to.include.members([
      Id.simpleId(3, ID_PREFIX_0),
    ]);
  });

  it('should update project and set categories, ops, stakeholders', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateProject (input: {
              background: "Updated Background",
              status: PENDING,
              startDate: "2022-07-31T00:00:00.527Z",
              endDate: "2022-08-31T00:00:00.527Z",
              type: "Pathfinder Seed",
              statusNotes: "Updated status notes"
              goals: "Updated Goals"
        }, links: {
           projectStakeholders: [
            { operator: SET, items: [
              { id: "${Id.simpleId(7, ID_PREFIX_0)}", type: PERFORMER },
              { id: "${Id.simpleId(8, ID_PREFIX_0)}", type: PERFORMER },
              { id: "${Id.simpleId(3, ID_PREFIX_0)}", type: TRANSITION },
              { id: "${Id.simpleId(3, ID_PREFIX_0)}", type:  DIVISION },
              { id: "${Id.simpleId(4, ID_PREFIX_0)}", type: TRANSITION },
            ]}
           ],
            categories: [
            { operator: SET, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
          ],
            opportunities: [
            { operator: SET, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
          ],
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id title status background startDate endDate
          curationInfo {
            lastCurated
          }
          creator {
            id emailAddress
          }
          projectStakeholders {
              type
              stakeholder {
                  name
              }
          }
          categories {
              name
          }
          opportunities {
              id
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateProject).to.be.a('object');
    expect(response.body.data.updateProject.background).equal('Updated Background');
    expect(response.body.data.updateProject.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateProject.creator.emailAddress).to.not.be.null;
    expect(response.body.data.updateProject.projectStakeholders.length).equal(5);
    expect(
      response.body.data.updateProject.projectStakeholders.map((p: ProjectStakeholder) => p.stakeholder.name),
    ).to.eql(['stakeholder_7', 'stakeholder_8', 'stakeholder_3', 'stakeholder_3', 'stakeholder_4']);
    expect(response.body.data.updateProject.categories.length).equal(2);
    expect(response.body.data.updateProject.categories.map((c: Category) => c.name)).to.include.members([
      'category_4',
      'category_5',
    ]);
    expect(response.body.data.updateProject.categories.map((c: Category) => c.name)).not.to.include.members([
      'category_3',
    ]);
    expect(response.body.data.updateProject.opportunities.length).equal(2);
    expect(response.body.data.updateProject.opportunities.map((o: Opportunity) => o.id)).to.include.members([
      Id.simpleId(4, ID_PREFIX_0),
      Id.simpleId(5, ID_PREFIX_0),
    ]);
    expect(response.body.data.updateProject.opportunities.map((o: Opportunity) => o.id)).not.to.include.members([
      Id.simpleId(3, ID_PREFIX_0),
    ]);
  });

  it('should update project and set stakeholders', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateProject (input: {
        }, links: {
           projectStakeholders: [
            { operator: SET, items: [
              { id: "${Id.simpleId(1, ID_PREFIX_0)}", type:  DIVISION},
              { id: "${Id.simpleId(1, ID_PREFIX_0)}", type:  TRANSITION },
              { id: "${Id.simpleId(7, ID_PREFIX_0)}", type: PERFORMER },
              { id: "${Id.simpleId(8, ID_PREFIX_0)}", type: PERFORMER },
              { id: "${Id.simpleId(3, ID_PREFIX_0)}", type:  DIVISION },
              { id: "${Id.simpleId(4, ID_PREFIX_0)}", type: TRANSITION },
              { id: "${Id.simpleId(10, ID_PREFIX_0)}", type: TRANSITION },
            ]}
          ],
        }, id: "${Id.simpleId(PROJS_PER_TENANT, ID_PREFIX_0)}") {
          id title status background startDate endDate
          curationInfo {
            lastCurated
          }
          creator {
            id emailAddress
          }
          projectStakeholders {
              type
              stakeholder {
                  name
              }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateProject).to.be.a('object');
    expect(response.body.data.updateProject.id).equal(Id.simpleId(PROJS_PER_TENANT, ID_PREFIX_0));
    expect(response.body.data.updateProject.creator.emailAddress).to.not.be.null;
    expect(response.body.data.updateProject.projectStakeholders.length).equal(7);
    expect(
      response.body.data.updateProject.projectStakeholders.map((p: ProjectStakeholder) => p.stakeholder.name),
    ).to.eql([
      'stakeholder_1',
      'stakeholder_1',
      'stakeholder_7',
      'stakeholder_8',
      'stakeholder_3',
      'stakeholder_4',
      'stakeholder_10',
    ]);
  });
});
