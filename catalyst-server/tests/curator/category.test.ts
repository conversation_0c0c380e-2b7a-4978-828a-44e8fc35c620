import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator Category Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get categories alphabetical', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryCategories(
          pagingInput: { pageSize: 5, cursor: "5" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: true }]
          }
        ) {
          results {
           id name
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryCategories.results).to.be.a('array');
    expect(response.body.data.queryCategories.results.length).equal(5);
    expect(response.body.data.queryCategories.results[0].name).equal('category_6');
  });

  it('search categories by prefix, sort alphabetical', async () => {
    // this should match category_1 and category_10
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryCategories(
          pagingInput: { pageSize: 10 },
          searchSortInput: {
            sortFields: [{ fieldName: "name", ascending: true }],
            searchFields: [{ fieldNames: ["name"], operator: MATCH, searchValue: "^category_1" }]
          }
        ) {
          results {
           id name
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryCategories.results).to.be.a('array');
    expect(response.body.data.queryCategories.results.length).equal(2);
    expect(response.body.data.queryCategories.results[0].name).equal('category_1');
    expect(response.body.data.queryCategories.results[1].name).equal('category_10');
  });

  it('should get category by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getCategory(id: "${Id.simpleId(3, ID_PREFIX_0)}") {
            id name
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getCategory).to.be.a('object');
    expect(response.body.data.getCategory.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.getCategory.name).equal('category_3');
  });

  it('should get category by name', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getCategory(name: "category_5") {
            id name
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getCategory).to.be.a('object');
    expect(response.body.data.getCategory.id).equal(Id.simpleId(5, ID_PREFIX_0));
    expect(response.body.data.getCategory.name).equal('category_5');
  });

  it('should create category', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createCategory (
            input: {
              name: "test_category"
            },
          ){
            id name
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createCategory).to.be.a('object');
    expect(response.body.data.createCategory.id).to.not.be.null;
    expect(response.body.data.createCategory.name).equal('test_category');
  });

  it('should return exiting category duplicate (case insensitive)', async () => {
    restoreDbFromLocalBackup();
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createCategory (
            input: {
              name: "CateGorY_1"
            },
          ){
            id name
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createCategory.name).equal('category_1');
  });

  it('should update category', async () => {
    restoreDbFromLocalBackup();
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateCategory (input: {
          name: "renamed_category_3"
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id name
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateCategory).to.be.a('object');
    expect(response.body.data.updateCategory.name).equal('renamed_category_3');
    expect(response.body.data.updateCategory.id).equal(Id.simpleId(3, ID_PREFIX_0));
  });

  it('should delete category', async () => {
    restoreDbFromLocalBackup();
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteCategory (id: "${Id.simpleId(3, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteCategory).to.be.true;
  });
});
