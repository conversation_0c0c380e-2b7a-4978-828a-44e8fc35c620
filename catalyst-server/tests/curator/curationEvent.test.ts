import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { CurationEvent } from 'core/entities/CurationEvent';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator CurationEvent Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get CurationEvents for a particular opportuntity', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryCurationEvents(
          pagingInput: { pageSize: 3, cursor: "0" },
          searchSortInput: {
            searchFields: [
              { fieldNames: ["entityId"], operator: EQ, searchValue: "${Id.simpleId(1, ID_PREFIX_0)}" },
              { fieldNames: ["entityType"], operator: EQ, searchValue: "o"},
            ]
            sortFields: [{ fieldName: "id", ascending: true }]
          }
        ) {
          results {
            type entityType entityId
            user {
              emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryCurationEvents.results).to.be.a('array');
    expect(response.body.data.queryCurationEvents.results.length).equal(3);
    expect((response.body.data.queryCurationEvents.results as []).map((r: CurationEvent) => r.type)).contain('CREATE');
    expect((response.body.data.queryCurationEvents.results as []).map((r: CurationEvent) => r.type)).contain('UPDATE');
  });

  it('should get CurationEvent by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getCurationEvent(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id
            user {
              id
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getCurationEvent).to.be.a('object');
    expect(response.body.data.getCurationEvent.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getCurationEvent.user.id).equal(Id.simpleId(3, ID_PREFIX_0));
  });

  it('should delete CurationEvent', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteCurationEvent (id: "${Id.simpleId(1, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteCurationEvent).to.be.true;
  });
});
