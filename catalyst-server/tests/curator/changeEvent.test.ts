import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { expect } from 'chai';
import { getToken } from 'core/auth/authUtils';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { ChangeEventType } from 'core/contracts/enums/ChangeEventType';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';
import { TargetType } from 'core/contracts/enums/TargetType';
import { ChangeEvent } from 'core/entities/ChangeEvent';
import { Id } from 'core/utils/Id';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;
let adminToken: string;

describe('))))))))))))) Core: ChangeEvent Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;

    adminToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(4, 0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.ADMIN]],
    }).token;

    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  describe('Event Creation', () => {
    it('should create a user creation event when registering a new user', async () => {
      // UserCreation Event
      const response = await request
        .post('/curator')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          query: `mutation {
          createUser (
            input: {
              emailAddress: "<EMAIL>",
              password: "someP@ss1abcd1234",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true }
            },
            links: {
              roleNames: [CURATOR]
            }
            ){
              id emailAddress firstName lastName org1 org2 status roles { name }
            }
        }
        `,
        })
        .expect(200);

      const userId = response.body.data.createUser.id;

      const events = await em!.find(ChangeEvent, {
        targetType: TargetType.USER,
        targetId: userId,
      });

      expect(events).to.have.lengthOf(1);
      expect(events[0].eventType).to.equal(ChangeEventType.UserCreation);
      expect(events[0].payload).to.deep.include({
        emailAddress: '<EMAIL>',
        firstName: 'Burt',
        lastName: 'McUser',
      });
    });

    it('should create a password change event when changing password', async () => {
      // UserPasswordChange Event
      await request
        .post('/curator')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          query: `mutation {
            updateCurrentUser(
              input: {
                password: "newP@ss5dfgh+098"
              }
            ) {
              id
            }
          }`,
        })
        .expect(200);

      const events = await em!.find(ChangeEvent, {
        targetType: TargetType.USER,
        targetId: Id.simpleId(4, ID_PREFIX_0),
        eventType: ChangeEventType.UserPasswordChange,
      });

      expect(events).to.have.lengthOf(1);
      expect(events[0].eventType).to.equal(ChangeEventType.UserPasswordChange);
      expect(events[0].payload).to.have.property('_password');
    });

    it('should create a user deletion event when deleting a user', async () => {
      // UserCreation Event
      const createResponse = await request
        .post('/user')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          query: `mutation {
            submitUser(
              input: {
                emailAddress: "<EMAIL>",
                firstName: "To",
                lastName: "Delete",
                org1: "Test Org",
                phone: "************",
                options: { cookieAcceptance: true },
                tenantHandle: "monumentsMen"
              }
            ) {
              id
            }
          }`,
        })
        .expect(200);

      const userId = createResponse.body.data.submitUser.id;

      // UserDeletion Event
      await request
        .post('/curator')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          query: `mutation {
            deleteUser(id: "${userId}")
          }`,
        })
        .expect(200);

      // Filtered by targetId, targetType, and eventType
      const events = await em!.find(ChangeEvent, {
        targetType: TargetType.USER,
        targetId: userId,
        eventType: ChangeEventType.UserDeletion,
      });

      expect(events).to.have.lengthOf(1);
      expect(events[0].eventType).to.equal(ChangeEventType.UserDeletion);
    });
  });

  describe('Event Querying', () => {
    it('should find all events for a user', async () => {
      // UserCreation Event
      const createResponse = await request
        .post('/user')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          query: `mutation {
            submitUser(
              input: {
                emailAddress: "<EMAIL>",
                firstName: "Test",
                lastName: "User",
                org1: "Test Org",
                phone: "************",
                options: { cookieAcceptance: true },
                tenantHandle: "monumentsMen"
              }
            ) {
              id
            }
          }`,
        })
        .expect(200);

      const userId = createResponse.body.data.submitUser.id;

      // UserPasswordChange Event
      await request
        .post('/curator')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          query: `mutation {
            updateUser(
              input: {
                password: "newP@ss5dfgh+098"
              }
              id: "${userId}"
            ) {
              id
            }
          }`,
        })
        .expect(200);

      const events = await em!.find(ChangeEvent, {
        targetType: TargetType.USER,
        targetId: userId,
      });

      expect(events).to.have.lengthOf(2);
      expect(events[0].eventType).to.equal(ChangeEventType.UserCreation);
      expect(events[1].eventType).to.equal(ChangeEventType.UserPasswordChange);
    });
  });
});
