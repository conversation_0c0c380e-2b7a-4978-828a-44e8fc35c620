import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator Link Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
  });

  beforeEach(async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  it('should create, update, and delete a link', async () => {
    // First create a link
    let response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          addLink(
            input: {
              name: "Test Link",
              url: "https://example.com",
              notes: "Initial notes"
            },
            links: {
              opportunityId: "${Id.simpleId(3, ID_PREFIX_0)}"
            }
          ) {
            id
            name
            url
            notes
            createdBy {
              id
            }
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.addLink.id).to.be.not.null;
    expect(response.body.data.addLink.name).equal('Test Link');
    expect(response.body.data.addLink.url).equal('https://example.com');
    expect(response.body.data.addLink.notes).equal('Initial notes');
    expect(response.body.data.addLink.createdBy.id).equal(Id.simpleId(3, ID_PREFIX_0));

    const linkId = response.body.data.addLink.id;

    // Now update the link
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          updateLink(input: {
            id: "${linkId}",
            name: "Updated Link Name",
            url: "https://updated-example.com",
            notes: "Updated notes for this link"
          }) {
            id
            name
            url
            notes
            createdBy {
              id
            }
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.updateLink.id).equal(linkId);
    expect(response.body.data.updateLink.name).equal('Updated Link Name');
    expect(response.body.data.updateLink.url).equal('https://updated-example.com');
    expect(response.body.data.updateLink.notes).equal('Updated notes for this link');
    expect(response.body.data.updateLink.createdBy.id).equal(Id.simpleId(3, ID_PREFIX_0)); // Should remain unchanged

    // Clean up - delete the link
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteLink(id: "${linkId}")
        }`,
      })
      .expect(200);

    expect(response.body.data.deleteLink).to.be.true;
  });

  it('should update only specified fields', async () => {
    // First create a link
    let response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          addLink(
            input: {
              name: "Original Name",
              url: "https://original.com",
              notes: "Original notes"
            },
            links: {
              opportunityId: "${Id.simpleId(3, ID_PREFIX_0)}"
            }
          ) {
            id
            name
            url
            notes
          }
        }`,
      })
      .expect(200);

    const linkId = response.body.data.addLink.id;

    // Update only the name
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          updateLink(input: {
            id: "${linkId}",
            name: "Only Name Updated"
          }) {
            id
            name
            url
            notes
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.updateLink.id).equal(linkId);
    expect(response.body.data.updateLink.name).equal('Only Name Updated');
    expect(response.body.data.updateLink.url).equal('https://original.com'); // Should remain unchanged
    expect(response.body.data.updateLink.notes).equal('Original notes'); // Should remain unchanged

    // Clean up
    await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteLink(id: "${linkId}")
        }`,
      })
      .expect(200);
  });
});
