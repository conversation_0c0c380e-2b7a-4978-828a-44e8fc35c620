import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator Attachment Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
  });

  beforeEach(async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  it('should create attachment, retrieve, and delete', async () => {
    const operations = `{
      "query":"mutation UploadAttachment($input: Upload!, $links: AttachmentLinks!) { addAttachment(input: $input, links: $links) { id name mimetype encoding } }",
      "variables": { "input": null, "links": { "opportunityId": "${Id.simpleId(3, ID_PREFIX_0)}" } }
    }`;
    const map = `{ "0": ["variables.input"] }`;

    let response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .field('operations', operations)
      .field('map', map)
      .attach('0', __dirname + '/test.png')
      .expect(200);

    expect(response.body.data.addAttachment.id).to.be.not.null;
    expect(response.body.data.addAttachment.name).equal('test.png');
    expect(response.body.data.addAttachment.mimetype).equal('image/png');
    expect(response.body.data.addAttachment.encoding).equal('7bit');

    const attachmentId = response.body.data.addAttachment.id;

    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getAttachmentLocation(id: "${attachmentId}") {
            location
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getAttachmentLocation.location).to.be.not.null;

    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteAttachment (id: "${attachmentId}")
        }
        `,
      })
      .expect(200);
  });

  it('should update attachment displayName and notes', async () => {
    // First create an attachment
    const operations = `{
      "query":"mutation UploadAttachment($input: Upload!, $links: AttachmentLinks!) { addAttachment(input: $input, links: $links) { id name mimetype encoding displayName notes } }",
      "variables": { "input": null, "links": { "opportunityId": "${Id.simpleId(3, ID_PREFIX_0)}" } }
    }`;
    const map = `{ "0": ["variables.input"] }`;

    let response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .field('operations', operations)
      .field('map', map)
      .attach('0', __dirname + '/test.png')
      .expect(200);

    const attachmentId = response.body.data.addAttachment.id;

    // Now update the attachment
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          updateAttachment(input: {
            id: "${attachmentId}",
            displayName: "Updated Display Name",
            notes: "Updated notes for this attachment"
          }) {
            id
            displayName
            notes
            name
            mimetype
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.updateAttachment.id).equal(attachmentId);
    expect(response.body.data.updateAttachment.displayName).equal('Updated Display Name');
    expect(response.body.data.updateAttachment.notes).equal('Updated notes for this attachment');
    expect(response.body.data.updateAttachment.name).equal('test.png'); // Should remain unchanged
    expect(response.body.data.updateAttachment.mimetype).equal('image/png'); // Should remain unchanged

    // Clean up
    await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteAttachment (id: "${attachmentId}")
        }`,
      })
      .expect(200);
  });
});
