import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { Category } from 'core/entities/Category';
import { Stakeholder } from 'core/entities/Stakeholder';
import { CAMPAIGNS, ID_PREFIX_0, MATCH_SUBSTRING, OPS_PER_TENANT, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { DEFAULT_PAGE_SIZE } from 'core/storage/queryUtils';
import { User } from 'core/entities/User';
import { Opportunity } from 'core/entities/Opportunity';
import { RelatedOpportunity } from 'core/entities/RelatedOpportunity';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe(')))))))))))))) Curator Opportunity Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('search opportunities for string OR fieldnames', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["title", "statement"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"}]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results.length).equal(4);
  });

  // this should match category_1 and category_10 for 2 opps each = 4
  it('search opportunities for category names', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["categories.name"], operator: MATCH, searchValue: "category_1"}]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results.length).equal(5);
  });

  it('search opportunities for string AND', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [
              { fieldNames: ["title"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"},
              { fieldNames: ["statement"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"}
            ]
          }
        ) {
          results {          
            id title statement context benefits status
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results.length).equal(2);
  });

  it('filter opportunities by status using OR', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["status"], operator: IN, searchValue: ["Approved", "Archived"] }]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(13);
  });

  it('filter opportunities by deleted status only', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["status"], operator: IN, searchValue: ["Deleted"] }]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(6);
    expect(response.body.data.queryOpportunities.results.map((r: any) => r.status)).contains('DELETED');
  });

  it('filter opportunities by deleted status using OR', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["status"], operator: IN, searchValue: ["Approved", "Deleted"] }]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(13);
    expect(response.body.data.queryOpportunities.results.map((r: any) => r.status)).contains('DELETED');
  });

  it('Simulate downoad query with no deleted opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["status"], operator: NE, searchValue: ["Deleted"] }]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(19);
    expect(response.body.data.queryOpportunities.results.map((r: any) => r.status)).not.contain('DELETED');
  });

  // Note: enum input values have to be treated as strings when usinjg searchFieldGroups because of JSONType // same as above example but using jsonSearchGroups
  it('filter opportunities using OR', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            jsonSearchGroups: [{ operator: "or", operands: [
              { fieldNames: ["status"], operator: "=", searchValue: "Approved" },
              { fieldNames: ["status"], operator: "=", searchValue: "Archived" }
            ] }]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(13);
  });

  // Note: enum input values have to be treated as strings when usinjg searchFieldGroups because of JSONType
  // category_1* should match 4 opps (category_1 / category_10) and catery_2* should match 2 ops but 1 of those is pending so ultimately only 1
  // this is a total of 5 opps
  it('filter opportunities using OR and AND', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            jsonSearchGroups: [
              { operator: "and", operands: [
                { operator: "or", operands: [
                  { fieldNames: ["status"], operator: "=", searchValue: "Approved" },
                  { fieldNames: ["status"], operator: "=", searchValue: "Archived" }
                ] },
                { operator: "or", operands: [
                  { fieldNames: ["categories.name"], operator: "~", searchValue: "category_1" },
                  { fieldNames: ["categories.name"], operator: "~", searchValue: "category_2" }
                ] }
              ] },
            ]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(5);
  });

  // Note: enum input values have to be treated as strings when usinjg searchFieldGroups because of JSONType
  // category_1* should match 4 opps (category_1 / category_10) and catery_2* should match 2 ops but 1 of those is pending so ultimately only 1
  // this is a total of 5 opps
  it('filter opportunities using OR and AND by using both searchFields and jsonSearchGroups', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["status"], operator: IN, searchValue: ["Approved", "Archived"] }],
            jsonSearchGroups: [
              { operator: "or", operands: [
                { fieldNames: ["categories.name"], operator: "~", searchValue: "category_1" },
                { fieldNames: ["categories.name"], operator: "~", searchValue: "category_2" }
              ] }
            ]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(5);
  });

  it('filter opportunities by date (since <some date>)', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.pageInfo.hasNext).equal(false);
    expect(response.body.data.queryOpportunities.pageInfo.hasPrevious).equal(false);
    expect(response.body.data.queryOpportunities.pageInfo.lastCursor).equal('0');
    expect(response.body.data.queryOpportunities.pageInfo.lastPageSize).equal(DEFAULT_PAGE_SIZE);
    expect(response.body.data.queryOpportunities.pageInfo.retrievedCount).equal(OPS_PER_TENANT);
    // should be all bootstraped opportunities
    expect(response.body.data.queryOpportunities.results.length).equal(OPS_PER_TENANT);
  });

  it('page opportunities and sort', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          pagingInput: { pageSize: 20, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: false }, { fieldName: "title", ascending: true}]
          }
        ) {
          results {          
            id title statement context benefits status
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(20);
    expect(response.body.data.queryOpportunities.pageInfo.hasNext).equal(true);
    expect(response.body.data.queryOpportunities.pageInfo.hasPrevious).equal(false);
    expect(response.body.data.queryOpportunities.pageInfo.lastCursor).equal('0');
    expect(response.body.data.queryOpportunities.pageInfo.lastPageSize).equal(20);
    expect(response.body.data.queryOpportunities.pageInfo.retrievedCount).equal(20);
    expect(response.body.data.queryOpportunities.pageInfo.totalCount).equal(OPS_PER_TENANT);
    expect(response.body.data.queryOpportunities.results[0].id).equal(Id.simpleId(OPS_PER_TENANT, ID_PREFIX_0));
    expect(response.body.data.queryOpportunities.results[19].id).equal(Id.simpleId(6, ID_PREFIX_0));
  });

  it('page opportunities and sort by org', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          pagingInput: { pageSize: 20, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "org1", ascending: false }, { fieldName: "org2", ascending: false }]
          }
        ) {
          results {          
            id title statement context benefits status
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(20);
    expect(response.body.data.queryOpportunities.pageInfo.hasNext).equal(true);
    expect(response.body.data.queryOpportunities.pageInfo.hasPrevious).equal(false);
    expect(response.body.data.queryOpportunities.pageInfo.lastCursor).equal('0');
    expect(response.body.data.queryOpportunities.pageInfo.lastPageSize).equal(20);
    expect(response.body.data.queryOpportunities.pageInfo.retrievedCount).equal(20);
    expect(response.body.data.queryOpportunities.pageInfo.totalCount).equal(OPS_PER_TENANT);
  });

  it('page forward opportunities (no more records)', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          pagingInput: { pageSize: 20, cursor: "20" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: false }, { fieldName: "title", ascending: true}]
          }
        ) {
          results {          
            id title statement context benefits status
            user {
              id emailAddress
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(5);
    expect(response.body.data.queryOpportunities.pageInfo.hasNext).equal(false);
    expect(response.body.data.queryOpportunities.pageInfo.hasPrevious).equal(true);
    expect(response.body.data.queryOpportunities.pageInfo.lastCursor).equal('20');
    expect(response.body.data.queryOpportunities.pageInfo.lastPageSize).equal(20);
    expect(response.body.data.queryOpportunities.pageInfo.retrievedCount).equal(5);
    expect(response.body.data.queryOpportunities.pageInfo.totalCount).equal(OPS_PER_TENANT);
  });

  it('page opportunities with offset', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          pagingInput: { pageSize: 3, cursor: "9" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: true } ]
          }
        ) {
          results {          
            id title statement context benefits status
            user {
              id emailAddress
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(3);
    expect(response.body.data.queryOpportunities.pageInfo.hasNext).equal(true);
    expect(response.body.data.queryOpportunities.pageInfo.hasPrevious).equal(true);
    expect(response.body.data.queryOpportunities.pageInfo.lastCursor).equal('9');
    expect(response.body.data.queryOpportunities.pageInfo.lastPageSize).equal(3);
    expect(response.body.data.queryOpportunities.pageInfo.retrievedCount).equal(3);
    expect(response.body.data.queryOpportunities.pageInfo.totalCount).equal(OPS_PER_TENANT);
    expect(response.body.data.queryOpportunities.results[0].id).equal(Id.simpleId(10, ID_PREFIX_0));
    expect(response.body.data.queryOpportunities.results[2].id).equal(Id.simpleId(12, ID_PREFIX_0));
  });

  it('page opportunities with offset larger than available results', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          pagingInput: { pageSize: 5, cursor: "24" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: true } ]
          }
        ) {
          results {          
            id title statement context benefits status
            user {
              id emailAddress
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(1);
    expect(response.body.data.queryOpportunities.pageInfo.hasNext).equal(false);
    expect(response.body.data.queryOpportunities.pageInfo.hasPrevious).equal(true);
    expect(response.body.data.queryOpportunities.pageInfo.lastCursor).equal('24');
    expect(response.body.data.queryOpportunities.pageInfo.lastPageSize).equal(5);
    expect(response.body.data.queryOpportunities.pageInfo.retrievedCount).equal(1);
    expect(response.body.data.queryOpportunities.pageInfo.totalCount).equal(OPS_PER_TENANT);
    expect(response.body.data.queryOpportunities.results[0].id).equal(Id.simpleId(OPS_PER_TENANT, ID_PREFIX_0));
  });

  it('get opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        getOpportunities {
          id title statement context benefits status
            curationInfo {
              lastCurated
            }
          user {
            id emailAddress
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getOpportunities).to.be.a('array');
    expect(response.body.data.getOpportunities.length).equal(OPS_PER_TENANT);
  });

  it('should get opportunity by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(3, ID_PREFIX_0)}") {
            id title statement context benefits status
            curationInfo {
              lastCurated
            }
            submissions {
              id title statement context benefits solutionConcepts campaign
              user {
                id emailAddress
              }
            }
            opportunities {
              id
              title
            }
            user {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.getOpportunity.submissions[0].title).equal(response.body.data.getOpportunity.title);
    expect(response.body.data.getOpportunity.opportunities.length).equal(2);
  });

  it('should get opportunity by id with CurationInfo', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id title statement context benefits status
            curationInfo {
              lastCurated
              users {
                id emailAddress
              }
            }
            user {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getOpportunity.curationInfo).to.be.a('object');
    expect(response.body.data.getOpportunity.curationInfo.users).to.be.a('array');
    expect(response.body.data.getOpportunity.curationInfo.users).to.be.a('array');
    expect(response.body.data.getOpportunity.curationInfo.users.map((u: User) => u.id)).to.contain(
      Id.simpleId(1, ID_PREFIX_0),
    );
    expect(response.body.data.getOpportunity.curationInfo.users.map((u: User) => u.id)).to.contain(
      Id.simpleId(3, ID_PREFIX_0),
    );
    expect(response.body.data.getOpportunity.curationInfo.users.map((u: User) => u.id)).to.contain(
      Id.simpleId(4, ID_PREFIX_0),
    );
  });

  it('should create opportunity for another user', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createOpportunity (
            input: {
              title: "Ministry Of Truth",
              statement: "A long statement",
              context: "Some context here..",
              benefits: "There are some, surely....",
              solutionConcepts: "Some concepts here...",
              campaign: "89494"
            },
            links: {
              userId: "${Id.simpleId(2, ID_PREFIX_0)}"
            }
          ){
            id title statement context benefits status
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createOpportunity).to.be.a('object');
    expect(response.body.data.createOpportunity.id).to.not.be.null;
    expect(response.body.data.createOpportunity.user.emailAddress).to.not.be.null;
  });

  it('should update opportunity and add/remove categories and stakeholders', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {
          context: "Updated context here..",
          benefits: "Some updated benefits",
        }, links: {
          categories: [
            { operator: ADD, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
            { operator: RM, ids: ["${Id.simpleId(3, ID_PREFIX_0)}"] }
          ],
          stakeholders: [
            { operator: ADD, ids: ["${Id.simpleId(7, ID_PREFIX_0)}", "${Id.simpleId(8, ID_PREFIX_0)}"] },
            { operator: RM, ids: ["${Id.simpleId(3, ID_PREFIX_0)}"] }
          ]
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id title statement context benefits status
          curationInfo {
            lastCurated
          }
          user {
            id emailAddress
          }
          categories {
            name
          }
          stakeholders {
            name
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.context).equal('Updated context here..');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.user.emailAddress).to.not.be.null;
    expect(response.body.data.updateOpportunity.categories.length).equal(2);
    expect(response.body.data.updateOpportunity.categories.map((c: Category) => c.name)).to.include.members([
      'category_4',
      'category_5',
    ]);
    expect(response.body.data.updateOpportunity.categories.map((c: Category) => c.name)).not.to.include.members([
      'category_3',
    ]);
    expect(response.body.data.updateOpportunity.stakeholders.length).equal(2);
    expect(response.body.data.updateOpportunity.stakeholders.map((s: Stakeholder) => s.name)).to.include.members([
      'stakeholder_7',
      'stakeholder_8',
    ]);
    expect(response.body.data.updateOpportunity.stakeholders.map((s: Stakeholder) => s.name)).not.to.include.members([
      'stakeholder_3',
    ]);
  });

  it('should update opportunity and set categories and stakeholders', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {
          context: "Updated context here..",
          benefits: "Some updated benefits",
        }, links: {
          categories: [
            { operator: SET, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
          ],
          stakeholders: [
            { operator: SET, ids: ["${Id.simpleId(7, ID_PREFIX_0)}", "${Id.simpleId(8, ID_PREFIX_0)}"] },
          ]
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id title statement context benefits status
          user {
            id emailAddress
          }
          categories {
            name
          }
          stakeholders {
            name
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.context).equal('Updated context here..');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.user.emailAddress).to.not.be.null;
    expect(response.body.data.updateOpportunity.categories.length).equal(2);
    expect(response.body.data.updateOpportunity.categories.map((c: Category) => c.name)).to.include.members([
      'category_4',
      'category_5',
    ]);
    expect(response.body.data.updateOpportunity.categories.map((c: Category) => c.name)).not.to.include.members([
      'category_3',
    ]);
    expect(response.body.data.updateOpportunity.stakeholders.length).equal(2);
    expect(response.body.data.updateOpportunity.stakeholders.map((s: Stakeholder) => s.name)).to.include.members([
      'stakeholder_7',
      'stakeholder_8',
    ]);
    expect(response.body.data.updateOpportunity.stakeholders.map((s: Stakeholder) => s.name)).not.to.include.members([
      'stakeholder_3',
    ]);
  });

  it('should update opportunity and add/remove opportunities', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          opportunities: [
            { operator: ADD, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
            { operator: RM, ids: ["${Id.simpleId(2, ID_PREFIX_0)}"] },
          ],
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
          id title statement context benefits status
          opportunities {
            id title opportunities {
              id title
            } 
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.opportunities.length).equal(3);
    expect(response.body.data.updateOpportunity.opportunities.map((o: Opportunity) => o.id)).to.include.members([
      Id.simpleId(3, ID_PREFIX_0),
      Id.simpleId(4, ID_PREFIX_0),
      Id.simpleId(5, ID_PREFIX_0),
    ]);
    expect(response.body.data.updateOpportunity.opportunities.map((o: Opportunity) => o.id)).not.to.include.members([
      Id.simpleId(1, ID_PREFIX_0),
    ]);
    expect(
      response.body.data.updateOpportunity.opportunities
        .find((o: Opportunity) => o.id === Id.simpleId(4, ID_PREFIX_0))
        .opportunities.map((o: Opportunity) => o.id),
    ).to.include.members([Id.simpleId(1, ID_PREFIX_0)]);
  });

  it('should update opportunity and set opportunities', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          opportunities: [
            { operator: SET, ids: ["${Id.simpleId(4, ID_PREFIX_0)}", "${Id.simpleId(5, ID_PREFIX_0)}"] },
          ],
        }, id: "${Id.simpleId(2, ID_PREFIX_0)}") {
          id title statement context benefits status
          opportunities {
            id title
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(2, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.opportunities.length).equal(2);
    expect(response.body.data.updateOpportunity.opportunities.map((o: Opportunity) => o.id)).to.include.members([
      Id.simpleId(4, ID_PREFIX_0),
      Id.simpleId(5, ID_PREFIX_0),
    ]);
    expect(response.body.data.updateOpportunity.opportunities.map((o: Opportunity) => o.id)).not.to.include.members([
      Id.simpleId(1, ID_PREFIX_0),
      Id.simpleId(3, ID_PREFIX_0),
    ]);
  });

  it('should update related opportunities and add opportunity', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          relatedOpportunities: [
           { operator: ADD, items: [
            { id: "${Id.simpleId(15, ID_PREFIX_0)}", type: CHILD }
           ] },
          ],
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id lastCurated
          ownedOpportunities {
            target {
              id title owningOpportunities {
                source {
                  id title
                }
              }
            } 
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.ownedOpportunities.length).equal(1);
  });

  it('should update related opportunities and add/remove opportunities', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          relatedOpportunities: [
           { operator: ADD, items: [
            { id: "${Id.simpleId(7, ID_PREFIX_0)}", type: CHILD }, { id: "${Id.simpleId(
              8,
              ID_PREFIX_0,
            )}", type: LINKED }
           ] },
           { operator: RM, items: [
              { id: "${Id.simpleId(2, ID_PREFIX_0)}", type: CHILD }, { id: "${Id.simpleId(
                4,
                ID_PREFIX_0,
              )}", type: CHILD }
           ] },
          ],
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
          id title statement context benefits status
          ownedOpportunities {
            target {
              id title owningOpportunities {
                source {
                  id title
                }
              }
            } 
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.ownedOpportunities.length).equal(5);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.map((r: RelatedOpportunity) => r.target.id),
    ).to.include.members([
      Id.simpleId(3, ID_PREFIX_0),
      Id.simpleId(5, ID_PREFIX_0),
      Id.simpleId(6, ID_PREFIX_0),
      Id.simpleId(7, ID_PREFIX_0),
      Id.simpleId(8, ID_PREFIX_0),
    ]);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.map((r: RelatedOpportunity) => r.target.id),
    ).not.to.include.members([Id.simpleId(1, ID_PREFIX_0), Id.simpleId(2, ID_PREFIX_0), Id.simpleId(4, ID_PREFIX_0)]);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities
        .find((r: RelatedOpportunity) => r.target.id === Id.simpleId(7, ID_PREFIX_0))
        .target.owningOpportunities.map((r: RelatedOpportunity) => r.source.id),
    ).to.include.members([Id.simpleId(1, ID_PREFIX_0)]);
  });

  it('should update opportunity and set related opportunities', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          relatedOpportunities: [
           { operator: SET, items: [
            { id: "${Id.simpleId(7, ID_PREFIX_0)}", type: CHILD }, { id: "${Id.simpleId(
              8,
              ID_PREFIX_0,
            )}", type: CHILD },
            { id: "${Id.simpleId(3, ID_PREFIX_0)}", type: CHILD},
           ] },
          ],
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
          id title statement context benefits status
          ownedOpportunities {
            target {
              id title owningOpportunities {
                source {
                  id title
                }
              }
            } 
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.ownedOpportunities.length).equal(3);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.map((r: RelatedOpportunity) => r.target.id),
    ).to.include.members([Id.simpleId(3, ID_PREFIX_0), Id.simpleId(7, ID_PREFIX_0), Id.simpleId(8, ID_PREFIX_0)]);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.map((r: RelatedOpportunity) => r.target.id),
    ).not.to.include.members([Id.simpleId(2, ID_PREFIX_0), Id.simpleId(4, ID_PREFIX_0), Id.simpleId(5, ID_PREFIX_0)]);
  });

  it('should update related opportunities and add related opportunity with same id different type', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          relatedOpportunities: [
           { operator: ADD, items: [
            { id: "${Id.simpleId(3, ID_PREFIX_0)}", type: LINKED }
           ] },
          ],
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
          id title statement context benefits status
          ownedOpportunities {
            type target {
              id
            } 
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.ownedOpportunities.length).equal(6);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.find(
        (r: RelatedOpportunity) => r.target.id === Id.simpleId(3, ID_PREFIX_0),
      ) && response.type === 'CHILD',
    ).to.be.not.null;
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.find(
        (r: RelatedOpportunity) => r.target.id === Id.simpleId(3, ID_PREFIX_0),
      ) && response.type === 'LINKED',
    ).to.be.not.null;
  });

  it('should not add related opportunity with same id, same type if it already exists', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          relatedOpportunities: [
           { operator: ADD, items: [
            { id: "${Id.simpleId(3, ID_PREFIX_0)}", type: CHILD }
           ] },
          ],
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
          id title statement context benefits status
          ownedOpportunities {
            type target {
              id
            } 
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.ownedOpportunities.length).equal(5);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.find(
        (r: RelatedOpportunity) => r.target.id === Id.simpleId(3, ID_PREFIX_0),
      ) && response.type === 'CHILD',
    ).to.be.not.null;
  });

  it('should not remove related opportunity with same id different type', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {}, links: {
          relatedOpportunities: [
           { operator: RM, items: [
            { id: "${Id.simpleId(3, ID_PREFIX_0)}", type: LINKED }
           ] },
          ],
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
          id title statement context benefits status
          ownedOpportunities {
            type target {
              id
            } 
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.ownedOpportunities.length).equal(5);
    expect(
      response.body.data.updateOpportunity.ownedOpportunities.find(
        (r: RelatedOpportunity) => r.target.id === Id.simpleId(3, ID_PREFIX_0),
      ) && response.type === 'CHILD',
    ).to.be.not.null;
  });

  // These are sensitive to the number of parent child relationships in the bootstrap data
  // right now there are 3 child and 3 linked relationships created for opportunity 1

  it('should count related opportunities', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id title statement context benefits status relatedOpportunityCount 
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getOpportunity.relatedOpportunityCount).equal(6);
  });

  it('should count child opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id title statement context benefits status childOpportunityCount 
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getOpportunity.childOpportunityCount).equal(3);
  });

  it('should count parent opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(4, ID_PREFIX_0)}") {
            id title statement context benefits status parentOpportunityCount 
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(4, ID_PREFIX_0));
    expect(response.body.data.getOpportunity.parentOpportunityCount).equal(1);
  });

  it('should count linked opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(5, ID_PREFIX_0)}") {
            id title statement context benefits status linkedOpportunityCount 
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(5, ID_PREFIX_0));
    expect(response.body.data.getOpportunity.linkedOpportunityCount).equal(1);
  });

  it('filter opportunities by non-child opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["parentOpportunityCount"], operator: EQ, searchValue: 0 }]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(OPS_PER_TENANT - 3);
  });

  it('sort opportunities on a subpath', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            sortFields: [{ fieldName: "user.emailAddress", ascending: true } ]
          }
        ) {
          results {          
            id title statement context benefits status
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results[0].user.emailAddress).equal('<EMAIL>');
  });

  it('get distinct campaign values', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        opportunityCalculation(
          calculation: {
            distinct: true,
            operations: [{ fieldName: "campaign" }]
          },
          searchSortInput: {
            searchFields: [{ fieldNames: ["status"], operator: IN, searchValue: ["Approved", "Archived", "Pending", "Deleted"] }]
          }
        ) {
          operationResults {
            result
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.opportunityCalculation.operationResults).to.be.a('array');
    expect(response.body.data.opportunityCalculation.operationResults.length).equal(1);
    expect(response.body.data.opportunityCalculation.operationResults[0].result).to.be.a('array');
    expect(response.body.data.opportunityCalculation.operationResults[0].result.length).equal(7);
    expect(response.body.data.opportunityCalculation.operationResults[0].result).contains(CAMPAIGNS[1]);
  });

  it('search for null values', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          searchSortInput: {
            searchFields: [{ fieldNames: ["campaignNotes"], operator: EQ, searchValue: null}]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results.length).equal(OPS_PER_TENANT);
  });

  it('should create opportunity with visibility set to private', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createOpportunity (
            input: {
              title: "Ministry Of Truth",
              statement: "A long statement",
              context: "Some context here..",
              benefits: "There are some, surely....",
              solutionConcepts: "Some concepts here...",
              campaign: "89494",
              visibility: PRIVATE
            },
            links: {
              userId: "${Id.simpleId(2, ID_PREFIX_0)}"
            }
          ){
            id title statement context benefits status, visibility
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createOpportunity).to.be.a('object');
    expect(response.body.data.createOpportunity.id).to.not.be.null;
    expect(response.body.data.createOpportunity.visibility).to.not.be.null;
    expect(response.body.data.createOpportunity.visibility).to.be.a('string');
    expect(response.body.data.createOpportunity.visibility).equal('PRIVATE');
  });

  it('should update opportunity visibility status to ALL', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {
          context: "Updated context here..",
          benefits: "Some updated benefits",
          visibility: ALL
        }, links: {
        },
        id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id title statement context benefits status, visibility
          curationInfo {
            lastCurated
          }
          user {
            id emailAddress
          }
          categories {
            name
          }
          stakeholders {
            name
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateOpportunity).to.be.a('object');
    expect(response.body.data.updateOpportunity.context).equal('Updated context here..');
    expect(response.body.data.updateOpportunity.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateOpportunity.visibility).to.not.be.null;
    expect(response.body.data.updateOpportunity.visibility).to.be.a('string');
    expect(response.body.data.updateOpportunity.visibility).equal('ALL');
  });

  it('Update should fail trying to set visibility to NULL', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunity (input: {
          context: "Updated context here..",
          benefits: "Some updated benefits",
          visibility: NULL
        }, links: {
        },
        id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id title statement context benefits status, visibility
          curationInfo {
            lastCurated
          }
          user {
            id emailAddress
          }
          categories {
            name
          }
          stakeholders {
            name
          }
        }
      }
      `,
      })
      .expect(400);
  });
});
