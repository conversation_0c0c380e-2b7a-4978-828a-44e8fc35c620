import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import tenantConfig from '../../config/tenants/monumentsMen/config.json';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;
let adminToken: string;

describe('))))))))))))) Curator Tenant Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    adminToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(4, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.ADMIN]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get tenants by name alphabetical', async () => {
    const response = await request
      .post('/curator')
      .send({
        query: `query {
        queryTenants(
          pagingInput: { pageSize: 5, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "name", ascending: true }]
          }
        ) {
          results {
           id name handle
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryTenants.results).to.be.a('array');
    expect(response.body.data.queryTenants.results.length).equal(3);
    expect(response.body.data.queryTenants.results[0].handle).equal('default');
    expect(response.body.data.queryTenants.results[2].handle).equal('monumentsMen');
  });

  it('should get tenant by id with no auth', async () => {
    const response = await request
      .post('/curator')
      .send({
        query: `query {
          getTenant(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id name handle meta {
              config theme
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenant).to.be.a('object');
    expect(response.body.data.getTenant.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getTenant.name).equal('The Monuments Men');
    expect(response.body.data.getTenant.handle).equal('monumentsMen');
    expect(response.body.data.getTenant.meta.config.applicationTitle).equal('Pathfinder Innovation');
    expect(response.body.data.getTenant.meta.theme).not.undefined;
  });

  it('should get tenant by alias with no auth', async () => {
    const response = await request
      .post('/curator')
      .send({
        query: `query {
          getTenant(handleOrAlias: "5_tenantAlias") {
            id name handle
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenant).to.be.a('object');
    expect(response.body.data.getTenant.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getTenant.name).equal('The Monuments Men');
    expect(response.body.data.getTenant.handle).equal('monumentsMen');
  });

  it('should get tenant info by alias with no auth', async () => {
    const response = await request
      .post('/curator')
      .send({
        query: `query {
          getTenantInfo(handleOrAlias: "5_tenantAlias") {
            tenantId name handle
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenantInfo).to.be.a('object');
    expect(response.body.data.getTenantInfo.tenantId).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getTenantInfo.name).equal('Tenant Alias 5');
    expect(response.body.data.getTenantInfo.handle).equal('5_tenantAlias');
  });

  it('should create tenant', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          createTenant (
            input: {
              name: "Test Tenant",
              handle: "test_tenant"
              config: { applicationTitle: "Test" }
            },
            adminPass: "admin-test-pass"
          ){
            id handle meta {
              config
              theme
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createTenant).to.be.a('object');
    expect(response.body.data.createTenant.id).to.not.be.null;
    expect(response.body.data.createTenant.handle).equal('test_tenant');
    expect(response.body.data.createTenant.meta.config.applicationTitle).equal('Test');
  });

  it('should fail to create tenant duplicate (case insensitive)', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          createTenant (
            input: {
              name: "Test Tenant",
              handle: "monumentsMen"
            },
            adminPass: "admin-test-pass"
          ){
            id handle
          }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.OBJECT_ALREADY_EXISTS].code);
  });

  it('should update tenant', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
        updateTenant (input: {
          handle: "renamed_tenant_1",
          config: { applicationTitle: "Test" }
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
          id handle meta {
            config
            theme
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateTenant).to.be.a('object');
    expect(response.body.data.updateTenant.handle).equal('renamed_tenant_1');
    expect(response.body.data.updateTenant.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateTenant.meta.config.applicationTitle).equal('Test');
    expect(response.body.data.updateTenant.meta.theme).not.null;
  });

  it('should delete tenant', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          deleteTenant (id: "${Id.simpleId(1, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteTenant).to.be.true;
  });
});
