import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0, ID_PREFIX_1,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';
import { Opportunity } from 'core/entities/Opportunity';
import { OpportunityVisibility } from 'core/contracts/enums/OpportunityVisibility';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let monumentsMenCuratorToken: string;
let starshipTroopersAnalystToken: string;
let monumentsMenAnalystToken: string;
let starshipTroopersCuratorToken: string;

// Currently starshipTroopers does not have the option to view "Private" submissions in other related tenants
// The baseline for this test is to verify starshipTroopers can see all of it's opportunities and only no "Private"
// opportunities in other tenants. MonumentsMen can see "Private" opportunities because of the serverConfig setting
// that allows them see "Private" opportunities across related tenants.
let starshipTroopersOpportunityCount = 25;
let starshipTroopersPrivateCount = 5;
let monumentsMenOpportunityCount = 25;
let monumentsMenPrivateCount = 5;
let starshipTroopersScopeCount = (starshipTroopersOpportunityCount + monumentsMenOpportunityCount) - (starshipTroopersPrivateCount + monumentsMenPrivateCount);
let monumentsMenScopeCount = starshipTroopersOpportunityCount + monumentsMenOpportunityCount;

describe(')))))))))))))) Curator Opportunity Visibility Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    monumentsMenCuratorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1, ID_PREFIX_0),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    monumentsMenAnalystToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(5, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1, ID_PREFIX_0),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    // this is the prefix for users for starshipTroopers (see loadFixtures.ts)
    starshipTroopersAnalystToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(5, ID_PREFIX_1),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(2, ID_PREFIX_0),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR], roleMap[RoleNames.ANALYST]],
    }).token;
    starshipTroopersCuratorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_1),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(2, ID_PREFIX_0),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR], roleMap[RoleNames.ANALYST]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should get private opportunity by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${starshipTroopersCuratorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(5, ID_PREFIX_1)}") {
            id title statement context benefits status visibility
            curationInfo {
              lastCurated
            }
            submissions {
              id title statement context benefits solutionConcepts campaign
              user {
                id emailAddress
              }
            }
            opportunities {
              id
              title
            }
            user {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(5, ID_PREFIX_1));
    expect(response.body.data.getOpportunity.visibility.toLowerCase()).equal(OpportunityVisibility.PRIVATE.toLowerCase());
  });

  it('should get All opportunity by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${starshipTroopersCuratorToken}`)
      .send({
        query: `query {
          getOpportunity(id: "${Id.simpleId(4, ID_PREFIX_1)}") {
            id title statement context benefits status visibility
            curationInfo {
              lastCurated
            }
            submissions {
              id title statement context benefits solutionConcepts campaign
              user {
                id emailAddress
              }
            }
            opportunities {
              id
              title
            }
            user {
              id emailAddress
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getOpportunity).to.be.a('object');
    expect(response.body.data.getOpportunity.id).equal(Id.simpleId(4, ID_PREFIX_1));
    expect(response.body.data.getOpportunity.visibility.toLowerCase()).equal(OpportunityVisibility.ALL.toLowerCase());
  });

  it('query monumentsMen opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${monumentsMenCuratorToken}`)
      .send({
        query: `query {
        queryOpportunities(scope: null) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(monumentsMenOpportunityCount);

    const found = response.body.data.queryOpportunities.results.filter((result: Opportunity) => 
      result.visibility.toLowerCase() === OpportunityVisibility.PRIVATE.toLowerCase());
    expect(found.length).to.eq(monumentsMenPrivateCount);

    const foundOther = response.body.data.queryOpportunities.results.find((result: Opportunity) => {
      result && result.tenant && result.tenant.id !== Id.simpleId(1, ID_PREFIX_0);
    });
    expect(foundOther).to.to.be.undefined;
  });

  it('query starshipTroopers opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${starshipTroopersAnalystToken}`)
      .send({
        query: `query {
        queryOpportunities(scope: null) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(starshipTroopersOpportunityCount);

    const found = response.body.data.queryOpportunities.results.filter((result: Opportunity) => result.visibility.toLowerCase() === OpportunityVisibility.PRIVATE.toLowerCase());
    expect(found.length).to.eq(starshipTroopersPrivateCount);

    const foundOther = response.body.data.queryOpportunities.results.find((result: Opportunity) => {
      result && result.tenant && result.tenant.id !== Id.simpleId(2, ID_PREFIX_0);
    });
    expect(foundOther).to.to.be.undefined;
  });

  it('query monumentsMen private opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${monumentsMenCuratorToken}`)
      .send({
        query: `query {
        queryOpportunities(scope: null, 
          searchSortInput: {
            searchFields: [{ fieldNames: ["visibility"], operator: MATCH, searchValue: "${OpportunityVisibility.PRIVATE}"}]
          }) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(monumentsMenPrivateCount);

    const found = response.body.data.queryOpportunities.results.filter((result: Opportunity) => 
      result.visibility.toLowerCase() === OpportunityVisibility.PRIVATE.toLowerCase());
    expect(found.length).to.eq(monumentsMenPrivateCount);

    const foundOther = response.body.data.queryOpportunities.results.find((result: Opportunity) => {
      result && result.tenant && result.tenant.id !== Id.simpleId(1, ID_PREFIX_0);
    });
    expect(foundOther).to.to.be.undefined;
  });

  it('query starshipTroopers private opportunities', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${starshipTroopersCuratorToken}`)
      .send({
        query: `query {
        queryOpportunities(scope: null, 
          searchSortInput: {
            searchFields: [{ fieldNames: ["visibility"], operator: MATCH, searchValue: "${OpportunityVisibility.PRIVATE}"}]
          }) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(starshipTroopersPrivateCount);

    const found = response.body.data.queryOpportunities.results.filter((result: Opportunity) => 
      result.visibility.toLowerCase() === OpportunityVisibility.PRIVATE.toLowerCase());
    expect(found.length).to.eq(starshipTroopersPrivateCount);

    const foundOther = response.body.data.queryOpportunities.results.find((result: Opportunity) => {
      result && result.tenant && result.tenant.id !== Id.simpleId(2, ID_PREFIX_0);
    });
    expect(foundOther).to.to.be.undefined;
  });

  // curator should not be able search all tenants
  it('query starshipTroopers opportunities with scope', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${starshipTroopersAnalystToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT },
               { resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT }]
          }
        ) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(starshipTroopersScopeCount);

    // Should contain no privates.
    expect(response.body.data.queryOpportunities.results.find(
      (opportunity: Opportunity) => opportunity.visibility.toLowerCase() === OpportunityVisibility.PRIVATE.toLowerCase())).to.be.undefined;

    // Should only get results for monumentsMen and starshipTroopers
    const star = response.body.data.queryOpportunities.results.filter((opp: any) => opp.tenant.id === Id.simpleId(2, ID_PREFIX_0));
    const mon = response.body.data.queryOpportunities.results.filter((opp: any) => opp.tenant.id === Id.simpleId(1, ID_PREFIX_0));
    expect(star.length).equal(starshipTroopersOpportunityCount - starshipTroopersPrivateCount);
    expect(mon.length).equal(monumentsMenOpportunityCount - monumentsMenPrivateCount);
    expect(star.length + mon.length).equal(starshipTroopersScopeCount);
  });

  it('Override query opportunities scope filter', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${starshipTroopersAnalystToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT },
               { resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT }]
          },
          searchSortInput: {
            searchFields: [{ fieldNames: ["visibility"], operator: MATCH, searchValue: "${OpportunityVisibility.PRIVATE}"}]
          }
        ) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(0);
  });

  it('query monumentsMen opportunities with scope', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${monumentsMenAnalystToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
               { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
          }
        ) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(monumentsMenScopeCount);

    // Should contain privates.
    expect(response.body.data.queryOpportunities.results.find(
      (opportunity: Opportunity) => opportunity.visibility.toLowerCase() === OpportunityVisibility.PRIVATE.toLowerCase())).to.not.be.undefined;
    
    // Should only get results for monumentsMen and starshipTroopers
    const star = response.body.data.queryOpportunities.results.filter((opp: any) => opp.tenant.id === Id.simpleId(2, ID_PREFIX_0));
    const mon = response.body.data.queryOpportunities.results.filter((opp: any) => opp.tenant.id === Id.simpleId(1, ID_PREFIX_0));
    expect(star.length).equal(starshipTroopersOpportunityCount);
    expect(mon.length).equal(monumentsMenOpportunityCount);
    expect(star.length + mon.length).equal(monumentsMenScopeCount);
  });

  it('MonumentsMen additional query scope for private', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${monumentsMenAnalystToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
               { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
          },
          searchSortInput: {
            searchFields: [{ fieldNames: ["visibility"], operator: MATCH, searchValue: "${OpportunityVisibility.PRIVATE}"}]
          }
        ) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);

    const privateCount = monumentsMenPrivateCount + starshipTroopersPrivateCount;
    expect(response.body.data.queryOpportunities.results).to.be.a('array');
    expect(response.body.data.queryOpportunities.results.length).equal(privateCount);

    // Should contain privates.
    expect(response.body.data.queryOpportunities.results.find(
      (opportunity: Opportunity) => opportunity.visibility.toLowerCase() === OpportunityVisibility.ALL.toLowerCase())).to.be.undefined;

    // Should only get results for monumentsMen and starshipTroopers
    const star = response.body.data.queryOpportunities.results.filter((opp: any) => opp.tenant.id === Id.simpleId(2, ID_PREFIX_0));
    const mon = response.body.data.queryOpportunities.results.filter((opp: any) => opp.tenant.id === Id.simpleId(1, ID_PREFIX_0));
    expect(star.length).equal(starshipTroopersPrivateCount);
    expect(mon.length).equal(monumentsMenPrivateCount);
    expect(star.length + mon.length).equal(privateCount);
  });

  it('query opportunities permission failure for monumentsMen user.', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${monumentsMenCuratorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT },
               { resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT }]
          }
        ) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(400);
    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.UNAUTHORIZED].code);
  });

  it('query opportunities fail permission on starship user', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${starshipTroopersCuratorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT },
               { resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT }]
          }
        ) {
          results {          
            id visibility title statement context benefits status initiatives endorsements
            tenant {
              id
            }
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(400);
    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.UNAUTHORIZED].code);
  });
});
