import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';
import { ID_PREFIX_0,  MATCH_SUBSTRING, OPS_PER_TENANT, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe(')))))))))))))) File Download Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, 0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should fail to download opportunities file', async () => {
    const response = await request.post('/ic/download/opp').expect(500);
  });

  it('download opportunities file (all ops)', async () => {
    const response = await request.post('/ic/download/opp').set('Authorization', `Bearer ${curatorToken}`).expect(200);
  });

  it('download opportunities file (match one)', async () => {
    const response = await request
      .post('/ic/download/opp')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [{ fieldNames: ['title'], operator: SearchOperator.MATCH, searchValue: MATCH_SUBSTRING }],
        },
      })
      .expect(200);
  });

  it('should fail to download projects file', async () => {
    const response = await request.post('/ic/download/project').expect(500);
  });

  it('download projects file (all)', async () => {
    const response = await request
      .post('/ic/download/project')
      .set('Authorization', `Bearer ${curatorToken}`)
      .expect(200);
  });

  it('download projects file (match one)', async () => {
    const response = await request
      .post('/ic/download/project')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [{ fieldNames: ['title'], operator: SearchOperator.MATCH, searchValue: MATCH_SUBSTRING }],
        },
      })
      .expect(200);
  });

  it('download project file (one project)', async () => {
    const response = await request
      .post(`/ic/download/project/${Id.simpleId(1, ID_PREFIX_0)}`)
      .set('Authorization', `Bearer ${curatorToken}`)
      .expect(200);
  });
});
