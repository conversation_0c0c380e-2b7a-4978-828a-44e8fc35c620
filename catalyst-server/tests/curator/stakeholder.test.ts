import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator Stakeholder Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get stakeholders alphabetical', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryStakeholders(
          pagingInput: { pageSize: 5, cursor: "5" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: true }]
          }
        ) {
          results {
           id name
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryStakeholders.results).to.be.a('array');
    expect(response.body.data.queryStakeholders.results.length).equal(5);
    expect(response.body.data.queryStakeholders.results[0].name).equal('stakeholder_6');
  });

  it('search stakeholders by prefix, sort alphabetical', async () => {
    // this should match stakeholder_1 and stakeholder_10
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryStakeholders(
          pagingInput: { pageSize: 10 },
          searchSortInput: {
            sortFields: [{ fieldName: "name", ascending: true }],
            searchFields: [{ fieldNames: ["name"], operator: MATCH, searchValue: "stakeholder_1" }]
          }
        ) {
          results {
           id name
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryStakeholders.results).to.be.a('array');
    expect(response.body.data.queryStakeholders.results.length).equal(2);
    expect(response.body.data.queryStakeholders.results[0].name).equal('stakeholder_1');
    expect(response.body.data.queryStakeholders.results[1].name).equal('stakeholder_10');
  });

  it('search stakeholders by prefix, sort alphabetical', async () => {
    // this should not match stakeholder_1 and stakeholder_10
    // stakeholder_10 has null org
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryStakeholders(
          pagingInput: { pageSize: 10 },
          searchSortInput: {
            sortFields: [{ fieldName: "name", ascending: true }],
            searchFields: [
              { fieldNames: ["name"], operator: MATCH, searchValue: "stakeholder_1" },
              { fieldNames: ["org"], operator: MATCH, searchValue: "stakeholder_org_1" },
            ]
          }
        ) {
          results {
           id name
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryStakeholders.results).to.be.a('array');
    expect(response.body.data.queryStakeholders.results.length).equal(1);
    expect(response.body.data.queryStakeholders.results[0].name).equal('stakeholder_1');
  });

  it('should get stakeholder by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getStakeholder(id: "${Id.simpleId(3, ID_PREFIX_0)}") {
            id name
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getStakeholder).to.be.a('object');
    expect(response.body.data.getStakeholder.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.getStakeholder.name).equal('stakeholder_3');
  });

  it('should get stakeholder by name and org', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getStakeholder(name: "stakeholder_5", org: "stakeholder_org_5") {
            id name org
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getStakeholder).to.be.a('object');
    expect(response.body.data.getStakeholder.id).equal(Id.simpleId(5, ID_PREFIX_0));
    expect(response.body.data.getStakeholder.name).equal('stakeholder_5');
    expect(response.body.data.getStakeholder.org).equal('stakeholder_org_5');
  });

  it('should get stakeholder by name and org w/ null org', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getStakeholder(name: "stakeholder_10") {
            id name org
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getStakeholder).to.be.a('object');
    expect(response.body.data.getStakeholder.id).equal(Id.simpleId(10, ID_PREFIX_0));
    expect(response.body.data.getStakeholder.name).equal('stakeholder_10');
    expect(response.body.data.getStakeholder.org).equal(null);
  });

  it('should create stakeholder', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              name: "test_stakeholder"
              org: "test_stakeholder_org"
            },
          ){
            id name org
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('test_stakeholder');
    expect(response.body.data.createStakeholder.org).equal('test_stakeholder_org');
  });

  it('should return existing stakeholder if already exists (not case sensitive)', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              name: "StakEhoLdeR_1"
              org: "StakEhoLdeR_org_1"
            },
          ){
            id name org
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('stakeholder_1');
    expect(response.body.data.createStakeholder.org).equal('stakeholder_org_1');
  });

  it('should return existing stakeholder if already exists (no org) (not case sensitive)', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              name: "StakEhoLdeR_10"
            },
          ){
            id name org
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('stakeholder_10');
    expect(response.body.data.createStakeholder.org).null;
  });

  it('should succeed creating stakeholder with duplicate name but not org', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              name: "stakeholder_1"
            },
          ){
            id name org
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('stakeholder_1');
    expect(response.body.data.createStakeholder.org).equal(null);
  });

  it('should update stakeholder', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateStakeholder (input: {
          name: "renamed_stakeholder_3",
          org: "renamed_stakeholder_3_org",
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id name org
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateStakeholder).to.be.a('object');
    expect(response.body.data.updateStakeholder.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateStakeholder.name).equal('renamed_stakeholder_3');
    expect(response.body.data.updateStakeholder.org).equal('renamed_stakeholder_3_org');
  });

  it('should not delete stakeholder that is still referenced by a projectStakeholder', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteStakeholder (id: "${Id.simpleId(3, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteStakeholder).to.be.false;
  });
});
