import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let adminToken: string;

describe('))))))))))))) Curator TenantAlias Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    adminToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(4, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.ADMIN]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get tenantAliases alphabetical', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `query {
        queryTenantAliases(
          pagingInput: { pageSize: 3, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "handle", ascending: true }]
          }
        ) {
          results {
           id name handle
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryTenantAliases.results).to.be.a('array');
    expect(response.body.data.queryTenantAliases.results.length).equal(3);
    expect(response.body.data.queryTenantAliases.results[0].handle).equal('0_first_alphabetical');
  });

  it('search tenantAliases by prefix, sort alphabetical', async () => {
    // this should match tenantAlias_1 and tenantAlias_10
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `query {
        queryTenantAliases(
          pagingInput: { pageSize: 10 },
          searchSortInput: {
            sortFields: [{ fieldName: "handle", ascending: true }],
            searchFields: [{ fieldNames: ["handle"], operator: MATCH, searchValue: "^3_tenantAlias" }]
          }
        ) {
          results {
           id name handle
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryTenantAliases.results).to.be.a('array');
    expect(response.body.data.queryTenantAliases.results.length).equal(1);
    expect(response.body.data.queryTenantAliases.results[0].handle).equal('3_tenantAlias');
  });

  it('should get tenantAlias by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `query {
          getTenantAlias(id: "${Id.simpleId(3, ID_PREFIX_0)}") {
            id name handle
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenantAlias).to.be.a('object');
    expect(response.body.data.getTenantAlias.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.getTenantAlias.handle).equal('3_tenantAlias');
  });

  it('should get tenantAlias by handle', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `query {
          getTenantAlias(handle: "2_tenantAlias") {
            id name handle
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenantAlias).to.be.a('object');
    expect(response.body.data.getTenantAlias.id).equal(Id.simpleId(2, ID_PREFIX_0));
    expect(response.body.data.getTenantAlias.handle).equal('2_tenantAlias');
  });

  it('should create tenantAlias', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          createTenantAlias (
            input: {
              handle: "test_tenantAlias"
            },
          ){
            id name handle
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createTenantAlias).to.be.a('object');
    expect(response.body.data.createTenantAlias.id).to.not.be.null;
    expect(response.body.data.createTenantAlias.handle).equal('test_tenantAlias');
  });

  it('should fail to create tenantAlias duplicate (case insensitive)', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          createTenantAlias (
            input: {
              handle: "1_tenantAlias"
            },
          ){
            id name handle
          }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.OBJECT_ALREADY_EXISTS].code);
  });

  it('should update tenantAlias', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
        updateTenantAlias (input: {
          handle: "renamed_tenantAlias_3",
          config: { applicationTitle: "Test" }
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id name handle meta {
            config theme
          }
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateTenantAlias).to.be.a('object');
    expect(response.body.data.updateTenantAlias.handle).equal('renamed_tenantAlias_3');
    expect(response.body.data.updateTenantAlias.meta.config.applicationTitle).equal('Test');
    expect(response.body.data.updateTenantAlias.id).equal(Id.simpleId(3, ID_PREFIX_0));
  });

  it('should delete tenantAlias', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        query: `mutation {
          deleteTenantAlias (id: "${Id.simpleId(3, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteTenantAlias).to.be.true;
  });
});
