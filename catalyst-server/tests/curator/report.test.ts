import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { CAMPAIGNS, ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let analystToken: string;

describe(')))))))))))))) Curator Report Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    analystToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(5, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR], roleMap[RoleNames.ANALYST]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get distinct campaign values for Opportunity', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        calculation(
          entityName: "Opportunity",
          calculation: {
            distinct: true,
            operations: [{ fieldName: "campaign"}]
          },
          searchSortInput: {
            searchFields: [{ fieldNames: ["status"], operator: IN, searchValue: ["Approved", "Archived", "Pending", "Deleted"] }]
          }
        ) {
          operationResults {
            result
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.calculation.operationResults).to.be.a('array');
    expect(response.body.data.calculation.operationResults.length).equal(1);
    expect(response.body.data.calculation.operationResults[0].result).to.be.a('array');
    expect(response.body.data.calculation.operationResults[0].result.length).equal(7);
    expect(response.body.data.calculation.operationResults[0].result).contains(CAMPAIGNS[1]);
  });

  it('get Opportunity status report', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [{
              reportName: "oppStatus", label: "oppStatus1"
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }
                ]
              }
            }]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(1);
    expect(response.body.data.report.reports[0].name).equal('oppStatus');
    expect(response.body.data.report.reports[0].label).equal('oppStatus1');
    expect(response.body.data.report.reports[0].data[0].tenant).equal('Starship Troopers');
    expect(response.body.data.report.reports[0].data[0].approvedCount).equals(6);
    expect(response.body.data.report.reports[0].data[0].pendingCount).equals(6);
    expect(response.body.data.report.reports[0].data[0].archivedCount).equals(7);
    expect(response.body.data.report.reports[0].data.find((r: any) => r.tenant === 'Total').approvedCount).equals(13);
  });

  it('get Opportunity priority report', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [{
              reportName: "oppPriority"
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }
                ]
              }
            }]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(1);
    expect(response.body.data.report.reports[0].name).equal('oppPriority');
    expect(response.body.data.report.reports[0].label).equal('oppPriority');
    expect(response.body.data.report.reports[0].data[0].tenant).equal('Starship Troopers');
    expect(response.body.data.report.reports[0].data[0].highPriorityCount).equals(5);
    expect(response.body.data.report.reports[0].data[0].mediumPriorityCount).equals(5);
    expect(response.body.data.report.reports[0].data[0].lowPriorityCount).equals(5);
    expect(response.body.data.report.reports[0].data[0].noPriorityCount).equals(4);
    expect(response.body.data.report.reports[0].data.find((r: any) => r.tenant === 'Total').highPriorityCount).equals(
      9,
    );
  });

  it('get Opportunity WFF report', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [{
              reportName: "oppWFF",
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }
                ]
              }
            }]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(1);
    expect(response.body.data.report.reports[0].name).equal('oppWFF');
    expect(response.body.data.report.reports[0].label).equal('oppWFF');
    expect(response.body.data.report.reports[0].data[0].tenant).equal('Starship Troopers');
    expect(response.body.data.report.reports[0].data[0].missionCommandCount).equals(4);
    expect(response.body.data.report.reports[0].data[0].movementManeuverCount).equals(3);
    expect(response.body.data.report.reports[0].data[0].intelligenceCount).equals(4);
    expect(response.body.data.report.reports[0].data[0].firesCount).equals(2);
    expect(response.body.data.report.reports[0].data[0].sustainmentCount).equals(4);
    expect(response.body.data.report.reports[0].data[0].forceProtectionCount).equals(2);
    expect(response.body.data.report.reports[0].data[0].noneCount).equals(0);
    expect(response.body.data.report.reports[0].data.find((r: any) => r.tenant === 'Total').missionCommandCount).equals(
      9,
    );
  });

  it('get Opportunity campaign report', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [{
              reportName: "oppCampaign", label: "oppCampaign1"
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }
                ]
              }
            }]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(1);
    expect(response.body.data.report.reports[0].name).equal('oppCampaign');
    expect(response.body.data.report.reports[0].label).equal('oppCampaign1');
    expect(response.body.data.report.reports[0].data.tenantCampaignResults[0].campaign).equal('95th Academy Awards');
    expect(response.body.data.report.reports[0].data.tenantCampaignResults[0].campaignCount).equals(3);
    expect(response.body.data.report.reports[0].data.totals[0].campaign).equal('95th Academy Awards');
    expect(response.body.data.report.reports[0].data.totals[0].campaignCount).equals(5);
  });

  it('get Opportunity submission report', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [{
              reportName: "oppSubmission", label: "oppSubmission1"
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }
                ]
              }
            }]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(1);
    expect(response.body.data.report.reports[0].name).equal('oppSubmission');
    expect(response.body.data.report.reports[0].label).equal('oppSubmission1');
    expect(response.body.data.report.reports[0].data.totalSubmissions).equals(50);
    expect(response.body.data.report.reports[0].data.totalSubmitters).equals(10);
    expect(response.body.data.report.reports[0].data.uncuratedOpportunities).equals(50);
  });

  it('get Opportunity tenant submission report', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [{
              reportName: "oppTenantSubmission", label: "oppTenantSubmission1"
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }
                ]
              }
            }]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(1);
    expect(response.body.data.report.reports[0].name).equal('oppTenantSubmission');
    expect(response.body.data.report.reports[0].label).equal('oppTenantSubmission1');
  });

  it('get Opportunity tenant submission by year report', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [{
              reportName: "oppTenantSubmissionByYear", label: "oppTenantSubmissionByYear1"
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["createdAt"], operator: GTE, searchValue: "2021-10-10T00:00:00.000Z" }
                ]
              }
            }]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(1);
    expect(response.body.data.report.reports[0].name).equal('oppTenantSubmissionByYear');
    expect(response.body.data.report.reports[0].label).equal('oppTenantSubmissionByYear1');
  });


  it('get all reports', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        report(
          reportInput: {
            queries: [
              { reportName: "oppStatus" },
              { reportName: "oppPriority" },
              { reportName: "oppWFF" },
              { reportName: "oppCampaign" },
              { reportName: "oppSubmission" },
              { reportName: "oppTenantSubmission" }
            ]
          },
          scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          reports {
            name
            label
            data
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.report.reports).to.be.a('array');
    expect(response.body.data.report.reports.length).equal(6);
    expect(response.body.data.report.reports.map((r: any) => r.name)).contains('oppStatus');
    expect(response.body.data.report.reports.map((r: any) => r.name)).contains('oppPriority');
    expect(response.body.data.report.reports.map((r: any) => r.name)).contains('oppWFF');
    expect(response.body.data.report.reports.map((r: any) => r.name)).contains('oppCampaign');
    expect(response.body.data.report.reports.map((r: any) => r.name)).contains('oppSubmission');
    expect(response.body.data.report.reports.map((r: any) => r.name)).contains('oppTenantSubmission');
  });
});
