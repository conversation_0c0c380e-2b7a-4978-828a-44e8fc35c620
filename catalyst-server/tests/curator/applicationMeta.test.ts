import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { ApplicationMeta } from 'core/entities/ApplicationMeta';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator ApplicationMeta Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
  });

  beforeEach(async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  it('should get applicationMeta by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getApplicationMeta(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id curationMeta
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getApplicationMeta).to.be.a('object');
    expect(response.body.data.getApplicationMeta.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getApplicationMeta.curationMeta).to.be.a('object');
  });

  it('should create applicationMeta', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createApplicationMeta (
            input: {
              curationMeta: {
                test: ["item1"]
              } 
            },
          ){
            id curationMeta
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createApplicationMeta).to.be.a('object');
    expect(response.body.data.createApplicationMeta.id).to.not.be.null;
    expect(response.body.data.createApplicationMeta.curationMeta?.test?.[0]).equal('item1');
  });

  it('should update applicationMeta', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateApplicationMeta (input: {
              curationMeta: {
                test: ["renameditem1"]
              } 
        }, id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id curationMeta
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateApplicationMeta).to.be.a('object');
    expect(response.body.data.updateApplicationMeta.id).to.not.be.null;
    expect(response.body.data.updateApplicationMeta.curationMeta?.test?.[0]).equal('renameditem1');
  });

  it('should delete applicationMeta', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteApplicationMeta (id: "${Id.simpleId(1, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteApplicationMeta).to.be.true;
  });
});
