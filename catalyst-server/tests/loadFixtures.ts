import * as fs from 'fs';
import { execSync } from 'child_process';
import { Readable } from 'stream';
import { Connection, EntityManager, IDatabaseDriver, MikroORM, t } from '@mikro-orm/core';
import { CurationEventType } from 'core/contracts/enums/CurationEventType';
import { EntityType } from 'core/contracts/enums/EntityType';
import { ProjectStakeholderType } from 'core/contracts/enums/ProjectStakeholderType';
import { ProjectStatus } from 'core/contracts/enums/ProjectStatus';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { VerifiedStatus } from 'core/contracts/enums/VerifiedStatus';
import { CreateSubmissionInput } from 'core/contracts/input/user/SubmissionInput';
import { AttachmentController } from 'core/controllers/curator/AttachmentController';
import { CurationEventController } from 'core/controllers/curator/CurationEventController';
import { Category } from 'core/entities/Category';
import { Opportunity } from 'core/entities/Opportunity';
import { Project } from 'core/entities/Project';
import { ProjectStakeholder } from 'core/entities/ProjectStakeholder';
import { Role } from 'core/entities/Role';
import { Stakeholder } from 'core/entities/Stakeholder';
import { Submission } from 'core/entities/Submission';
import { Tenant } from 'core/entities/Tenant';
import { TenantAlias } from 'core/entities/TenantAlias';
import { User } from 'core/entities/User';
import { company, date, internet, lorem, name, phone } from 'faker';
import { Id } from 'core/utils/Id';
import { Attachment } from 'core/entities/Attachment';
import monumentsMenConfig from '../config/tenants/monumentsMen/config.json';
import monumentsMenTheme from '../config/tenants/monumentsMen/theme.json';
import serverConfig from '../config/tenants/monumentsMen/serverConfig.json';
import _82ndairborneConfig from '../config/tenants/82ndairborne/config.json';
import _82ndairborneTheme from '../config/tenants/82ndairborne/theme.json';
import _testConfig from '../config/tenants/test/config.json';
import _testTheme from '../config/tenants/test/theme.json';
import _content from '../config/content/content.json';
import { TenantMeta } from 'core/entities/TenantMeta';
import { OpportunityStatus } from 'core/contracts/enums/OpportunityStatus';
import { Series } from 'core/utils/Async';
import { ApplicationMeta } from 'core/entities/ApplicationMeta';
import { RelatedOpportunity } from 'core/entities/RelatedOpportunity';
import { RelatedOpportunityType } from 'core/contracts/enums/RelatedOpportunityType';
import { Privilege } from 'core/entities/Privilege';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { OpportunityVisibility } from 'core/contracts/enums/OpportunityVisibility';
import { createCleanLocalDbDump } from 'core/utils/databaseUtils';
import { Owner } from 'core/entities/Owner';
import { OpportunityOwner } from 'core/entities/OpportunityOwner';

export const OPS_PER_TENANT = 25;
export const PROJS_PER_TENANT = 20;
export const CATEGORIES_PER_TENANT = 10;
export const STAKEHOLDERS_PER_TENANT = 10;
export const USERS_PER_TENANT = 5;
export const OPS_PER_USER = OPS_PER_TENANT / USERS_PER_TENANT;
export const TENANT_ALIASES = [
  '0_first_alphabetical',
  '1_tenantAlias',
  '2_tenantAlias',
  '3_tenantAlias',
  '4_tenantAlias',
  '5_tenantAlias',
  'test',

  // these additional aliases are for testing and theming convenience (add (or remove) aliases here as needed)
  // these will collide with production tenants however, so they should be used with bootstrap data only
  /*
  '82ndairborne',
  '101stairborne',
  'pathfinder',
  'intelligencewff',
  'marne',
  '3rdesc',
  '1sfc',
  'iiiarmoredcorps',
  '7tbx',
  '25id',
  '18fab',
  'centcom',
  'usasoac',
  */
];
export const ATTACHMENT_FILE_NAME = 'test.png';
export const NUM_ATTACHMENTS = 3;
// monumentsMen
export const ID_PREFIX_0 = 0;
// starshipTroopers
export const ID_PREFIX_1 = 1;
export const ID_PREFIX_2 = 2;
export const ID_PREFIX_3 = 3;

export const USER_SUBSTRING = ' PhD, SoB, BmF';
export const MATCH_SUBSTRING = '. Additionally, not all of those who wander are lost';
export const CAMPAIGNS = [
  'Dirt Days 2023',
  'Bonaroo 2023',
  'Southeast Fishing Championship',
  'Burning Man Festival',
  '95th Academy Awards',
  'Annual Dental Checkup',
  'SxSW 2024',
];
export const ARMY_MODERNIZATION_PRIORITY = [
  'Air and Missile Defense (AMD)',
  'All-Domain Sensing (ADS)',
  'Command and Control (C2)',
  'Contested Logistics (CL)',
  'Future Vertical Lift (FVL)',
  'Long Range Precision Fires (LRPF)',
  'Next Generation Combat Vehicles (NGCV)',
  'Soldier Lethality (SL)',
  'Synthetic Training Environment (STE)',
];
export const FUNCTIONS = [
  'Mission Command',
  'Movement and Maneuver',
  'Intelligence',
  'Fires',
  'Sustainment',
  'Force Protection',
];

const CAPABILITY_AREAS = [
  'Aircraft',
  'Air Defense',
  'AR/VR',
  'Artificial Intelligence (AI)',
  'Automation (Process)',
  'Ammunition (Munitions)',
  'Command and Control (C2)',
  'Communications',
  'Counter-Mobility',
  'Cover and Concealment',
  'Data Analytics and Visualization',
  'Direct Fires',
  'Doctrine, Policy, and Organization',
  'Electronic Warfare (EW)',
  'Energy and Power',
  'Facilities',
  'Ground Vehicles',
  'Human Performance',
  'Fitness / Wellness',
  'Indirect Fires',
  'ISR',
  'Logistics',
  'Low-Cost Fabrication',
  'Maintenance',
  'Medical',
  'Mobility',
  'Network',
  'Nett Warrior',
  'Other',
  'Personnel (Retention)',
  'Personnel (Protection and Survivability)',
  'Reliability',
  'Robotic and Autonomous Systems Common Control',
  'Sensors',
  'Soldier Worn / Carried',
  'Soldier Load Reduction',
  'Soldier Common CsUAS',
  'Soldier and Small Unit Power (S2UP)',
  'Sustainment',
  'Training',
  'Tactical Assault Kit (TAK)',
  'Unmanned Systems (UxS)',
  'Aerial/Aircraft Vehicles - UAVs',
  'Combat Air System - UCAS',
  'Ground Vehicles - UGVs',
  'Mobile Vehicles - UMVs',
  'Surface Vehicles - USVs',
  'Undersea Vehicles - UUVs',
  'Underwater Vehicles - UWVs',
];

const OPERATIONAL_RULES = [
  'Infantry',
  'Armor',
  'Field Artillery',
  'Air Defense Artillery',
  'Aviation',
  'Special Ops',
  'Military Intelligence',
  'Signal Corps',
  'Chemical Corps',
  'Military Police',
  'Engineers',
  'Logistics Corps',
  'Transportation Corps',
  'Medical Corps',
  'Ordnance Corps',
  'Quartermaster Corps',
  'Finance Corps',
  "Adjutant General's Corps",
];

const TRANSITION_IN_CONTACT = [
  'Lethality',
  'Protection and Survivability - Soldier Common CsUAS',
  'Situational Awareness - Tactical Assault Kit (TAK)',
  'Mobility',
  'Human Performance',
  'Soldier and Small Unit Power (S2UP)',
  'Communications',
  'Accelerated Robotic and Autonomous Systems Common Control',
  'Sustainment',
];

const ECHELON_APPLICABILITY = [
  'Army',
  'Corps',
  'Division',
  'Brigade / Regiment',
  'Battalion',
  'Company / Battery / Troop',
  'Platoon',
];
export function restoreDbFromLocalBackup() {
  const SCRIPTPATH = `${process.cwd()}/tests`;
  const localDumpFile = `${SCRIPTPATH}/local.sql`;
  if (!fs.existsSync(localDumpFile)) {
    console.log('Creating data dump of local db');
    createCleanLocalDbDump({ outputPath: SCRIPTPATH });
  }
  execSync(`PGPASSWORD="dev" psql -h localhost -U dev -d catalyst-db -f ${localDumpFile}`);
}

function getRandomBoolean() {
  return Math.random() < 0.5;
}

const prodFixtures = async (em: EntityManager<IDatabaseDriver<Connection>>): Promise<{ roles: Role[] }> => {
  // create default tenant
  const tenant = Tenant.newTenant({ name: 'Default', handle: 'default', label: 'Default' });
  await em.persist(tenant);

  // create roles
  const roles = await Promise.all(
    Object.keys(RoleNames).map(async (roleNameKey) => {
      const role = Role.newRole({ name: RoleNames[<never>roleNameKey] });
      await em.persist(role);
      return role;
    }),
  );

  const adminRole = roles.find((role) => role.name === RoleNames.ADMIN);
  if (!adminRole) throw Error(`Role 'admin' not found in database`);

  const curatorRole = roles.find((role) => role.name === RoleNames.CURATOR);
  if (!curatorRole) throw Error(`Role 'curator' not found in database`);

  const analystRole = roles.find((role) => role.name === RoleNames.ANALYST);
  if (!analystRole) throw Error(`Role 'analyst' not found in database`);

  const admin = User.newUser({
    emailAddress: 'admin@default',
    password: 'admin@default',
    firstName: '',
    lastName: '',
    org1: '',
    org2: '',
    phone: '',
    status: VerifiedStatus.VERIFIED,
  });
  admin.tenant = tenant;
  admin.roles.add(adminRole, curatorRole, analystRole);
  await em.persist(admin);

  return { roles };
};

export const loadProdFixtures = async (em: EntityManager<IDatabaseDriver<Connection>>): Promise<{ roles: Role[] }> => {
  try {
    const result = await prodFixtures(em);
    await em.flush();
    return result;
  } catch (error) {
    console.error('📌 Could not load prod fixtures', error);
    throw error;
  }
};

export const loadDevFixtures = async (
  orm: MikroORM<IDatabaseDriver<Connection>>,
  type: 'test' | 'all' = 'test',
  operation: 'clear' | 'update' = 'clear',
): Promise<void> => {
  const em = orm.em.fork();
  const bootstrapTenant = async ({
    tenant,
    roles,
    tenantIndex,
    basicPrivilegeGroup,
    analystPrivilegeGroup,
    opsPerTenant,
  }: {
    tenant: Tenant;
    roles: Role[];
    tenantIndex: number;
    basicPrivilegeGroup?: PrivilegeGroup;
    analystPrivilegeGroup?: PrivilegeGroup;
    opsPerTenant?: number;
  }) => {
    const adminRole = roles.find((role) => role.name === RoleNames.ADMIN);
    if (!adminRole) throw Error(`Role 'admin' not found in database`);

    const curatorRole = roles.find((role) => role.name === RoleNames.CURATOR);
    if (!curatorRole) throw Error(`Role 'curator' not found in database`);

    const analystRole = roles.find((role) => role.name === RoleNames.ANALYST);
    if (!analystRole) throw Error(`Role 'analyst' not found in database`);

    const USER_SET_SIZE = 5;

    const emailAddresses = [...Array(Math.round(USERS_PER_TENANT / USER_SET_SIZE))].reduce(
      (addresses, _, userIndex) => {
        const num = userIndex || '';
        addresses.push(
          `unverified.user${num}@user.com`,
          `verified.user${num}@user.com`,
          `curator.user${num}@user.com`,
          `admin.user${num}@user.com`,
          `analyst.user${num}@user.com`,
        );
        return addresses;
      },
      [],
    );
    const passwords = ['', 'pass1', 'passcurator', 'passadmin', 'passanalyst'];
    const status = [
      VerifiedStatus.UNVERIFIED,
      VerifiedStatus.VERIFIED,
      VerifiedStatus.VERIFIED,
      VerifiedStatus.VERIFIED,
      VerifiedStatus.VERIFIED,
    ];
    const rolesSets = [[], [], [curatorRole], [adminRole, curatorRole, analystRole], [curatorRole, analystRole]];

    const applicationMeta = await Promise.all(
      [...Array(1)].map(async (_, metaIndex) => {
        const applicationMeta = ApplicationMeta.newApplicationMeta({
          curationMeta: {},
        });
        applicationMeta.id = Id.simpleId(metaIndex + 1, tenantIndex);
        applicationMeta.tenant = tenant;

        await em.persist(applicationMeta);
        return applicationMeta;
      }),
    );

    const users = await Promise.all(
      [...Array(USERS_PER_TENANT)].map(async (_, userIndex) => {
        const user = User.newUser({
          emailAddress: emailAddresses[userIndex],
          password: passwords[userIndex % USER_SET_SIZE],
          firstName: name.firstName(),
          lastName: name.lastName() + (userIndex % 2 ? USER_SUBSTRING : ''),
          org1: company.bsBuzz(),
          org2: company.bsBuzz(),
          org3: company.bsBuzz(),
          org4: company.bsBuzz(),
          altContact: internet.email(),
          phone: phone.phoneNumber('###-###-####'),
          status: status[userIndex % USER_SET_SIZE],
          options: { cookieAcceptance: true },
        });
        user.roles.set(rolesSets[userIndex % USER_SET_SIZE]);
        user.tenant = tenant;
        user.id = Id.simpleId(userIndex + 1, tenantIndex);
        if (userIndex === 0) user.appMeta = applicationMeta[userIndex];
        if (user.roles.contains(analystRole) && analystPrivilegeGroup) {
          user.privilegeGroups.add(analystPrivilegeGroup);
        } else {
          if (basicPrivilegeGroup) user.privilegeGroups.add(basicPrivilegeGroup);
        }

        await em.persist(user);
        return user;
      }),
    );

    const categories = await Promise.all(
      [...Array(CATEGORIES_PER_TENANT)].map(async (_, catIndex) => {
        const category = Category.newCategory({
          name: `category_${catIndex + 1}`,
        });
        category.id = Id.simpleId(catIndex + 1, tenantIndex);
        category.tenant = tenant;

        await em.persist(category);
        return category;
      }),
    );

    const stakeholders = await Promise.all(
      [...Array(STAKEHOLDERS_PER_TENANT)].map(async (_, index) => {
        const stakeholder = Stakeholder.newStakeholder({
          // add undefined values for last 2 items for testing
          name: index !== STAKEHOLDERS_PER_TENANT - 2 ? `stakeholder_${index + 1}` : undefined,
          org: index !== STAKEHOLDERS_PER_TENANT - 1 ? `stakeholder_org_${index + 1}` : undefined,
        });
        stakeholder.id = Id.simpleId(index + 1, tenantIndex);
        stakeholder.tenant = tenant;

        await em.persist(stakeholder);
        return stakeholder;
      }),
    );

    let rotateIndexCounter = -1;
    const opportunities = await Promise.all(
      [...Array(opsPerTenant || OPS_PER_TENANT)].map(async (_, oppIndex) => {
        //split between the users
        const user = users[oppIndex % users.length];

        const input: CreateSubmissionInput = {
          title: company.catchPhrase() + (oppIndex % 7 ? '' : MATCH_SUBSTRING),
          statement: lorem.sentences(3) + (oppIndex % 14 ? '' : MATCH_SUBSTRING),
          context: lorem.sentences(10) + (oppIndex % 3 ? '' : MATCH_SUBSTRING),
          benefits: lorem.sentences(10),
          solutionConcepts: lorem.sentences(8),
          campaign: CAMPAIGNS[(oppIndex + tenantIndex) % CAMPAIGNS.length],
          function: FUNCTIONS[(oppIndex + tenantIndex) % FUNCTIONS.length],
        };
        const submission = Submission.newSubmission(input, tenant);
        const fromDate = new Date();
        fromDate.setFullYear(fromDate.getFullYear() - 1);
        submission.createdAt = getRandomDate(fromDate, new Date());
        const opportunity = Opportunity.newOpportunity(
          {
            ...input,
            org1: user.org1,
            org2: user.org2,
            org3: user.org3,
            org4: user.org4,
          },
          tenant,
        );
        submission.id = Id.simpleId(oppIndex + 1, tenantIndex);
        opportunity.id = Id.simpleId(oppIndex + 1, tenantIndex);
        submission.user = user;
        opportunity.user = user;
        opportunity.solutionPathway = company.catchPhraseNoun();
        opportunity.solutionPathwayDetails = lorem.sentences(3);
        opportunity.initiatives = company.catchPhraseNoun();
        opportunity.endorsements = `${name.firstName()} ${name.lastName()}`;
        opportunity.statusNotes = lorem.sentences(4);
        opportunity.priorityNotes = lorem.sentences(4);
        opportunity.additionalNotes = lorem.sentences(4);
        opportunity.attachmentNotes = lorem.sentence();
        opportunity.armyModernizationPriority =
          ARMY_MODERNIZATION_PRIORITY[(oppIndex + tenantIndex) % ARMY_MODERNIZATION_PRIORITY.length];
        const isTiCLOE = getRandomBoolean();
        opportunity.isTiCLOE = isTiCLOE;
        if (isTiCLOE) {
          opportunity.transitionInContactLineOfEffort =
            TRANSITION_IN_CONTACT[(oppIndex + tenantIndex) % TRANSITION_IN_CONTACT.length];
          opportunity.operationalRules = OPERATIONAL_RULES[(oppIndex + tenantIndex) % OPERATIONAL_RULES.length];
          opportunity.capabilityArea = CAPABILITY_AREAS[(oppIndex + tenantIndex) % CAPABILITY_AREAS.length];
        }

        opportunity.echelonApplicability =
          ECHELON_APPLICABILITY[(oppIndex + tenantIndex) % ECHELON_APPLICABILITY.length];
        const statusTypes = [
          OpportunityStatus.APPROVED,
          OpportunityStatus.ARCHIVED,
          OpportunityStatus.PENDING,
          OpportunityStatus.DELETED,
        ];
        opportunity.status = statusTypes[(oppIndex + tenantIndex) % statusTypes.length];
        // We are using rotateIndexCounter to offset priorities rotation for a good mix of status and priority
        if (!(oppIndex % 4)) rotateIndexCounter++;
        opportunity.priority = (oppIndex + rotateIndexCounter + tenantIndex) % 4;
        // set every 5th opportunity visibility status to PRIVATE
        opportunity.visibility = (oppIndex + 1) % 5 ? OpportunityVisibility.ALL : OpportunityVisibility.PRIVATE;

        opportunity.categories.set([categories[oppIndex % CATEGORIES_PER_TENANT]]);
        opportunity.stakeholders.set([stakeholders[oppIndex % STAKEHOLDERS_PER_TENANT]]);

        await em.persist(opportunity);
        opportunity.submissions.add(submission);
        await em.persist(submission);
        // only add CREATE events to first 2 opps for testing
        // this means that a Curator created this opportunity directly, otherwise there should be no curation events
        if (oppIndex < 3) {
          const curationEvent = await CurationEventController.getCurationEvent(
            em,
            user,
            EntityType.OPPORTUNITY,
            opportunity.id,
            CurationEventType.CREATE,
          );

          await em.persist(curationEvent);
        }
        return opportunity;
      }),
    );

    // related opportunities
    opportunities[0].opportunities.set([opportunities[1], opportunities[2]]);
    opportunities[1].opportunities.set([opportunities[0], opportunities[2]]);
    opportunities[2].opportunities.set([opportunities[0], opportunities[1]]);
    await em.persist([opportunities[0], opportunities[1], opportunities[2]]);

    // add related opportunities
    opportunities.slice(1, 4).map((opportunity, opportunity_index) => {
      const relatedOpportunity = RelatedOpportunity.newRelatedOpportunity({
        type: RelatedOpportunityType.CHILD,
        orderBy: opportunity_index,
      });
      relatedOpportunity.target = opportunity;
      opportunities[0].ownedOpportunities.add(relatedOpportunity);
    });
    opportunities.slice(4, 6).map((opportunity, opportunity_index) => {
      const relatedOpportunity = RelatedOpportunity.newRelatedOpportunity({
        type: RelatedOpportunityType.LINKED,
        orderBy: opportunity_index,
      });
      relatedOpportunity.target = opportunity;
      opportunities[0].ownedOpportunities.add(relatedOpportunity);
    });
    const relatedOpportunity = RelatedOpportunity.newRelatedOpportunity({
      type: RelatedOpportunityType.LINKED,
      orderBy: 0,
    });
    relatedOpportunity.target = opportunities[0];
    opportunities[1].ownedOpportunities.add(relatedOpportunity);

    await em.persist(opportunities[0]);

    // create some attachments for opp
    const attachments = await Series.map([...Array(NUM_ATTACHMENTS)], (_, index) =>
      createAttachment(Id.simpleId(1, tenantIndex), tenant.id, Id.simpleId(index, tenantIndex)),
    );

    const projects = await Promise.all(
      [...Array(PROJS_PER_TENANT)].map(async (_, projIndex) => {
        //split between the users
        const statusTypes = [
          ProjectStatus.PENDING,
          ProjectStatus.ACTIVE,
          ProjectStatus.COMPLETED,
          ProjectStatus.ARCHIVED,
          ProjectStatus.DELETED,
        ];
        const types = ['Pathfinder Seed', 'Pathfinder Pilot', 'Pathfinder Makerspace', 'Other'];
        const type = types[projIndex % types.length];
        const otherType = type === 'Other' ? name.jobArea() : undefined;
        const project = Project.newProject(
          {
            id: Id.simpleId(projIndex + 1, tenantIndex),
            status: statusTypes[projIndex % statusTypes.length],
            title: company.bs(),
            background: lorem.sentences(5) + (projIndex % 5 ? '' : MATCH_SUBSTRING),
            goals: lorem.sentences(4) + (projIndex % 10 ? '' : MATCH_SUBSTRING),
            startDate: date.soon(7),
            endDate: date.soon(30),
            statusNotes: company.catchPhrase(),
            otherType,
            type,
          },
          tenant,
        );
        project.creator = users[projIndex % users.length];
        project.opportunities.add(opportunities[projIndex % opportunities.length]);
        project.categories.set([categories[projIndex % CATEGORIES_PER_TENANT]]);
        const projectStakeholderTypes = [
          ProjectStakeholderType.DIVISION,
          ProjectStakeholderType.PERFORMER,
          ProjectStakeholderType.TRANSITION,
        ];
        // add many project stakeholders to the last project for testing
        if (projIndex === PROJS_PER_TENANT - 1) {
          stakeholders.map((stakeholder, stakeholder_index) => {
            const projectStakeholder = ProjectStakeholder.newProjectStakeholder({
              type: projectStakeholderTypes[projIndex % projectStakeholderTypes.length],
              orderBy: stakeholder_index,
            });
            projectStakeholder.stakeholder = stakeholder;
            project.projectStakeholders.add(projectStakeholder);
          });
          //otherwise, just add 1
        } else {
          const projectStakeholder = ProjectStakeholder.newProjectStakeholder({
            type: projectStakeholderTypes[projIndex % projectStakeholderTypes.length],
            orderBy: 0,
          });
          projectStakeholder.stakeholder = stakeholders[projIndex % STAKEHOLDERS_PER_TENANT];
          project.projectStakeholders.add(projectStakeholder);
        }

        await em.persist(project);

        const curationEvent = await CurationEventController.getCurationEvent(
          em,
          project.creator,
          EntityType.PROJECT,
          project.id,
          CurationEventType.CREATE,
        );
        await em.persist(curationEvent);

        return project;
      }),
    );

    return { users, stakeholders, categories, opportunities, projects };
  };

  /*
    We using tenantIds as resourceIds right now, as we transition over to privilege groups
  */
  const createPrivilegeGroup = async (
    em: EntityManager<IDatabaseDriver<Connection>>,
    forTenant: Tenant,
    groupName: string,
    tenants: Tenant[],
    groupId: string,
  ) => {
    const privilegeGroup = PrivilegeGroup.newPrivilegeGroup({ name: groupName }, forTenant);
    for (const tenant of tenants) {
      const privilege = Privilege.newPrivilege({
        name: `${tenant.label}`,
        resourceId: tenant.id,
        resourceType: ResourceType.TENANT,
      });
      privilegeGroup.privileges.add(privilege);
    }
    privilegeGroup.id = groupId;
    await em.persist(privilegeGroup);
    return privilegeGroup;
  };

  const getRoles = async (em: EntityManager<IDatabaseDriver<Connection>>): Promise<Role[]> => {
    const roles: Role[] = await em.getRepository(Role).findAll();

    const adminRole = roles.find((role) => role.name === RoleNames.ADMIN);
    if (!adminRole) throw Error(`Role 'admin' not found in database`);

    const curatorRole = roles.find((role) => role.name === RoleNames.CURATOR);
    if (!curatorRole) throw Error(`Role 'curator' not found in database`);

    const analystRole = roles.find((role) => role.name === RoleNames.ANALYST);
    if (!analystRole) throw Error(`Role 'analyst' not found in database`);

    return roles;
  };

  const createAttachment = (opportunityId: string, tenantId: string, id: string): Promise<Attachment> => {
    return AttachmentController.addAttachment({
      em,
      input: {
        filename: ATTACHMENT_FILE_NAME,
        mimetype: 'image/png',
        encoding: '7bit',
        createReadStream: () => Readable.from(fs.readFileSync(__dirname + '/curator/test.png')),
      },
      tenantId,
      opportunityId,
      noUpload: true,
      attachmentId: id,
    });
  };

  const addUpdateEventsToOpportunity = async (users: User[], opportuntiyId: string) => {
    // add additional events to the 1st opportunity
    const curationEvent1 = await CurationEventController.getCurationEvent(
      em,
      users[2],
      EntityType.OPPORTUNITY,
      opportuntiyId,
      CurationEventType.UPDATE,
    );
    // we need a known id for some events
    if (opportuntiyId === Id.simpleId(1, ID_PREFIX_0)) {
      curationEvent1.id = Id.simpleId(1, ID_PREFIX_0);
    }
    await em.persist(curationEvent1);
    const curationEvent2 = await CurationEventController.getCurationEvent(
      em,
      users[3],
      EntityType.OPPORTUNITY,
      opportuntiyId,
      CurationEventType.UPDATE,
    );
    await em.persist(curationEvent2);
  };

  try {
    let roles: Role[];
    if (operation === 'clear') {
      roles = (await prodFixtures(em)).roles;
    } else {
      roles = await getRoles(em);
    }

    const meta = TenantMeta.newTenantMeta({
      config: monumentsMenConfig,
      theme: monumentsMenTheme,
      serverConfig: serverConfig,
      content: _content,
    });
    const tenant1 = Tenant.newTenant({ name: 'The Monuments Men', handle: 'monumentsMen', label: 'The Monuments Men' });
    tenant1.id = Id.simpleId(1);
    tenant1.meta = meta;
    em.persist(tenant1);

    await Promise.all(
      TENANT_ALIASES.map(async (handle, i) => {
        const tenantAlias = TenantAlias.newTenantAlias({
          handle,
          name: `Tenant Alias ${i}`,
        });
        tenantAlias.id = Id.simpleId(i);
        tenantAlias.tenant = tenant1;
        // for testing theme from alias
        if (handle === 'test') {
          tenantAlias.meta = TenantMeta.newTenantMeta({ config: _testConfig, theme: _testTheme });
        }
        if (handle === '82ndairborne') {
          tenantAlias.meta = TenantMeta.newTenantMeta({ config: _82ndairborneConfig, theme: _82ndairborneTheme });
        }
        await em.persist(tenantAlias);
        return tenantAlias;
      }),
    );

    const tenant2 = Tenant.newTenant({
      name: 'Starship Troopers',
      handle: 'starshipTroopers',
      label: 'Starship Troopers',
    });
    tenant2.id = Id.simpleId(2);
    tenant2.meta = TenantMeta.newTenantMeta({
      config: monumentsMenConfig,
      theme: monumentsMenTheme,
      content: _content,
    });
    await em.persist(tenant2);

    const allTenants = [tenant1, tenant2];

    if (type === 'all') {
      const tenant3 = Tenant.newTenant({
        name: 'The Ten Thousand',
        handle: 'theTenThousand',
        label: 'The Ten Thousand',
      });
      tenant3.id = Id.simpleId(3);
      tenant3.meta = meta;
      await em.persist(tenant3);

      const tenant4 = Tenant.newTenant({
        name: 'Rebel Alliance',
        handle: 'rebelAlliance',
        label: 'Rebel Alliance',
      });
      tenant4.id = Id.simpleId(4);
      tenant4.meta = meta;
      await em.persist(tenant4);

      const tenant5 = Tenant.newTenant({
        name: 'The White Company',
        handle: 'theWhiteCompany',
        label: 'The White Company',
      });
      tenant5.id = Id.simpleId(5);
      tenant5.meta = meta;
      await em.persist(tenant5);

      const tenant6 = Tenant.newTenant({
        name: 'The Varangian Guard',
        handle: 'theVarangianGuard',
        label: 'The Varangian Guard',
      });
      tenant6.id = Id.simpleId(6);
      tenant6.meta = meta;
      await em.persist(tenant6);

      const tenant7 = Tenant.newTenant({
        name: 'The Kiss Army',
        handle: 'theKissArmy',
        label: 'The Kiss Army',
      });
      tenant7.id = Id.simpleId(7);
      tenant7.meta = meta;
      await em.persist(tenant7);

      const tenant8 = Tenant.newTenant({
        name: 'The Sardaukar',
        handle: 'theSardaukar',
        label: 'The Sardaukar',
      });
      tenant8.id = Id.simpleId(8);
      tenant8.meta = meta;
      await em.persist(tenant8);

      const tenant9 = Tenant.newTenant({
        name: 'The Sacred Band Of Thebes',
        handle: 'theSacredBandOfThebes',
        label: 'The Sacred Band Of Thebes',
      });
      tenant9.id = Id.simpleId(9);
      tenant9.meta = meta;
      await em.persist(tenant9);

      const tenant11 = Tenant.newTenant({
        name: 'The Justice League',
        handle: 'theJusticeLeague',
        label: 'The Justice League',
      });
      tenant11.id = Id.simpleId(11);
      tenant11.meta = meta;
      await em.persist(tenant11);

      allTenants.push(tenant3, tenant4, tenant5, tenant6, tenant7, tenant8, tenant9, tenant11);
    }

    const tenant1PrivilegeGroup = await createPrivilegeGroup(
      em,
      tenant1,
      'Monuments Men Only',
      [tenant1],
      Id.simpleId(0, ID_PREFIX_0),
    );
    // Create of a privilege group that grants permission to see all tenants' data
    const analystPrivilegeGroup1 = await createPrivilegeGroup(
      em,
      tenant1,
      'All Tenants Group',
      allTenants,
      Id.simpleId(1, ID_PREFIX_0),
    );

    const tenant2PrivilegeGroup = await createPrivilegeGroup(
      em,
      tenant2,
      'Starship Troopers Only',
      [tenant2],
      Id.simpleId(0, ID_PREFIX_1),
    );
    // Create of a privilege group that grants permission to see all tenants' data
    const analystPrivilegeGroup2 = await createPrivilegeGroup(
      em,
      tenant2,
      'All Tenants Group',
      [tenant1, tenant2],
      Id.simpleId(1, ID_PREFIX_1),
    );

    const _bootstrap = async (
      tenant: Tenant,
      tenantIndex: number,
      basicPrivilegeGroup?: PrivilegeGroup,
      analystPrivilegeGroup?: PrivilegeGroup,
      opsPerTenant?: number,
    ) => {
      return await bootstrapTenant({
        tenant,
        roles,
        tenantIndex,
        basicPrivilegeGroup,
        analystPrivilegeGroup,
        opsPerTenant,
      });
    };

    const { users: tenant1Users } = await _bootstrap(tenant1, 0, tenant1PrivilegeGroup, analystPrivilegeGroup1);
    const { users: tenant2Users } = await _bootstrap(tenant2, 1, tenant2PrivilegeGroup, analystPrivilegeGroup2);

    if (type === 'all') {
      await _bootstrap(allTenants[2], 2, undefined, undefined, 50);
      await _bootstrap(allTenants[3], 3, undefined, undefined, 30);
      await _bootstrap(allTenants[4], 4, undefined, undefined, 10);
      await _bootstrap(allTenants[5], 5, undefined, undefined, 16);
      await _bootstrap(allTenants[6], 6, undefined, undefined, 70);
      await _bootstrap(allTenants[7], 7, undefined, undefined, 55);
      await _bootstrap(allTenants[8], 8, undefined, undefined, 19);
      await _bootstrap(allTenants[9], 9, undefined, undefined, 17);
    }

    /* end block */

    await em.flush();

    await addUpdateEventsToOpportunity(tenant1Users, Id.simpleId(1, ID_PREFIX_0));
    await addUpdateEventsToOpportunity(tenant1Users, Id.simpleId(3, ID_PREFIX_0));
    await addUpdateEventsToOpportunity(tenant2Users, Id.simpleId(1, ID_PREFIX_1));
    await addUpdateEventsToOpportunity(tenant2Users, Id.simpleId(3, ID_PREFIX_1));

    await em.flush();

    // bootstrap an existing tenant
    // To run this on an existing tenant: uncomment these lines
    /*
    const roles = await getRoles(em);
    const tenant1 = await em.getRepository(Tenant).findOneOrFail({ handle: caseInsensitiveMatchValue('test') });
    const { users: tenant1Users } = await bootstrapTenant(tenant1, roles, ID_PREFIX_0);
    await em.flush();
    await addUpdateEventsToOpportunity(tenant1Users, Id.simpleId(1, ID_PREFIX_0));
    await addUpdateEventsToOpportunity(tenant1Users, Id.simpleId(3, ID_PREFIX_0));
    await em.flush();
    */
  } catch (error) {
    console.error('📌 Could not load fixtures', error);
  }
};

function getRandomDate(from: Date, to: Date) {
  const fromTime = from.getTime();
  const toTime = to.getTime();
  return new Date(fromTime + Math.random() * (toTime - fromTime));
}
