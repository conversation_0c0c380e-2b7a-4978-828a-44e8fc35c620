import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let token: string;

describe('))))))))))))) User Tests (((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
  });

  beforeEach(async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  /***
   *      __  __               __  __            __  _          __         __  ____
   *     / / / /__  ___ ___ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *    / /_/ / _ \/ _ `/ // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    \____/_//_/\_,_/\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                             /_/
   */

  it('should create user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          submitUser (
            input: {
              emailAddress: "<EMAIL>",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "monumentsMen"
            },
            ){
              id emailAddress firstName lastName org1 org2 status
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.submitUser).to.be.a('object');
    expect(response.body.data.submitUser.id).to.not.be.null;
  });

  it('should create user w/ tenant alias', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          submitUser (
            input: {
              emailAddress: "<EMAIL>",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "3_tenantAlias"
            },
            ){
              id emailAddress firstName lastName org1 org2 status
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.submitUser).to.be.a('object');
    expect(response.body.data.submitUser.id).to.not.be.null;
  });

  it('should fail validation', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          submitUser (
            input: {
              emailAddress: "user@user",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "monumentsMen"
            },
            ){
              id emailAddress firstName lastName org1 org2 status
            }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors).to.be.an('array');
    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.ARGUMENT_VALIDATION_ERROR].code);
    expect(response.body.errors[0].extensions.validationErrors[0].property).equal('emailAddress');
  });

  it('should update user unverified user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          submitUser (
            input: {
              emailAddress: "<EMAIL>",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "monumentsMen"
            },
            ){
              id emailAddress firstName lastName org1 org2 status
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.submitUser).to.be.a('object');
    expect(response.body.data.submitUser.id).equal(Id.simpleId(1));
    expect(response.body.data.submitUser.firstName).equal('Burt');
  });

  // @TODO:registration
  // turning this off for now
  /*
  it('should fail create for verified user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          submitUser (
            input: {
              emailAddress: "<EMAIL>",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "monumentsMen"
            },
            ){
              id emailAddress firstName lastName org1 org2 status
            }
        }
        `,
      })
      .expect(400);

    expect(response).to.not.be.null;
  });
  */

  it('should register user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          register (
            input: {
              emailAddress: "<EMAIL>",
              password: "someP@ss1abcd1234",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "monumentsMen"
            },
            ){
              user {
                id
              }
              token
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.register).to.be.a('object');
    expect(response.body.data.register.user.id).to.not.be.null;
    expect(response.body.data.register.token).to.not.be.null;
  });

  it('should create user w/ tenant alias', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          submitUser (
            input: {
              emailAddress: "<EMAIL>",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "2_tenantAlias"
            },
            ){
              id emailAddress firstName lastName org1 org2 status
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.submitUser).to.be.a('object');
    expect(response.body.data.submitUser.id).to.not.be.null;
  });

  it('should register unverified user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          register (
            input: {
              emailAddress: "<EMAIL>",
              password: "someP@ss1abcd1234",
              firstName: "Burt",
              lastName: "McUser",
              org1 : "94949",
              org2 : "995959",
              phone: "************",
              options: { cookieAcceptance: true },
              tenantHandle: "monumentsMen"
            },
            ){
              user {
                id
              }
              token
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.register).to.be.a('object');
    expect(response.body.data.register.user.id).to.not.be.null;
    // this should create a new user - not reuse the existing unverfied user
    expect(response.body.data.register.user.id).to.not.be.equal(Id.simpleId(1));
    expect(response.body.data.register.token).to.not.be.null;
  });

  it('should fail to login user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          login (
              userName: "<EMAIL>",
              password: "pass2",
              tenantHandle: "monumentsMen"
            ){
              user {
                id
              }
              token
            }
        }
        `,
      })
      .expect(400);
  });

  it('should login user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          login (
              userName: "<EMAIL>",
              password: "pass1",
              tenantHandle: "monumentsMen"
            ){
              user {
                id
              }
              token
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.login).to.be.a('object');
    expect(response.body.data.login.user.id).to.not.be.null;
    expect(response.body.data.login.token).to.not.be.null;
    token = response.body.data.login.token;
  });

  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  it('should get current user', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `query {
          getCurrentUser {
            id emailAddress firstName lastName org1 org2 status
            submissions {
              title
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getCurrentUser).to.be.a('object');
    expect(response.body.data.getCurrentUser.id).equal(Id.simpleId(2));
  });

  it('should fail updating user due to length less than 15 characters', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          updateCurrentUser (input: {
           password: "someP@ss1",
          }) {
                id emailAddress firstName lastName
          }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.ARGUMENT_VALIDATION_ERROR].code);
    expect(response.body.errors[0].extensions.validationErrors[0].constraints.minLength).equal(
      'Password must be at least 15 characters long',
    );
  });

  it('should fail updating user due to no symbol', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          updateCurrentUser (input: {
           password: "somePass1234abcdEFG",
          }) {
                id emailAddress firstName lastName
          }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.ARGUMENT_VALIDATION_ERROR].code);
    expect(response.body.errors[0].extensions.validationErrors[0].constraints.matches).equal(
      'Password must include at least one special character',
    );
  });

  it('should fail updating user due to no lowercase letter', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          updateCurrentUser (input: {
           password: "SOMEPASS123@BCD",
          }) {
                id emailAddress firstName lastName
          }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.ARGUMENT_VALIDATION_ERROR].code);
    expect(response.body.errors[0].extensions.validationErrors[0].constraints.matches).equal(
      'Password must include at least one lower-case character',
    );
  });

  it('should fail updating user due to no uppercase letter', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          updateCurrentUser (input: {
           password: "somepass123@bcd",
          }) {
                id emailAddress firstName lastName
          }
        }
        `,
      })
      .expect(400);

    console.dir({ body: response.body.errors }, { depth: null, colors: true });

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.ARGUMENT_VALIDATION_ERROR].code);
    expect(response.body.errors[0].extensions.validationErrors[0].constraints.matches).equal(
      'Password must include at least one upper-case character',
    );
  });

  it('should fail updating user due to no number', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          updateCurrentUser (input: {
           password: "sOmepasswasd@bcd",
          }) {
                id emailAddress firstName lastName
          }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.ARGUMENT_VALIDATION_ERROR].code);
    expect(response.body.errors[0].extensions.validationErrors[0].constraints.matches).equal(
      'Password must include at least one number',
    );
  });

  it('should update user', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          updateCurrentUser (input: {
            org1 : "33333",
            org2 : "995959",
            options: { optOutAll: true }
          }, links: { appMetaId: "${Id.simpleId(1, ID_PREFIX_0)}" }) {
                id emailAddress firstName lastName org1 org2 status
                appMeta {
                  id
                  curationMeta
                }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.updateCurrentUser).to.be.a('object');
    expect(response.body.data.updateCurrentUser.org1).equal('33333');
    expect(response.body.data.updateCurrentUser.id).equal(Id.simpleId(2));
    expect(response.body.data.updateCurrentUser.appMeta.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.updateCurrentUser.appMeta.curationMeta).not.null;
  });

  it('should login user with tenant alias', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          login (
              userName: "<EMAIL>",
              password: "pass1",
              tenantHandle: "1_tenantAlias"
            ){
              user {
                id
              }
              token
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.login).to.be.a('object');
    expect(response.body.data.login.user.id).to.not.be.null;
    expect(response.body.data.login.token).to.not.be.null;
    token = response.body.data.login.token;
  });

  it('should get current user', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `query {
          getCurrentUser {
            id emailAddress firstName lastName org1 org2 status
            submissions {
              title
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getCurrentUser).to.be.a('object');
    expect(response.body.data.getCurrentUser.id).equal(Id.simpleId(2));
  });

  it('should renew auth', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          renew {
              user {
                id
              }
              token
            }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.renew).to.be.a('object');
    expect(response.body.data.renew.user.id).to.not.be.null;
    expect(response.body.data.renew.token).to.not.be.null;
    token = response.body.data.renew.token;
  });

  it('should delete user', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          deleteCurrentUser
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteCurrentUser).to.be.true;
  });
});
