import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import {  OPS_PER_USER, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let token: string;

describe(')))))))))))) User Submission Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    token = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(2),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  /***
   *      __  __               __  __            __  _          __         __  ____
   *     / / / /__  ___ ___ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *    / /_/ / _ \/ _ `/ // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    \____/_//_/\_,_/\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                             /_/
   */

  it('should create a new submission', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          newSubmission (
            input: {
              title: "Ministry Of Truth",
              statement: "A long statement",
              context: "Some context here..",
              benefits: "There are some, surely....",
              solutionConcepts: "Some concepts here...",
              campaign: "89494"
            },
            links: {
              userId: "${Id.simpleId(1)}"
            }
          ){
            id title statement context benefits solutionConcepts campaign
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.newSubmission).to.be.a('object');
    expect(response.body.data.newSubmission.id).to.not.be.null;
    expect(response.body.data.newSubmission.campaign).to.equal('89494');
  });

  //@TODO:registration - turn this off until we have registration
  /*
  it('should fail to create submission for verified user', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `mutation {
          newSubmission (
            input: {
              title: "Ministry Of Truth",
              statement: "A long statement",
              context: "Some context here..",
              benefits: "There are some, surely....",
              solutionConcepts: "Some concepts here...",
              campaign: "89494"
            },
            links: {
              userId: "${Id.simpleId(2)}"
            }
          ){
            id title statement context benefits solutionConcepts campaign
          }
        }
        `,
      })
      .expect(400);

  });
  */

  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  it('get submissions', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `query {
        getSubmissions {
          id title statement context benefits solutionConcepts campaign
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getSubmissions).to.be.a('array');
    expect(response.body.data.getSubmissions.length).equal(OPS_PER_USER);
  });

  it('should get submission by id', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `query {
          getSubmission(id: "${Id.simpleId(2)}") {
            id title statement context benefits solutionConcepts campaign
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getSubmission).to.be.a('object');
    expect(response.body.data.getSubmission.id).equal(Id.simpleId(2));
  });

  it('should create submission', async () => {
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          createSubmission (
            input: {
              title: "Ministry Of Truth",
              statement: "A long statement",
              context: "Some context here..",
              benefits: "There are some, surely....",
              solutionConcepts: "Some concepts here...",
              campaign: "89494"
            }
          ){
            id title statement context benefits solutionConcepts campaign
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createSubmission).to.be.a('object');
    expect(response.body.data.createSubmission.id).to.not.be.null;
    expect(response.body.data.createSubmission.campaign).to.equal('89494');
  });

  it('should update submission', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
        updateSubmission (input: {
          context: "Updated context here..",
          benefits: "Some updated benefits",
        }, id: "${Id.simpleId(2)}") {
          id title statement context benefits solutionConcepts campaign
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateSubmission).to.be.a('object');
    expect(response.body.data.updateSubmission.context).equal('Updated context here..');
    expect(response.body.data.updateSubmission.id).equal(Id.simpleId(2));
  });

  it('should delete submission', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/user')
      .set('Authorization', `Bearer ${token}`)
      .send({
        query: `mutation {
          deleteSubmission (id: "${Id.simpleId(2)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteSubmission).to.be.true;
  });
});
