import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import tenantConfig from '../../config/tenants/monumentsMen/config.json';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;

describe('))))))))))))) User Tenant Tests (((((((((((((((', async () => {

  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get tenants by name alphabetical', async () => {
    const response = await request
    .post('/user')
    .send({
      query: `query {
        queryTenants(
          pagingInput: { pageSize: 5, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "name", ascending: true }]
          }
        ) {
          results {
           id name handle
          }
        }
      }
      `,
    })
    .expect(200);
    expect(response.body.data.queryTenants.results).to.be.a('array');
    expect(response.body.data.queryTenants.results.length).equal(3);
    expect(response.body.data.queryTenants.results[0].handle).equal('default');
    expect(response.body.data.queryTenants.results[2].handle).equal('monumentsMen');
  });

  it('search tenants by name alphabetical', async () => {
    const response = await request
    .post('/user')
    .send({
      query: `query {
        queryTenants(
          pagingInput: { pageSize: 5, cursor: "0" },
          searchSortInput: {
            searchFields: [{ fieldNames: [ "name" ], operator: MATCH, searchValue: "Monument" }],
            sortFields: [{ fieldName: "name", ascending: true }]
          }
        ) {
          results {
           id name handle meta { config }
          }
        }
      }
      `,
    })
    .expect(200);
    expect(response.body.data.queryTenants.results).to.be.a('array');
    expect(response.body.data.queryTenants.results.length).equal(1);
    expect(response.body.data.queryTenants.results[0].handle).equal('monumentsMen');
  });

  it('find tenants and aliases by name', async () => {
    const response = await request
    .post('/user')
    .send({
      query: `query {
        findTenantInfoByName(
          name: "Monuments"
        ) {
           tenantId name handle meta { config }
        }
      }
      `,
    })
    .expect(200);
    expect(response.body.data.findTenantInfoByName).to.be.a('array');
    expect(response.body.data.findTenantInfoByName.length).equal(1);
    expect(response.body.data.findTenantInfoByName[0].handle).equal('monumentsMen');
  });

  it('should get tenant by id with no auth', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `query {
          getTenant(id: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id name handle meta {
              config theme filterOtherPrivateOpportunities
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenant).to.be.a('object');
    expect(response.body.data.getTenant.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getTenant.name).equal('The Monuments Men');
    expect(response.body.data.getTenant.handle).equal('monumentsMen');
    expect(response.body.data.getTenant.meta.config.applicationTitle).equal('Pathfinder Innovation');
    expect(response.body.data.getTenant.meta.theme).not.undefined;
    expect(response.body.data.getTenant.meta.filterOtherPrivateOpportunities).equal(false);
  });

  it('should get tenant by alias with no auth', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `query {
          getTenant(handleOrAlias: "5_tenantAlias") {
            id name handle
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenant).to.be.a('object');
    expect(response.body.data.getTenant.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getTenant.name).equal('The Monuments Men');
    expect(response.body.data.getTenant.handle).equal('monumentsMen');
  });

  it('should get tenant info by alias with no auth', async () => {
    const response = await request
      .post('/user')
      .send({
        query: `query {
          getTenantInfo(handleOrAlias: "5_tenantAlias") {
            tenantId name handle
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getTenantInfo).to.be.a('object');
    expect(response.body.data.getTenantInfo.tenantId).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.data.getTenantInfo.name).equal('Tenant Alias 5');
    expect(response.body.data.getTenantInfo.handle).equal('5_tenantAlias');
  });

});
