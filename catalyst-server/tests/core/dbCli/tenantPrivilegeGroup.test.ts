import { execSync } from 'child_process';
import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { expect } from 'chai';
import {  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { PrivilegeGroup } from 'core/entities/PrivilegeGroup';
import { Privilege } from 'core/entities/Privilege';
import { ResourceType } from 'core/contracts/enums/ResourceType';
import { Tenant } from 'core/entities/Tenant';
import { AuthResponse } from 'api/output/AuthResponse';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
const MonumentsMenHandle = 'monumentsMen'
const MonumentsMenName = 'The Monuments Men'
const StarShipTroopersHandle = 'starshipTroopers'
const StarShipTroopersName = 'Starship Troopers'
let automatedTestingTenant = { adminToken: '', name: 'Automated Test Tenant', handle: 'autotest', emailAddress: 'admin@default', pwd: 'passadmin', tenantId: '', privilegeGroupId: '' };
let getTenant = {
  query: `query {
    getTenant ( handleOrAlias: "${automatedTestingTenant.handle}" ) {
      id name handle meta {
      config
      theme
      serverConfiguration
      content
    }
  }
  }
  `,
};
let loginAutomatedTestingUser = {
  query: `mutation {
    login (
        userName: "${automatedTestingTenant.emailAddress}",
        password: "${automatedTestingTenant.pwd}",
        tenantHandle: "${automatedTestingTenant.handle}"
      ){
        user { id emailAddress }
        token
      }
  }`,
};
let getPrivilegeGroupQuery = {
    query: `query {
        getPrivilegeGroup(name: "Analyst Access")
        {
            id
            name
            privileges{ id name resourceId resourceType}
            users { emailAddress }
          }
      }
  `,
};

describe('))))))))))))) Core: dCli Tenant Privilege Group Tests ((((((((((((((', async () => {
  // For this testing we want to standup a new tenant for validating outside the bootstrap data.
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    restoreDbFromLocalBackup();
    // Create a new tenant for testing this tooling
    execSync(`npm run dbCli tenant "${automatedTestingTenant.name}" ${automatedTestingTenant.handle} ${automatedTestingTenant.pwd} config/tenants/test/config.json config/tenants/test/theme.json "${automatedTestingTenant.name}"`);
    const createTenantResponse = await request
      .post('/curator')
      .send(getTenant)
      .expect(200);

    expect(createTenantResponse.body.data.getTenant).to.be.a('object');
    expect(createTenantResponse.body.data.getTenant).not.null;
    const resultsTenant: Tenant = createTenantResponse.body.data.getTenant;
    automatedTestingTenant.tenantId = resultsTenant.id;

    const loginResponse = await request
      .post('/curator')
      .send(loginAutomatedTestingUser)
      .expect(200);
    expect(loginResponse.body.data.login).to.be.a('object');
    expect(loginResponse).not.null;
    const adminUser: AuthResponse = loginResponse.body.data.login;
    expect(adminUser).not.null;
    expect(adminUser.user).not.null;
    automatedTestingTenant.adminToken = adminUser.token;
  });

  after(async () => {
    application.server?.close();
  });

  it('Add new privilige group for a tenant', async () => {
    
    execSync(`npm run dbCli tenant-privilege-group ${automatedTestingTenant.handle} "Analyst Access" ${automatedTestingTenant.handle}`);
    
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${automatedTestingTenant.adminToken}`)
      .send(getPrivilegeGroupQuery)
      .expect(200);
    
    expect(response.body.data.getPrivilegeGroup).not.null;
    expect(response.body.data.getPrivilegeGroup).to.be.a('object');
    const resultsPrivilege: PrivilegeGroup = response.body.data.getPrivilegeGroup;
    automatedTestingTenant.privilegeGroupId = resultsPrivilege.id;
    expect(resultsPrivilege.name).to.equal('Analyst Access');
    expect(resultsPrivilege.privileges.length).to.equal(1);
    expect(resultsPrivilege.privileges[0].name).to.equal(automatedTestingTenant.name);
    expect(resultsPrivilege.privileges[0].resourceId).to.equal(automatedTestingTenant.tenantId);
    expect(resultsPrivilege.privileges[0].resourceType).to.equal(ResourceType.TENANT);
    expect(resultsPrivilege.users.length).to.equal(1);
  });

  it('Add tenant to existing privilege group', async () => {

    execSync(`npm run dbCli add-to-tenant-privilege-group-by-name ${automatedTestingTenant.handle} "Analyst Access" ${StarShipTroopersHandle}`);
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${automatedTestingTenant.adminToken}`)
      .send(getPrivilegeGroupQuery)
      .expect(200);

    const resultsPrivilege: PrivilegeGroup = response.body.data.getPrivilegeGroup;
    expect(resultsPrivilege).not.null;
    expect(resultsPrivilege).to.be.a('object');
    expect(resultsPrivilege.name).to.equal('Analyst Access');
    expect(resultsPrivilege.privileges.length).to.equal(2);
    let foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === StarShipTroopersName &&
        privilege.resourceId === '00000000-0000-0000-0000-000000000002' &&
        privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === automatedTestingTenant.name &&
        privilege.resourceId === automatedTestingTenant.tenantId &&
        privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
      privilege.name === MonumentsMenName &&
      privilege.resourceId === '00000000-0000-0000-0000-000000000001' &&
      privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).to.be.undefined;
    expect(resultsPrivilege.users.length).to.equal(1);
  });

  it('Verify failure adding tenant to privilege group where it exists', async () => {

    execSync(`npm run dbCli add-to-tenant-privilege-group-by-name ${automatedTestingTenant.handle} "Analyst Access" ${MonumentsMenHandle} ${StarShipTroopersHandle}`);
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${automatedTestingTenant.adminToken}`)
      .send(getPrivilegeGroupQuery)
      .expect(200);

    const resultsPrivilege: PrivilegeGroup = response.body.data.getPrivilegeGroup;
    expect(resultsPrivilege).not.null;
    expect(resultsPrivilege).to.be.a('object');
    expect(resultsPrivilege.name).to.equal('Analyst Access');
    expect(resultsPrivilege.privileges.length).to.equal(3);
    let foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === StarShipTroopersName &&
        privilege.resourceId === '00000000-0000-0000-0000-000000000002' &&
        privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === automatedTestingTenant.name &&
        privilege.resourceId === automatedTestingTenant.tenantId &&
        privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
      privilege.name === MonumentsMenName &&
      privilege.resourceId === '00000000-0000-0000-0000-000000000001' &&
      privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    expect(resultsPrivilege.users.length).to.equal(1);
  });

  it('Verify duplicates do not occur for existing tenant privilege group', async () => {

    execSync(`npm run dbCli add-to-tenant-privilege-group-by-name ${automatedTestingTenant.handle} "Analyst Access" ${StarShipTroopersHandle}`);
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${automatedTestingTenant.adminToken}`)
      .send(getPrivilegeGroupQuery)
      .expect(200);

    const resultsPrivilege: PrivilegeGroup = response.body.data.getPrivilegeGroup;
    expect(resultsPrivilege).not.null;
    expect(resultsPrivilege).to.be.a('object');
    expect(resultsPrivilege.name).to.equal('Analyst Access');
    expect(resultsPrivilege.privileges.length).to.equal(3);
    let foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
      privilege.name === StarShipTroopersName &&
      privilege.resourceId === '00000000-0000-0000-0000-000000000002' &&
      privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === automatedTestingTenant.name &&
        privilege.resourceId === automatedTestingTenant.tenantId &&
        privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
      privilege.name === MonumentsMenName &&
      privilege.resourceId === '00000000-0000-0000-0000-000000000001' &&
      privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    expect(resultsPrivilege.users.length).to.equal(1);
  });

  it('Delete starshipTroopers existing tenant privilege group', async () => {

    execSync(`npm run dbCli remove-from-tenant-privilege-group ${automatedTestingTenant.handle} ${automatedTestingTenant.privilegeGroupId} ${StarShipTroopersHandle}`);
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${automatedTestingTenant.adminToken}`)
      .send(getPrivilegeGroupQuery)
      .expect(200);

    expect(response.body.data.getPrivilegeGroup).not.null;
    expect(response.body.data.getPrivilegeGroup).to.be.a('object');
    const resultsPrivilege: PrivilegeGroup = response.body.data.getPrivilegeGroup;
    expect(resultsPrivilege.name).to.equal('Analyst Access');
    expect(resultsPrivilege.privileges.length).to.equal(2);
    let foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
      privilege.name === StarShipTroopersName &&
      privilege.resourceId === '00000000-0000-0000-0000-000000000002' &&
      privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).to.be.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === automatedTestingTenant.name &&
        privilege.resourceId === automatedTestingTenant.tenantId &&
        privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
      privilege.name === MonumentsMenName &&
      privilege.resourceId === '00000000-0000-0000-0000-000000000001' &&
      privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    expect(resultsPrivilege.users.length).to.equal(1);
  });

  it('Check removing tenant from privilege group does not exist', async () => {
    execSync(`npm run dbCli remove-from-tenant-privilege-group ${automatedTestingTenant.handle} ${automatedTestingTenant.privilegeGroupId} ${StarShipTroopersHandle}`);
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${automatedTestingTenant.adminToken}`)
      .send(getPrivilegeGroupQuery)
      .expect(200);

      expect(response.body.data.getPrivilegeGroup).not.null;
      expect(response.body.data.getPrivilegeGroup).to.be.a('object');
      const resultsPrivilege: PrivilegeGroup = response.body.data.getPrivilegeGroup;
      expect(resultsPrivilege.name).to.equal('Analyst Access');
      expect(resultsPrivilege.privileges.length).to.equal(2);
      let foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === StarShipTroopersName &&
        privilege.resourceId === '00000000-0000-0000-0000-000000000002' &&
        privilege.resourceType === ResourceType.TENANT );
      expect(foundPrivilege).to.be.undefined;
      foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
          privilege.name === automatedTestingTenant.name &&
          privilege.resourceId === automatedTestingTenant.tenantId &&
          privilege.resourceType === ResourceType.TENANT );
      expect(foundPrivilege).not.undefined;
      foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === MonumentsMenName &&
        privilege.resourceId === '00000000-0000-0000-0000-000000000001' &&
        privilege.resourceType === ResourceType.TENANT );
      expect(foundPrivilege).not.undefined;
      expect(resultsPrivilege.users.length).to.equal(1);
  });

  it('Delete monumentsMen from existing tenant privilege group', async () => {

    execSync(`npm run dbCli remove-from-tenant-privilege-group ${automatedTestingTenant.handle} ${automatedTestingTenant.privilegeGroupId} ${MonumentsMenHandle}`);
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${automatedTestingTenant.adminToken}`)
      .send(getPrivilegeGroupQuery)
      .expect(200);

    const resultsPrivilege: PrivilegeGroup = response.body.data.getPrivilegeGroup;
    expect(resultsPrivilege).not.null;
    expect(resultsPrivilege).to.be.a('object');
    expect(resultsPrivilege.name).to.equal('Analyst Access');
    expect(resultsPrivilege.privileges.length).to.equal(1);
    let foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
        privilege.name === automatedTestingTenant.name &&
        privilege.resourceId === automatedTestingTenant.tenantId &&
        privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).not.undefined;
    foundPrivilege = resultsPrivilege.privileges.find((privilege: Privilege) => 
      privilege.name === MonumentsMenName &&
      privilege.resourceId === '00000000-0000-0000-0000-000000000001' &&
      privilege.resourceType === ResourceType.TENANT );
    expect(foundPrivilege).to.be.undefined;
    expect(resultsPrivilege.users.length).to.equal(1);
  });
});
