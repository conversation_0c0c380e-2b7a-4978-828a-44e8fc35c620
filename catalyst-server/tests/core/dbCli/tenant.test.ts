import { execSync } from 'child_process';
import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { expect } from 'chai';
import {  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Tenant } from 'core/entities/Tenant';
import { AuthResponse } from 'core/contracts/output/AuthResponse';
import { TenantMeta } from 'core/entities/TenantMeta';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let adminToken: string;
let automatedTestingTenant = { name: 'Automated Test Tenant', handle: 'autotest', emailAddress: 'admin@default', pwd: 'passadmin' };
let autoTestTenantId = '';

let getTenant = {
      query: `query {
        getTenant ( handleOrAlias: "${automatedTestingTenant.handle}" ) {
          id name handle meta {
          config
          theme
          serverConfiguration
          content
        }
      }
      }
      `,
};

let loginAutomatedTestingUser = {
  query: `mutation {
    login (
        userName: "${automatedTestingTenant.emailAddress}",
        password: "${automatedTestingTenant.pwd}",
        tenantHandle: "${automatedTestingTenant.handle}"
      ){
        user { id emailAddress }
        token
      }
  }`,
};

let queryForTenantUser = {
  query: `query {
        queryUsers(
          pagingInput: { pageSize: 10, cursor: "0" },
          searchSortInput: {
              searchFields: [
                  { 
                      fieldNames: ["emailAddress"], 
                      operator: EQ,
                      searchValue: ["${automatedTestingTenant.emailAddress}"]
                  }
              ]
          }
        ) 
        {
          results {          
            id emailAddress
            privilegeGroups {
                id
                privileges {
                    resourceType
                    resourceId
                }
            }
            roles {
                id
                name
            }
          }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }`,
}

// For this testing we want to standup a new tenant for validating outside the bootstrap data.
describe('))))))))))))) Core: dCli Tenant Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    restoreDbFromLocalBackup();
  });

  after(async () => {
    application.server?.close();
  });

  it('Add new tenant', async () => {
    
    execSync(`npm run dbCli tenant "${automatedTestingTenant.name}" ${automatedTestingTenant.handle} ${automatedTestingTenant.pwd} config/tenants/test/config.json config/tenants/test/theme.json "${automatedTestingTenant.name}"`);

    const response = await request
      .post('/curator')
      .send(getTenant)
      .expect(200);

    expect(response.body.data.getTenant).to.be.a('object');
    const resultsTenant: Tenant = response.body.data.getTenant;
    autoTestTenantId = resultsTenant.id;
    expect(resultsTenant).not.null;
    expect(resultsTenant).to.be.a('object');
    expect(resultsTenant.name).to.equal(automatedTestingTenant.name);
    expect(resultsTenant.handle).to.equal(automatedTestingTenant.handle);
    expect(resultsTenant.meta).not.null;
    const tenantMeta = resultsTenant.meta as TenantMeta;
    expect(tenantMeta.config).not.null;
    expect(tenantMeta.theme).not.null;
  });

  it('Add tenant server config', async () => {
    
    execSync(`npm run dbCli update-serverConfig ${automatedTestingTenant.handle} config/tenants/test/serverConfig.json ${automatedTestingTenant.handle}`);

    const response = await request
      .post('/curator')
      .send(getTenant)
      .expect(200);

    expect(response.body.data.getTenant).to.be.a('object');
    const resultsTenant: Tenant = response.body.data.getTenant;
    expect(resultsTenant).not.null;
    expect(resultsTenant).to.be.a('object');
    expect(resultsTenant.name).to.equal(automatedTestingTenant.name);
    expect(resultsTenant.handle).to.equal(automatedTestingTenant.handle);
    expect(resultsTenant.meta).not.null;
    const tenantMeta = resultsTenant.meta as TenantMeta;
    expect(tenantMeta.config).not.null;
    expect(tenantMeta.theme).not.null;
    expect(tenantMeta.serverConfiguration).not.null;
    expect(tenantMeta.serverConfiguration?.filterOtherPrivateOpportunities).to.equal(true);
  });

  it('Add tenant content config', async () => {
    
    execSync(`npm run dbCli update-content ${automatedTestingTenant.handle} config/content/content.json ${automatedTestingTenant.handle}`);

    const response = await request
      .post('/curator')
      .send(getTenant)
      .expect(200);

    expect(response.body.data.getTenant).to.be.a('object');
    const resultsTenant: Tenant = response.body.data.getTenant;
    expect(resultsTenant).not.null;
    expect(resultsTenant).to.be.a('object');
    expect(resultsTenant.name).to.equal(automatedTestingTenant.name);
    expect(resultsTenant.handle).to.equal(automatedTestingTenant.handle);
    expect(resultsTenant.meta).not.null;
    const tenantMeta = resultsTenant.meta as TenantMeta;
    expect(tenantMeta.config).not.null;
    expect(tenantMeta.theme).not.null;
    expect(tenantMeta.serverConfiguration).not.null;
    expect(tenantMeta.serverConfiguration?.filterOtherPrivateOpportunities).to.equal(true);
    expect(tenantMeta.content).not.null;
  });

  it('Login to new tenant as admin', async () => {

    const response = await request
      .post('/curator')
      .send(loginAutomatedTestingUser)
      .expect(200);

    expect(response.body.data.login).to.be.a('object');
    const results: AuthResponse = response.body.data.login;
    expect(results).not.null;
    expect(results).to.be.a('object');
    expect(results.user).not.null;
    expect(results.user).to.be.a('object');
    expect(results.user?.emailAddress).to.equal(automatedTestingTenant.emailAddress);
    expect(results.token).not.null;
    adminToken = results.token;
  });

  it('Delete tenant', async () => {

    execSync(`npm run dbCli delete-tenant ${autoTestTenantId}`);

    const response = await request
      .post('/curator')
      .send(getTenant)
      .expect(200);

    expect(response.body.data.getTenant).to.be.null;

    const userResponse = await request
      .post('/curator')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(queryForTenantUser)
      .expect(200);

    expect(userResponse.body.data.queryUsers.results).not.null;
    expect(userResponse.body.data.queryUsers.results.length).to.equal(0);
  });

});
