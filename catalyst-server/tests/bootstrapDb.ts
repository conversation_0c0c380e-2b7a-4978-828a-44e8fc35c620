import Application from 'application';
import { loadDevFixtures } from 'loadFixtures';
import { clearDatabase } from 'core/utils/databaseUtils';

const strap = async ({
  type,
  operation,
  envOverride,
}: {
  type: 'all' | 'test';
  operation: 'clear' | 'update';
  envOverride?: string;
}) => {
  const application = new Application();
  await application.init();
  if(operation === 'clear') await clearDatabase(application.orm!);
  await loadDevFixtures(application.orm!, type, operation);
  console.log('🚀 Database cleared, dev fixtures loaded');
  await application.orm?.close();
};

const clear = async (application: Application) => {
  await clearDatabase(application.orm!);
  console.log('🚀 Database cleared');
};

const help = () => {
  console.log('bootstrap (test|all) (clear|update) (<env-override>)');
  console.log(`(default is 'test clear'`);
  process.exit(0);
};

(async () => {
  try {
    const args = process.argv.slice(2);
    if (!args.length) {
      strap({type: 'test', operation: 'clear'});
    } else if (args[0] == 'all' || args[0] === 'test') {
      const type = args?.[0] as 'all' | 'test';
      const operation = args?.[1] as 'clear' | 'update' | undefined;
      const envOverride = args?.[2] as string | undefined;
      if (operation && operation !== 'clear' && operation !== 'update'){
        help();
      }
      strap({type, operation: operation || 'clear', envOverride});
    } else {
      help();
    }
  } catch (e) {
    console.log(e);
    process.exit(1);
  }
})();
