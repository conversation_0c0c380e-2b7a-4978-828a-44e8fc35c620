# This yml file is used by Azure DevOps to update our environments tenant configuration data.
# This is a manual process of execution and will push config files located in this project into
# the targeted environment database.
#
# Created by: <PERSON>
trigger: none

parameters:
- name: environment
  displayName: Select which environment should be updated with production data.
  type: string
  default: "staging"
  values:
  - staging
  - test

variables:
  envStaging: staging
  envTest: test
  backupFile: prod_dump.sql
  stagingDatabase: catalyst-db-staging
  testDatabase: catalyst-db
  buildDate: $(date '+%Y%m%d')

name: $(buildDate).$(Rev:.r)

stages:
  - stage: refresh
    displayName: 'Pulling and restoring data'
    jobs:
      - job: refreshData
        displayName: 'Pull and Push prod data to environment'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - checkout: none
          
          # -------------- Download and copy Environment files --------------
          - task: DownloadSecureFile@1
            name: productionPgpassFile
            displayName: 'Download production pgpass file'
            inputs:
              secureFile: $(production_pgpass)

          - task: CopyFiles@2
            displayName: 'Copy production pgpass file'
            inputs:
              SourceFolder: '$(Agent.TempDirectory)'
              Contents: '$(production_pgpass)'
              OverWrite: true
              TargetFolder: '$(Pipeline.Workspace)'

          # -------------- Create production dump file --------------

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                isActive=$(sudo systemctl is-active postgresql)
                echo "Postgres status: ${isActive}"
                if [ $isActive = "inactive" ]; then
                  echo "starting postgres"
                  sudo service postgresql start
                fi
              workingDirectory: $(Pipeline.Workspace)
            name: startPostgres
            displayName: 'Start Postgres'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                cp $(production_pgpass) .pgpass
                echo "path: $(Pipeline.Workspace)"
                export PGPASSFILE=$(Pipeline.Workspace)/.pgpass
                echo "Prod pgpass local: $PGPASSFILE"
                if [ $(deployChanges) = true ]; then
                  echo "Starting data dump..."
                  chmod 0600 $PGPASSFILE
                  pg_dump -h catalyst-server-db.postgres.database.usgovcloudapi.net -w -U catalyst_admin -c catalyst-db > ./$(backupFile)
                fi
                rm "$PGPASSFILE"
                rm $(Pipeline.Workspace)/.prod-pgpass
                unset PGPASSFILE
              workingDirectory: $(Pipeline.Workspace)
            name: productionDump
            displayName: 'Production data dump'

          # -------------- Download and copy Environment files --------------
  
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                ENVIRONMENT=${{ parameters.environment }}
                echo "##vso[task.setvariable variable=PGPASS;isoutput=true]$(staging_pgpass)"
                echo "##vso[task.setvariable variable=DATABASE;isoutput=true]$(stagingDatabase)"
                echo "##vso[task.setvariable variable=ENVIRONMENT;isoutput=true]$ENVIRONMENT"
                if [ $ENVIRONMENT == $(envTest) ]; then
                  echo "Using test environment settings"
                  echo "##vso[task.setvariable variable=PGPASS;isoutput=true]$(test_pgpass)"
                  echo "##vso[task.setvariable variable=DATABASE;isoutput=true]$(testDatabase)"
                fi
                echo "##vso[build.updatebuildnumber]"${ENVIRONMENT}-$(buildDate)""
            name: setupValues
            displayName: 'Set variables'

          - task: DownloadSecureFile@1
            name: testPgpassFile
            condition: ${{ eq(parameters.environment, 'test') }}
            displayName: 'Download test pgpass file'
            inputs:
              secureFile: $(test_pgpass)

          - task: DownloadSecureFile@1
            name: stagingPgpassFile
            condition: ${{ eq(parameters.environment, 'staging') }}
            displayName: 'Download staging pgpass file'
            inputs:
              secureFile: $(staging_pgpass)

          - task: CopyFiles@2
            displayName: 'Copy pgpass file'
            inputs:
              SourceFolder: '$(Agent.TempDirectory)'
              Contents: '$(setupValues.PGPASS)'
              OverWrite: true
              TargetFolder: $(Pipeline.Workspace)

          # -------------- Run produciton backup against environment --------------

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Updating $(setupValues.ENVIRONMENT) database."
                echo "Rename $(setupValues.PGPASS) to .pgpass"
                cp $(setupValues.PGPASS) .pgpass
                export PGPASSFILE=$(Pipeline.Workspace)/.pgpass
                echo "PGPASS: $PGPASSFILE, DATABASE: $(setupValues.DATABASE)"
                if [ $(deployChanges) = true ]; then
                  echo "Excuting clean and restore"
                  chmod 0600 .pgpass
                  psql -h catalyst-db-staging.postgres.database.usgovcloudapi.net -w -U catalyst_admin -d $(setupValues.DATABASE) < ./$(backupFile)
                  rm $(backupFile)
                fi
                rm $PGPASSFILE
                rm $(Pipeline.Workspace)/$(setupValues.PGPASS)
                unset PGPASSFILE
                ls -la
              workingDirectory: $(Pipeline.Workspace)
            name: updateEnvironmentData
            displayName: 'Clean and restore data'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Stoping postgres"
                sudo service postgresql stop
              workingDirectory: $(Pipeline.Workspace)
            name: stopPostgres
            displayName: 'Stop Postgres'

      - job: bootstrap
        displayName: 'Bootstrap the environment'
        continueOnError: false
        dependsOn: refreshData
        workspace:
          clean: all
        steps:
          - checkout: self
            name: 'checkoutBranch'
            displayName: 'Checkout source branch $(Build.SourceBranchName)'

          - task: UseNode@1
            inputs:
              version: $(nodeVersion)
            displayName: 'Install Node.js'

          # -------------- Download and copy Environment files --------------

          - task: DownloadSecureFile@1
            name: stagingEnvFile
            displayName: 'Download env file'
            condition: ${{ eq(parameters.environment, 'staging') }}
            inputs:
              secureFile: $(staging_env_file)
          
          - task: DownloadSecureFile@1
            name: testEnvFile
            displayName: 'Download test env file'
            condition: ${{ eq(parameters.environment, 'test') }}
            inputs:
              secureFile: $(test_env_file)

          - task: CopyFiles@2
            displayName: 'Copy staging env file'
            condition: ${{ eq(parameters.environment, 'staging') }}
            inputs:
              SourceFolder: '$(Agent.TempDirectory)'
              Contents: '$(staging_env_file)'
              OverWrite: true
              TargetFolder: '$(System.DefaultWorkingDirectory)/catalyst-server'

          - task: CopyFiles@2
            displayName: 'Copy test env file'
            condition: ${{ eq(parameters.environment, 'test') }}
            inputs:
              SourceFolder: '$(Agent.TempDirectory)'
              Contents: '$(test_env_file)'
              OverWrite: true
              TargetFolder: '$(System.DefaultWorkingDirectory)/catalyst-server'

          # -------------- Install and set .env file --------------

          - task: Npm@1
            displayName: "Install catalyst-server dependencies"
            inputs:
              command: ci
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Npm@1
            displayName: "Set test env file"
            condition: ${{ eq(parameters.environment, 'test') }}
            inputs:
              command: custom
              customCommand: 'run test-env'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Npm@1
            displayName: "Set staging env file"
            condition: ${{ eq(parameters.environment, 'staging') }}
            inputs:
              command: custom
              customCommand: 'run staging-env'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          # -------------- Run migration up to resync possible schema changes against environment --------------

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Updating schema."
                NODE_ENV=production npm run dbCli run-migrations
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: miroMigrateUp
            displayName: 'Run Migration Up'
            condition: eq(variables.deployChanges, true)

          # -------------- Update environment tenant data incase changes have been made --------------
          - template: ./templates/stepsBashUpdateAllTenantConfigs.yml

          # -------------- BOOTSTRAP --------------

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Bootstrap ${{ parameters.environment }}"
                if [ $(deployChanges) = true ]; then
                  echo "Executing bootstrap"
                  NODE_ENV=production npm run bootstrap-db test update
                fi
                rm .env
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: bootstrapDB
            displayName: 'Bootstrap Database'