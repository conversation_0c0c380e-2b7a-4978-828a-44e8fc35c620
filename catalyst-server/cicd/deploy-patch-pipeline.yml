# This yml file is used by Azure DevOps to execute automated tests
# and deploy changes for a patch release to the test environment.
# The deployment includes pushing new server images to catalystinnovation container registry
# hosted in the ACME Gov cloud as well as pushing cluster updates to Kubernetes containing
# the deployed image to use.
#
# This file serves one purposes, validation of automated tests and
# automated deployed to our hosted azure gov cloud instance. It also produces deployment
# artifacts for debugging issues. This process will only deploy to test and prduction. A new
# environment maybe created down the road for delivering to a production mirror for better accuracy
# on deploying a patch. This is needed to prevent overlap between development efforts in Test and
# producing a patch/hotfix release.
#
# Testing CICD Build: When testing the pipeline flow, set "deployChanges" to false. This will prevent
# any changes being pushed to any server. Setting this flag to false will execute all steps excepts 
# the ones that pertain to commiting code, pushing tags or releasing versions. The variable can be found
# under our Library in Azure DevOps named Global-Variables:
# https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=VariableGroups
#
# Created by: <PERSON> Resha

variables:
  - name: pipelineVersion
    ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
      value: 'PR_'
    ${{ else }}:
      value: ''
  - name: buildDate
    value: $(date '+%Y%m%d')

name: $(pipelineVersion)$(buildDate)

stages:
  - stage: TestCatalystServer
    displayName: 'Automated Testing'
    jobs:
      - job: test
        displayName: 'Run automated-testing'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - task: UseNode@1
            inputs:
              version: $(nodeVersion)
            displayName: 'Install Node.js'
          
          - template: ./templates/stepsRunCatalystServerTests.yml
            parameters:
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: PublishTestResults@2
            displayName: 'Publish testing report'
            inputs:
              testRunner: JUnit
              testResultsFiles: '**/junit-reporter.xml'

  - stage: build
    displayName: 'Tag version and build'
    dependsOn: TestCatalystServer
    condition: and(ne(variables['Build.Reason'], 'PullRequest'), succeeded('TestCatalystServer'))
    jobs:
      - job: versionBuildRelease
        displayName: 'Checkout, Build and version the applications'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - checkout: self
            clean: true
            fetchTags: true
            persistCredentials: true
            name: 'checkoutBranch'
            displayName: 'Checkout source branch $(Build.SourceBranchName)'

          - task: UseNode@1
            inputs:
              version: $(nodeVersion)
            displayName: 'Install Node.js'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                git fetch --all
                git switch $(Build.SourceBranchName)
                git config user.email "<EMAIL>"
                git config user.name "alanresha"
                echo "Prune all previous built images"
                docker image prune -af
              workingDirectory: $(System.DefaultWorkingDirectory)
            name: fetchBrach
            displayName: 'Fetch branch from GitHub'

          - task: Npm@1
            displayName: "Install catalyst-innovation-client dependencies"
            inputs:
              command: custom
              customCommand: 'ci'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-innovation-client

          - task: Npm@1
            displayName: "Build catalyst-innovation-client"
            inputs:
              command: custom
              customCommand: 'run prod-build-web'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-innovation-client

          - task: Npm@1
            displayName: "Install catalyst-curator-client dependencies"
            inputs:
              command: custom
              customCommand: 'ci'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-curator-client

          - task: Npm@1
            displayName: "Build catalyst-curator-client"
            inputs:
              command: custom
              customCommand: 'run prod-build-web'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-curator-client

          - task: Npm@1
            displayName: "Install catalyst-server dependencies"
            inputs:
              command: ci
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                PREV_PACKAGE_VERSION=$(node -p "require('./package.json').version")
                npm version prerelease --preid patch
                PACKAGE_VERSION=$(node -p "require('./package.json').version")
                npm version ${PACKAGE_VERSION}
                TAG_PREFIX=$(node -p "require('./.versionrc.json')['tag-prefix']")
                TAG_NAME="$TAG_PREFIX$PACKAGE_VERSION"
                echo "Tag Name: $TAG_NAME"
                echo "##vso[task.setvariable variable=PREV_PACKAGE_VERSION;isoutput=true]$PREV_PACKAGE_VERSION"
                echo "##vso[task.setvariable variable=PACKAGE_VERSION;isoutput=true]$PACKAGE_VERSION"
                echo "##vso[task.setvariable variable=TAG_NAME;isoutput=true]$TAG_NAME"
                echo "##vso[build.updatebuildnumber]"${PACKAGE_VERSION}_$(buildDate)""
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: createGitHubTag
            displayName: 'Create GitHub tagged version'

          - task: Npm@1
            displayName: "Build catalyst-server"
            inputs:
              command: custom
              customCommand: 'run build'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Npm@1
            displayName: "Copy web folders into catalyst-server directory"
            inputs:
              command: custom
              customCommand: 'run copy-web-build'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: DownloadSecureFile@1
            name: testEnvFile
            displayName: 'Download test env file'
            inputs:
              secureFile: $(test_env_file)

          - task: DownloadSecureFile@1
            name: prodEnvFile
            displayName: 'Download prod env file'
            condition: eq(variables.runMigration, true)
            inputs:
              secureFile: $(prod_env_file)

          - task: CopyFiles@2
            displayName: 'Copy test env file'
            inputs:
              SourceFolder: '$(Agent.TempDirectory)'
              Contents: '$(test_env_file)'
              OverWrite: true
              TargetFolder: '$(System.DefaultWorkingDirectory)/catalyst-server'

          - task: CopyFiles@2
            name: copyProdEnv
            displayName: 'Copy prod env file'
            condition: eq(variables.runMigration, true)
            inputs:
              SourceFolder: '$(Agent.TempDirectory)'
              Contents: '$(prod_env_file)'
              OverWrite: true
              TargetFolder: '$(System.DefaultWorkingDirectory)/catalyst-server'

          - task: Npm@1
            displayName: "Set prod .env file"
            condition: eq(variables.runMigration, true)
            inputs:
              command: custom
              customCommand: 'run prod-env'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Remove migration folders. We want to create fresh copies of the migration each build."
                echo "The prod folder will be recreated and updated with a fresh script. This simplifies rollback."
                rm -r migrations || true
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: clearMigrationFolders
            displayName: 'Clear migration folders'
          
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Create migration scripts for prod."
                NODE_ENV=production npm run dbCli create-migrations
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: runMigrationProd
            displayName: 'Run prod create-migration'
            condition: eq(variables.runMigration, true)

          - task: Npm@1
            name: setTestEnv
            displayName: "Set test .env file"
            inputs:
              command: custom
              customCommand: 'run test-env'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Create migration scripts for test."
                NODE_ENV=production npm run dbCli create-migrations
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: runMigrationTest
            displayName: 'Run test create-migration'
            condition: eq(variables.runMigration, true)

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo 'Set cluster configs to new version: $(createGitHubTag.PACKAGE_VERSION)'
                echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-server-test.yml
                sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-server-test.yml
                echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-api-server-test.yml
                sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-api-server-test.yml
                echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-server-prod.yml
                sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-server-prod.yml
                echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-api-server-prod.yml
                sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(createGitHubTag.PACKAGE_VERSION)/g" ./catalyst-api-server-prod.yml
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server/deploy
              failOnStderr: true
            name: updateClusterConfigFiles
            displayName: 'Update cluster configs for new release'

          # NOTE: Uses deployChanges variable to prevenet pushing changes to GitHub
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                git status
                git add ./migrations/*.ts
                git commit -an -m "Commit build changes [skip ci]"
                git merge origin/$(Build.SourceBranchName) -m "Merge published tag changes [skip ci]"
                if [ $(deployChanges) = true ]; then
                  git push --follow-tags origin $(Build.SourceBranchName)
                fi
                git status
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: pushChanges
            displayName: 'Push git changes'

          - task: CopyFiles@2
            inputs:
              sourceFolder: '$(System.DefaultWorkingDirectory)/catalyst-server'
              contents: |
                **
                !cicd/*.md
                !cicd/images/*.png
                !node_modules/**
                !.env*
              targetFolder: '$(Build.ArtifactStagingDirectory)'
              CleanTargetFolder: true
            displayName: 'Copy files used for build'

          - task: PublishPipelineArtifact@1
            inputs:
              artifactName: catalyst-server-release
              targetPath: '$(Build.ArtifactStagingDirectory)'
              publishLocation: 'pipeline'
            displayName: 'Publish catalyst-server artifacts'

  # TODO: Name will need to be updated once test and staging are swapped.
  # Deploy changes to test and/or production environment. This will occur on approval. We only need
  # to pull the catalystTestCluster update files for this deployment as well as env file. Image is already deployed
  - stage: testRelease
    displayName: 'Test deployment'
    dependsOn: 
      - build
    condition: succeeded('build')
    jobs:
      - deployment: deployToTest
        displayName: 'Release patch updates to test'
        environment: catalyst-server-test-patch
        continueOnError: false
        variables:
          version: $[ stageDependencies.build.versionBuildRelease.outputs['createGitHubTag.PACKAGE_VERSION'] ]
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - task: UseNode@1
                  inputs:
                    version: $(nodeVersion)
                  displayName: 'Install Node.js'

                - download: current
                  artifact: catalyst-server-release
                
                - task: DownloadSecureFile@1
                  name: testEnvFile
                  displayName: 'Download env file from secure location in azure'
                  inputs:
                    secureFile: $(test_env_file)
                
                - task: CopyFiles@2
                  displayName: 'Copy env file that was downloaded to the deploy directory'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(test_env_file)'
                    OverWrite: true
                    TargetFolder: '$(Pipeline.Workspace)/catalyst-server-release/deploy/'

                - task: CopyFiles@2
                  name: copyTestEnvWorkingDir
                  displayName: 'Copy env file to working directory'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(test_env_file)'
                    OverWrite: true
                    TargetFolder: $(Pipeline.Workspace)/catalyst-server-release

                - task: Npm@1
                  displayName: "Set .env file"
                  inputs:
                    command: custom
                    customCommand: 'run test-env'
                    workingDir: $(Pipeline.Workspace)/catalyst-server-release

                - task: Npm@1
                  displayName: "Install catalyst-server dependencies"
                  inputs:
                    command: ci
                    workingDir: $(Pipeline.Workspace)/catalyst-server-release

                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      echo "Updating schema."
                      NODE_ENV=production npm run dbCli run-migrations
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-release
                  name: miroTestMigrateUp
                  displayName: 'Run Migration Up'
                  condition: and(eq(variables.deployChanges, true), eq(variables.runMigration, true))

                - template: ./templates/stepsBashUpdateAllTenantConfigs.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-release

                # NOTE: Uses the deployChanges enviroment variable to for pushing the new image to the repository
                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      az acr login --name catalystinnovation
                      docker build -f deploy/prod.Dockerfile -t catalyst-server:$(version) -t catalystinnovation.azurecr.us/catalyst-server:$(version) .
                      docker build -f deploy/prod.api.Dockerfile -t catalyst-api-server:$(version) -t catalystinnovation.azurecr.us/catalyst-api-server:$(version) .
                      if [ $(deployChanges) = true ]; then
                        docker push catalystinnovation.azurecr.us/catalyst-server:$(version)
                        docker push catalystinnovation.azurecr.us/catalyst-api-server:$(version)
                      fi
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-release
                  name: buildCatalystServerImages
                  displayName: 'Acr Login, Build and Push Catalyst Server and API images'

                # NOTE: Uses the deployChanges enviroment variable to determine if changes should be deployed
                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      echo 'Deploying Version: $(version)'
                      kubectl config use-context $(default_cluster)
                      kubectl config get-contexts
                      if [ $(deployChanges) = true ]; then
                        echo kubectl --context $(default_cluster) delete secret test-secrets
                        kubectl --context $(default_cluster) delete secret test-secrets
                        echo kubectl --context $(default_cluster) create secret generic test-secrets --from-env-file=$(test_env_file)
                        kubectl --context $(default_cluster) create secret generic test-secrets --from-env-file=$(test_env_file)
                        echo kubectl --context $(default_cluster) apply -f ./catalyst-server-test.yml
                        kubectl --context $(default_cluster) apply -f ./catalyst-server-test.yml
                        echo kubectl --context $(default_cluster) apply -f ./catalyst-api-server-test.yml
                        kubectl --context $(default_cluster) apply -f ./catalyst-api-server-test.yml
                      else
                        echo kubectl --context $(default_cluster) delete secret test-secrets
                        echo kubectl --context $(default_cluster) create secret generic test-secrets --from-env-file=$(test_env_file)
                        echo kubectl --context $(default_cluster) apply -f ./catalyst-server-test.yml
                        echo kubectl --context $(default_cluster) apply -f ./catalyst-api-server-test.yml
                      fi
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-release/deploy
                    failOnStderr: true
                  name: deployToTestCluster
                  displayName: 'Configure and Apply cluster update to catalystServerTestCluster'
                
  # Deploy changes to production environment. This will occur on approval. We only need
  # to pull the catalystCluster update files for this deployment as well as env file. Image is already deployed
  - stage: release
    displayName: 'Release to production'
    dependsOn: 
      - testRelease
    condition: succeeded('testRelease')
    jobs:
      - deployment: deployToProd
        displayName: 'Release updates to production'
        environment: catalyst-server-prod-patch
        continueOnError: false
        variables:
          version: $[ stageDependencies.build.versionBuildRelease.outputs['createGitHubTag.PACKAGE_VERSION'] ]
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - task: UseNode@1
                  inputs:
                    version: $(nodeVersion)
                  displayName: 'Install Node.js'

                - download: current
                  artifact: catalyst-server-release
                
                - task: DownloadSecureFile@1
                  name: prodEnvFile
                  displayName: 'Download env file from secure location in azure'
                  inputs:
                    secureFile: $(prod_env_file)

                - task: CopyFiles@2
                  name: copyProdEnvDeployDir
                  displayName: 'Copy env file that was downloaded to the deploy directory'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(prod_env_file)'
                    OverWrite: true
                    TargetFolder: '$(Pipeline.Workspace)/catalyst-server-release/deploy/'

                - task: CopyFiles@2
                  name: copyProdEnvWorkingDir
                  displayName: 'Copy env file to working directory'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(prod_env_file)'
                    OverWrite: true
                    TargetFolder: $(Pipeline.Workspace)/catalyst-server-release

                - task: Npm@1
                  displayName: "Set prod .env file"
                  inputs:
                    command: custom
                    customCommand: 'run prod-env'
                    workingDir: $(Pipeline.Workspace)/catalyst-server-release

                - task: Npm@1
                  displayName: "Install catalyst-server dependencies"
                  inputs:
                    command: ci
                    workingDir: $(Pipeline.Workspace)/catalyst-server-release

                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      echo "Updating schema."
                      NODE_ENV=production npm run dbCli run-migrations
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-release
                  name: miroProdMigrateUp
                  displayName: 'Run Migration Up'
                  condition: and(eq(variables.deployChanges, true), eq(variables.runMigration, true))

                - template: ./templates/stepsBashUpdateAllTenantConfigs.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-release

                # NOTE: Uses the deployChanges enviroment variable to determine if changes should be deployed
                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      echo 'Deploying Version: $(version)'
                      kubectl config use-context $(prod_cluster)
                      kubectl config get-contexts
                      if [ $(deployChanges) = true ]; then
                        echo kubectl --context $(prod_cluster) delete secret prod-secrets
                        kubectl --context $(prod_cluster) delete secret prod-secrets
                        echo kubectl --context $(prod_cluster) create secret generic prod-secrets --from-env-file=$(prod_env_file)
                        kubectl --context $(prod_cluster) create secret generic prod-secrets --from-env-file=$(prod_env_file)
                        echo kubectl --context $(prod_cluster) apply -f ./catalyst-server-prod.yml
                        kubectl --context $(prod_cluster) apply -f ./catalyst-server-prod.yml
                        echo kubectl --context $(prod_cluster) apply -f ./catalyst-api-server-prod.yml
                        kubectl --context $(prod_cluster) apply -f ./catalyst-api-server-prod.yml
                      else
                        echo kubectl --context $(prod_cluster) delete secret prod-secrets
                        echo kubectl --context $(prod_cluster) create secret generic prod-secrets --from-env-file=$(prod_env_file)
                        echo kubectl --context $(prod_cluster) apply -f $SCRIPT_DIR/../catalyst-server-prod.yml
                        echo kubectl --context $(prod_cluster) apply -f $SCRIPT_DIR/../catalyst-api-server-prod.yml
                      fi
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-release/deploy
                    failOnStderr: true
                  name: deployProdCluster
                  displayName: 'Configure and Apply cluster update to catalystServerCluster'

