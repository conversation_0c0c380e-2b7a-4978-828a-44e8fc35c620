# Manually Deploy Prebuilt Version
The [Manually Deploy Prebuilt Version](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=7) pipeline was setup to allow for pushing forward or rolling back an environments build. This manual pipeline does not build any code. It requires a prebuilt version for deployment to any environment.

Please note, this will only deploy any prebuilt catalyst-server or catalyst-server-api image. It will not update the database or schema in any way. Schema migrations and tenant configurations must be evaluated indidually on how to deploy a rollback or roll forward.


# Links
- [Manually Deploy Prebuilt Version](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=7)
- [Azure Devops Overview](./devops-overview.md)