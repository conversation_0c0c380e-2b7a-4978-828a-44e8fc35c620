# This yml file is used by Azure DevOps to execute our built in dbCli tool to add tenants to
# existing privilege groups for existing tenants. This provides a relationship between tenants for
# accessing certain information.
#
# This file should be used to add privilege groups for existing tenants using the dbCli tool located at src/core/utils/dbCli.ts
# The execution steps should leverage existing processes inside the cli tool.
#
# Testing: When testing the pipeline flow, set "deployChanges" to false. This will prevent
# any changes being pushed to any server. Setting this flag to false will execute all steps excepts 
# the ones that pertain to committing code, pushing tags or releasing versions. The variable can be found
# under our Library in Azure DevOps named Global-Variables:
# https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=VariableGroups
#
# Created by: Alan Resha

parameters:
- name: tenantHandle
  displayName: Enter the EXISTING tenant handle of the privilege group you want to add a new tenant(s) to.
  type: string
  default: ""

- name: privilegeGroupName
  displayName: Enter the privilege group name you want to add the tenant(s) to.
  type: string
  default: 'Reporting Tenant Access'
  values:
  - 'Analyst Access'
  - 'Reporting Tenant Access'

- name: tenantsToAdd
  displayName: Enter the tenant handle(s) you want to associate with this privilege group. Add a space between each tenant handle.
  type: string
  default: ""

trigger: none

variables:
  - name: pipelineVersion
    value: 'Add_Privilege_Group_Tenant_Handle'
  - name: groupHandle
    value: ''
  - name: buildDate
    value: $(date '+%Y%m%d')
  - name: artifactName
    value: 'catalyst-server-add-tenant-to-privilege-group'

name: $(pipelineVersion)${groupHandle}$(buildDate)

stages:
  - stage: build
    displayName: 'Create Artifacts'
    jobs:
      - job: createArtifacts
        displayName: 'Checkout source and archive'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - template: ../templates/stepsCheckoutAndPullBranch.yml

          - template: ../templates/stepsBashSetPipelineName.yml
            parameters:
              descriptionPrefix: "add_to_privilege_group_${{ parameters.tenantHandle }}_${{ parameters.tenantsToAdd }}"
          
          - template: ../templates/stepsArchiveFiles.yml
            parameters:
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
              artifactName: $(artifactName)

  - stage: stagingRelease
    displayName: 'Staging deployment'
    dependsOn: 
      - build
    condition: succeeded('build')
    jobs:
      - deployment: deployStaging
        displayName: 'Add tenant privileges to staging'
        environment: catalyst-server-staging
        continueOnError: false
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'staging'

                - template: ../templates/stepsBashAddTenantToPrivilegeGroupByName.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ parameters.tenantHandle }}
                    privilegeGroupName: "${{ parameters.privilegeGroupName }}"
                    tenantsToAdd: "${{ parameters.tenantsToAdd }}"

  - stage: testRelease
    displayName: 'Release to test'
    dependsOn:
      - build
      - stagingRelease
    condition: succeeded('stagingRelease')
    jobs:
      - deployment: deployToTest
        displayName: 'Add tenant privileges to test'
        environment: catalyst-server-test
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'test'

                - template: ../templates/stepsBashAddTenantToPrivilegeGroupByName.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ parameters.tenantHandle }}
                    privilegeGroupName: "${{ parameters.privilegeGroupName }}"
                    tenantsToAdd: "${{ parameters.tenantsToAdd }}"

  - stage: release
    displayName: 'Release to production'
    dependsOn:
      - build
      - testRelease
    condition: succeeded('testRelease')
    jobs:
      - deployment: deployToProd
        displayName: 'Add tenant privileges to production'
        environment: catalyst-server-prod
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'production'

                - template: ../templates/stepsBashAddTenantToPrivilegeGroupByName.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ parameters.tenantHandle }}
                    privilegeGroupName: "${{ parameters.privilegeGroupName }}"
                    tenantsToAdd: "${{ parameters.tenantsToAdd }}"
