# Support Pipelines
The files located in this support directory are used to create pipelines that assist the innovation platform. Each file has an associated pipeline in our DevOps cloud environment. This README will outline the file usage and associated pipeline for clarity and use.

The current support pipelines focus on the following areas:
1. Adding new tenants to the innovation platform
2. Adding tenants to existing privilege groups
3. Creating new privilege groups for tenants

Each pipeline follows the same process for deployment as our Innovation client CICD pipeline. The only exception is, the pipelines under support do not execute automated tests.

![img](../images/deployment_support.png)

All support pipelines are associated with files in this support directory. The pipelines are located at: [DevOps Support Pipelines](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionScope=%5Ccatalyst-server%5CSupport)

## Environment
Pipelines should be associated with an Environment inside DevOps. DevOps Environments assists in tracking environment changes, utilized resources and Approval workflows. The support pipelines leverage the same environment as our build process. The pipelines should follow the same cadence as a build/release process because it has the potential to impact existing tenants. These pipelines should be tested for accuracy after they are executed. A list of our environments in DevOps can be found [here](https://dev.azure.com/ACME-General/catalyst-innovation/_environments).

## Tenant Support
The pipelines and associated files listed here assist in managing the creation of tenants and view access into tenants. Examples in this section are based on seed data we use for our development environments.

- [Add New Tenant](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=15)
    - When adding a new tenant, changes must be made in our source control to introduce the new tenant. This requires adding a new folder under ./config/tenants/${ newTenantHandle }/ before running the job. The folder must match the tenantHandle added to the system for lookup purposes.
        - Example: ./config/tenants/monumentsMen/

    - Input Parameters:
        - newTenantHandle: This parameter will be used to identify the tenant in our system. 
            - Example: monumentsMen
        - newTenantName: This parameter will be used as the user friendly name of the tenant. 
            - Example: The Monuments Men

    - The pipeline will use the input parameters to execute three dbCli utility functions.
        - tenant: This function will create the tenant in our system and the configuration files in source control to setup the clients configuration parameters for UI context and theme. 
        - update-serverConfig: This function will push the tenants ./config/tenants/${YOUR NEW TENANT HANDLE}/serverConfig.json file into the database to be referenced in code.
        - update-content: This function will push the tenants content.json file into the database to be referenced in code. If the file isn't present, it takes the root default under ./config/content/content.json

- [Add Tenant to Privilege Group](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=16):
    - Before you can add a tenant to an existing tenant grouping, your tenant must already exist in the system.
    - Input Parameters:

        - tenantHandle: This parameter is the tenant handle associated with existing privilege grouping you want use for adding new tenant associations.
            - Example: monumentsMen
        - privilegeGroupName: A tenant can have multiple privilege groups. This name identifies the privilege group you want to associate with.
            - Note: Currently only 2 group names are used, Analyst Access and Reporting Tenant Access. Only one tenant uses Reporting Tenant Access. All other tenants have Analyst Access. The current default is Reporting Tenant Access.
        - tenantsToAdd: This input will be associated with the tenantHandle input privilege group. You can add multiple tenant handles by adding a space between each tenantsToAdd tenant handles.
            - Example: starshipTroopers
            - Example: starshipTroopers myNewTenant mySecondNewTenant

    - The pipeline will use the input parameters to execute one dbCli utility function.
        - add-to-tenant-privilege-group-by-name: This function will update the privilege group associations for the tenantHandle input. This extends the tenantHandle input associations by adding the tenantsToAdd tenant handles.
        
- [Create New Tenant Privilege Group](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=17):
    - Before you can create a tenant privilege group, your tenant must already exist in the system.
    - Input Parameters:

        - tenantHandle: This parameter is the tenant handle for the new privilege group.
            - Example: monumentsMen
        - privilegeGroupName: A tenant can have multiple privilege groups. This name identifies the privilege group you want to associate other tenants to.
            - Note: Currently only 2 group names are used, Analyst Access and Reporting Tenant Access. Only one tenant uses Reporting Tenant Access. All other tenants have Analyst Access. The current default is Reporting Tenant Access.
        - tenantsToAdd (optional): This input is optional. If you have other existing tenants you want to associate with this grouping, you can add them here. Add a single tenant or multiple tenant handles by adding a space between each tenantsToAdd tenant handle.
            - Example: starshipTroopers
            - Example: starshipTroopers myNewTenant mySecondNewTenant

    - The pipeline will use the input parameters to execute one dbCli utility function.
        - tenant-privilege-group: This function will create the privilege group for the tenantHandle input. Similar to "add-to-tenant-privilege-group-by-name" if you include tenant handles in tenantsToAdd input, they will be automatically associated with this grouping.