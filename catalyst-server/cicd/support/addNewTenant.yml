# This yml file is used by Azure DevOps to execute our built in dbCli tool for adding new tenants
# for the catalyst-innovation environments.
#
# This file should be used to setup new tenants using the dbCli tool located at src/core/utils/dbCli.ts
# The execution steps should leverage existing processes inside the cli tool with no direct database access
# for the user running the pipeline..
#
# Testing: When testing the pipeline flow, set "deployChanges" to false. This will prevent
# any changes being pushed to any server. Setting this flag to false will execute all steps excepts 
# the ones that pertain to commiting code, pushing tags or releasing versions. The variable can be found
# under our Library in Azure DevOps named Global-Variables:
# https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=VariableGroups
#
# Created by: <PERSON>sha

parameters:
- name: newTenantHandle
  displayName: Enter the Tenant Handle. Example monumentsMen
  type: string
  default: ""

- name: newTenantName
  displayName: Enter the Tenant Name. Example The Monuments Men
  type: string
  default: ""

trigger: none

variables:
  - name: pipelineVersion
    value: 'Add_Tenant'
  - name: buildDate
    value: $(date '+%Y%m%d')
  - name: artifactName
    value: 'catalyst-server-new-tenant'

name: $(pipelineVersion)$(buildDate)

stages:
  - stage: build
    displayName: 'Create Artifacts'
    jobs:
      - job: createArtifacts
        displayName: 'Checkout source and archive'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - template: ../templates/stepsCheckoutAndPullBranch.yml

          - template: ../templates/stepsBashSetPipelineName.yml
            parameters:
              descriptionPrefix: "create-tenant_${{ parameters.newTenantHandle }}"
          
          - template: ../templates/stepsArchiveFiles.yml
            parameters:
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
              artifactName: $(artifactName)

  - stage: stagingRelease
    displayName: 'Staging deployment'
    dependsOn: 
      - build
    condition: succeeded('build')
    jobs:
      - deployment: deployStaging
        displayName: 'Deploy tenant to staging'
        environment: catalyst-server-staging
        continueOnError: false
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'staging'
                    
                - template: ../templates/stepsBashAddNewTenant.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    newTenantHandle: ${{ parameters.newTenantHandle}}
                    newTenantName: "${{ parameters.newTenantName}}"

  - stage: testRelease
    displayName: 'Release to test'
    dependsOn:
      - build
      - stagingRelease
    condition: succeeded('stagingRelease')
    jobs:
      - deployment: deployToTest
        displayName: 'Release tenant to test'
        environment: catalyst-server-test
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none
                
                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'test'
                    
                - template: ../templates/stepsBashAddNewTenant.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    newTenantHandle: ${{ parameters.newTenantHandle}}
                    newTenantName: "${{ parameters.newTenantName}}"

  - stage: release
    displayName: 'Release to production'
    dependsOn:
      - build
      - testRelease
    condition: succeeded('testRelease')
    jobs:
      - deployment: deployToProd
        displayName: 'Release tenant to production'
        environment: catalyst-server-prod
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none
                
                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'production'
                    
                - template: ../templates/stepsBashAddNewTenant.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    newTenantHandle: ${{ parameters.newTenantHandle}}
                    newTenantName: "${{ parameters.newTenantName}}"
                    