# This yml file is used by Azure DevOps to execute our built in dbCli tool for removing tenant
# privilege groups from existing privilege groups for the catalyst-innovation environments.
#
# This file should be used to setup new tenants using the dbCli tool located at src/core/utils/dbCli.ts
# The execution steps should leverage existing processes inside the cli tool.
#
# Testing: When testing the pipeline flow, set "deployChanges" to false. This will prevent
# any changes being pushed to any server. Setting this flag to false will execute all steps excepts 
# the ones that pertain to committing code, pushing tags or releasing versions. The variable can be found
# under our Library in Azure DevOps named Global-Variables:
# https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=VariableGroups
#
# Created by: Alan Resha

parameters:
- name: tenantHandle
  displayName: Enter the Tenant Handle for the top level privilege group. Good example is afc or pathfinder.
  type: string
  default: ""

- name: privilegeGroupId
  displayName: Enter the privilegeGroupId for the tenant handle you want to remove. Good example is efe2a1e4-3b3b-4e8a-a586-039aae539f26
  type: string
  default: ""

- name: tenantHandleToRemove
  displayName: Enter the tenant handles you want to remove from this group. Add a space between each tenant handle.
  type: string
  default: ""

trigger: none

variables:
  - name: pipelineVersion
    value: 'Add_Tenant'
  - name: tenantName
    value: ''
  - name: buildDate
    value: $(date '+%Y%m%d')
  - name: artifactName
    value: 'catalyst-server-privilege-removal'

name: $(pipelineVersion)${tenantName}$(buildDate)

stages:
  - stage: build
    displayName: 'Create Artifacts'
    jobs:
      - job: createArtifacts
        displayName: 'Checkout source and archive'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - template: ../templates/stepsCheckoutAndPullBranch.yml

          - template: ../templates/stepsBashSetPipelineName.yml
            parameters:
              descriptionPrefix: "remove_from_privilege_group_${{ parameters.tenantHandleToRemove }}"
          
          - template: ../templates/stepsArchiveFiles.yml
            parameters:
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
              artifactName: $(artifactName)

  - stage: stagingRelease
    displayName: 'Staging deployment'
    dependsOn: 
      - build
    condition: succeeded('build')
    jobs:
      - deployment: deployStaging
        displayName: 'Remove tenant privilege group in staging'
        environment: catalyst-server-staging
        continueOnError: false
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'staging'

                - template: ../templates/stepsBashRemoveTenantFromPrivilegeGroup.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ paramaters.tenantHandle }}
                    privilegeGroupId: ${{ paramaters.privilegeGroupId }}
                    tenantHandlesToRemove: ${{ paramaters.tenantHandeToRemove }}

  - stage: testRelease
    displayName: 'Release to test'
    dependsOn:
      - build
      - stagingRelease
    condition: succeeded('stagingRelease')
    jobs:
      - deployment: deployToTest
        displayName: 'Remove tenant privilege group in test'
        environment: catalyst-server-test
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'test'

                - template: ../templates/stepsBashRemoveTenantFromPrivilegeGroup.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ paramaters.tenantHandle }}
                    privilegeGroupId: ${{ paramaters.privilegeGroupId }}
                    tenantHandlesToRemove: ${{ paramaters.tenantHandeToRemove }}

  - stage: release
    displayName: 'Release to production'
    dependsOn:
      - build
      - testRelease
    condition: succeeded('testRelease')
    jobs:
      - deployment: deployToProd
        displayName: 'Remove tenant privilege group in production'
        environment: catalyst-server-prod
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'production'

                - template: ../templates/stepsBashRemoveTenantFromPrivilegeGroup.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ paramaters.tenantHandle }}
                    privilegeGroupId: ${{ paramaters.privilegeGroupId }}
                    tenantHandlesToRemove: ${{ paramaters.tenantHandeToRemove }}
