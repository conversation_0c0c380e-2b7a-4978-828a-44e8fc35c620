# This yml file is used by Azure DevOps to execute our built in dbCli tool to create a privilege group
# for an existing tenant.
#
# This file should be used to create a privilege group for an existing tenant using the dbCli tool located at src/core/utils/dbCli.ts
# The execution steps should leverage existing processes inside the cli tool.
#
# Testing: When testing the pipeline flow, set "deployChanges" to false. This will prevent
# any changes being pushed to any server. Setting this flag to false will execute all steps excepts 
# the ones that pertain to committing code, pushing tags or releasing versions. The variable can be found
# under our Library in Azure DevOps named Global-Variables:
# https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=VariableGroups
#
# Created by: Alan <PERSON>sha

parameters:
- name: tenantHandle
  displayName: Enter the tenant handle for this privilege group.
  type: string
  default: ""

- name: privilegeGroupName
  displayName: Enter the name of the privilege group.
  type: string
  default: 'Reporting Tenant Access'
  values:
  - 'Analyst Access'
  - 'Reporting Tenant Access'

- name: tenantsToAdd
  displayName: Enter additional existing tenant handle(s) you want associated with this privilege group if any. Add a space between each tenant handle.
  type: string
  default: ""

trigger: none

variables:
  - name: pipelineVersion
    value: 'Add_Privilege_Group_Tenant_Handle'
  - name: groupHandle
    value: ''
  - name: buildDate
    value: $(date '+%Y%m%d')
  - name: artifactName
    value: 'catalyst-server-create-tenant-privilege-group'

name: $(pipelineVersion)${groupHandle}$(buildDate)

stages:
  - stage: build
    displayName: 'Create Artifacts'
    jobs:
      - job: createArtifacts
        displayName: 'Checkout source and archive'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - template: ../templates/stepsCheckoutAndPullBranch.yml

          - template: ../templates/stepsBashSetPipelineName.yml
            parameters:
              descriptionPrefix: "create-privilege_group_for_${{ parameters.tenantHandle }}"
          
          - template: ../templates/stepsArchiveFiles.yml
            parameters:
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
              artifactName: $(artifactName)

  - stage: stagingRelease
    displayName: 'Staging deployment'
    dependsOn: 
      - build
    condition: succeeded('build')
    jobs:
      - deployment: deployStaging
        displayName: 'Add tenant privilege group to staging'
        environment: catalyst-server-staging
        continueOnError: false
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'staging'

                - template: ../templates/stepsBashCreateTenantPrivilegeGroup.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ parameters.tenantHandle }}
                    privilegeGroupName: ${{ parameters.privilegeGroupName }}
                    tenantsToAdd: ${{ parameters.tenantsToAdd }}

  - stage: testRelease
    displayName: 'Release to test'
    dependsOn:
      - build
      - stagingRelease
    condition: succeeded('stagingRelease')
    jobs:
      - deployment: deployToTest
        displayName: 'Add tenant privilege group to test'
        environment: catalyst-server-test
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'test'

                - template: ../templates/stepsBashCreateTenantPrivilegeGroup.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ parameters.tenantHandle }}
                    privilegeGroupName: ${{ parameters.privilegeGroupName }}
                    tenantsToAdd: ${{ parameters.tenantsToAdd }}

  - stage: release
    displayName: 'Release to production'
    dependsOn:
      - build
      - testRelease
    condition: succeeded('testRelease')
    jobs:
      - deployment: deployToProd
        displayName: 'Add tenant privilege group to production'
        environment: catalyst-server-prod
        continueOnError: false
        workspace:
          clean: outputs
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - template: ../templates/stepsNpmInstallForPath.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)

                - template: ../templates/stepsSetCatalystServerEnvironment.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    environment: 'production'

                - template: ../templates/stepsBashCreateTenantPrivilegeGroup.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/$(artifactName)
                    tenantHandle: ${{ parameters.tenantHandle }}
                    privilegeGroupName: ${{ parameters.privilegeGroupName }}
                    tenantsToAdd: ${{ parameters.tenantsToAdd }}