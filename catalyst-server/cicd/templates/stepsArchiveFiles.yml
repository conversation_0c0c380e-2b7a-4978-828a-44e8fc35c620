parameters:
  - name: workingDirectory
    type: string
  - name: artifactName
    type: string

steps:
  - task: CopyFiles@2
    inputs:
      sourceFolder: '${{ parameters.workingDirectory }}'
      contents: |
        **
        !cicd/*.md
        !cicd/images/*.png
        !node_modules/**
        !.env*
      targetFolder: '$(Build.ArtifactStagingDirectory)'
      CleanTargetFolder: true
    displayName: 'Copy files used for build'

  - task: PublishPipelineArtifact@1
    inputs:
      artifactName: '${{ parameters.artifactName }}'
      targetPath: '$(Build.ArtifactStagingDirectory)'
      publishLocation: 'pipeline'
    displayName: 'Publish catalyst-server artifacts'