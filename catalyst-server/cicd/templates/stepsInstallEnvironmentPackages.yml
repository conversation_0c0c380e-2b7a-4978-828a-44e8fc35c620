parameters:
  - name: workingDirectory
    type: string
  - name: installOnly
    displayName: Install environment packages for one or all projects
    type: string
    default: all
    values:
    - all
    - catalyst-server
    - catalyst-innovation-client
    - catalyst-curator-client

steps:
  - task: UseNode@1
    inputs:
      version: $(nodeVersion)
    displayName: 'Install Node.js'

  - ${{ if or( eq(parameters.installOnly, 'all'), eq(parameters.installOnly, 'catalyst-innovation-client')) }}:
    - template: ./stepsNpmInstallForPath
      parameters:
        workingDirectory: ${{ parameters.workingDirectory }}/catalyst-innovation-client

  - ${{ if or( eq(parameters.installOnly, 'all'), eq(parameters.installOnly, 'catalyst-curator-client')) }}:
    - template: ./stepsNpmInstallForPath
      parameters:
        workingDirectory: ${{ parameters.workingDirectory }}/catalyst-curator-client

  - ${{ if or( eq(parameters.installOnly, 'all'), eq(parameters.installOnly, 'catalyst-server')) }}:
    - template: ./stepsNpmInstallForPath
      parameters:
        workingDirectory: ${{ parameters.workingDirectory }}/catalyst-server
