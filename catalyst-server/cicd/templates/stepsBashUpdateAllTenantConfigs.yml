# Steps to update all tenant configs

parameters:
  - name: workingDirectory
    type: string
    default: $(System.DefaultWorkingDirectory)/catalyst-server

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        echo "Updating content, config, serverConfig and theme database files."
        echo "NODE_ENV=production npm run dbCli update-all-tenant-configs config/tenants/"
        if [ $(deployChanges) = true ]; then
          NODE_ENV=production npm run dbCli update-all-tenant-configs config/tenants/
        fi
      workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Update tenant meta data'