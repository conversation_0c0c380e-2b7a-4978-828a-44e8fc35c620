parameters:
  - name: workingDirectory
    type: string

steps:
  - template: ./stepsInstallEnvironmentPackages.yml
    parameters:
      workingDirectory: ${{ parameters.workingDirectory }}

  - task: Npm@1
    displayName: "Build catalyst-innovation-client"
    inputs:
      command: custom
      customCommand: 'run prod-build-web'
      workingDir: ${{ parameters.workingDirectory }}/catalyst-innovation-client

  - task: Npm@1
    displayName: "Build catalyst-curator-client"
    inputs:
      command: custom
      customCommand: 'run prod-build-web'
      workingDir: ${{ parameters.workingDirectory }}/catalyst-curator-client

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        PREV_PACKAGE_VERSION=$(node -p "require('./package.json').version")
        npm run release
        PACKAGE_VERSION=$(node -p "require('./package.json').version")
        npm version ${PACKAGE_VERSION}
        TAG_PREFIX=$(node -p "require('./.versionrc.json')['tag-prefix']")
        TAG_NAME="$TAG_PREFIX$PACKAGE_VERSION"
        echo "Tag Name: $TAG_NAME"
        echo "##vso[task.setvariable variable=PREV_PACKAGE_VERSION;isoutput=true]$PREV_PACKAGE_VERSION"
        echo "##vso[task.setvariable variable=PACKAGE_VERSION;isoutput=true]$PACKAGE_VERSION"
        echo "##vso[task.setvariable variable=TAG_NAME;isoutput=true]$TAG_NAME"
        echo "##vso[build.updatebuildnumber]"${PACKAGE_VERSION}_$(buildDate)""
      workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
    name: createGitHubTag
    displayName: 'Create GitHub tagged version'

  - task: Npm@1
    displayName: "Build catalyst-server"
    inputs:
      command: custom
      customCommand: 'run tsoa'
      workingDir: ${{ parameters.workingDirectory }}/catalyst-server

  - task: Npm@1
    displayName: "Build catalyst-server"
    inputs:
      command: custom
      customCommand: 'run build'
      workingDir: ${{ parameters.workingDirectory }}/catalyst-server

  - task: Npm@1
    displayName: "Copy web folders into catalyst-server directory"
    inputs:
      command: custom
      customCommand: 'run copy-web-build'
      workingDir: ${{ parameters.workingDirectory }}/catalyst-server
