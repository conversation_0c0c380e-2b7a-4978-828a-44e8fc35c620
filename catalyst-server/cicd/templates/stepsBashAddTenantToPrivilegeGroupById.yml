# Steps to set the environment up to point to staging

parameters:
  - name: workingDirectory
    type: string
  - name: tenantHandle
    type: string
  - name: privilegeGroupId
    type: string
  - name: addTenantHandles
    type: string

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
          echo "Adding tenant to ${{ parameters.tenantHandle }} privelege group for ${{ parameters.addTenantHandles }}..."
          echo "NODE_ENV=production npm run dbCli add-to-tenant-privilege-group ${{ parameters.tenantHandle }} ${{ parameters.privilegeGroupId }} ${{ parameters.addTenantHandles }}"
          if [ $(deployChanges) = true ]; then
            NODE_ENV=production npm run dbCli add-to-tenant-privilege-group ${{ parameters.tenantHandle }} "${{ parameters.privilegeGroupId }}" ${{ parameters.addTenantHandles }}
          fi
      workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Adding to privilege groups ${{ parameters.tenantHandle }}'