# Publish artifacts for pipeline

parameters:
  - name: sourceFolder
    type: string
  - name: artifactName
    type: string

steps:
  - task: CopyFiles@2
    inputs:
      sourceFolder: ${{ parameters.sourceFolder }}
      contents: |
        **
        !cicd/*.md
        !cicd/images/*.png
        !node_modules/**
        !.env*
      targetFolder: '$(Build.ArtifactStagingDirectory)'
      CleanTargetFolder: true
    displayName: 'Copy source files'

  - task: PublishPipelineArtifact@1
    inputs:
      artifactName: ${{ parameters.artifactName }}
      targetPath: '$(Build.ArtifactStagingDirectory)'
      publishLocation: 'pipeline'
    displayName: 'Publish Pipeline Artifacts'
