# Steps to set add a new tenant alias

parameters:
  - name: workingDirectory
    type: string
  - name: tenantHandle
    type: string
    default: 'monumentsMen'
  - name: aliasTenantHandle
    type: string
  - name: aliasTenantName
    type: string

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        tenantHandle=${{ parameters.tenantHandle }}
        aliasTenantHandle=${{ parameters.aliasTenantHandle }}
        aliasTenantName="${{ parameters.aliasTenantName }}"
        echo "Setup tenant alias $aliasTenantHandle $aliasTenantName for $tenantHandle..."
        echo "NODE_ENV=production npm run dbCli tenant-alias $tenantHandle $aliasTenantHandle $aliasTenantName"
        echo "NODE_ENV=production npm run dbCli update-config $tenantHandle config/tenants/$aliasTenantHandle/config.json $aliasTenantHandle"
        echo "NODE_ENV=production npm run dbCli update-theme $tenantHandle config/tenants/$aliasTenantHandle/theme.json $aliasTenantHandle"
        echo "NODE_ENV=production npm run dbCli update-serverConfig $tenantHandle config/tenants/$aliasTenantHandle/serverConfig.json $aliasTenantHandle"
        echo "NODE_ENV=production npm run dbCli update-content $tenantHandle config/content/content.json $aliasTenantHandle"
        if [ $(deployChanges) = true ]; then
          NODE_ENV=production npm run dbCli tenant-alias $tenantHandle $aliasTenantHandle "$aliasTenantName"
          NODE_ENV=production npm run dbCli update-config $tenantHandle config/tenants/$aliasTenantHandle/config.json $aliasTenantHandle
          NODE_ENV=production npm run dbCli update-theme $tenantHandle config/tenants/$aliasTenantHandle/theme.json $aliasTenantHandle
          NODE_ENV=production npm run dbCli update-serverConfig $tenantHandle config/tenants/$aliasTenantHandle/serverConfig.json $aliasTenantHandle
          NODE_ENV=production npm run dbCli update-content $tenantHandle config/content/content.json $aliasTenantHandle
        fi
      bashEnvValue: '.env'
      workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Setup new tenant alias'