# Steps to set add a new tenant

parameters:
  - name: workingDirectory
    type: string
  - name: newTenantHandle
    type: string
  - name: newTenantName
    type: string

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        newTenantHandle=${{ parameters.newTenantHandle }}
        newTenantName="${{ parameters.newTenantName }}"
        PART_START=$(echo $newTenantHandle | cut -c 1-3 | rev)
        PART_END=$(admin_pwd_end)
        ADMIN="$PART_START$PART_END"
        echo "Info: "
        echo "Setup tenant $newTenantHandle $newTenantName..."
        echo "NODE_ENV=production npm run dbCli tenant $newTenantName $newTenantHandle $ADMIN config/tenants/$newTenantHandle/config.json config/tenants/$newTenantHandle/theme.json"
        echo "NODE_ENV=production npm run dbCli update-serverConfig $newTenantHandle config/tenants/$newTenantHandle/serverConfig.json"
        echo "NODE_ENV=production npm run dbCli update-content $newTenantHandle config/content/content.json"
        if [ $(deployChanges) = true ]; then
          NODE_ENV=production npm run dbCli tenant "$newTenantName" $newTenantHandle "$ADMIN" config/tenants/$newTenantHandle/config.json config/tenants/$newTenantHandle/theme.json "$newTenantName"
          NODE_ENV=production npm run dbCli update-serverConfig $newTenantHandle config/tenants/$newTenantHandle/serverConfig.json
          NODE_ENV=production npm run dbCli update-content $newTenantHandle config/content/content.json
        fi
      workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Setup new tenant ${{ parameters.newTenantName }}'