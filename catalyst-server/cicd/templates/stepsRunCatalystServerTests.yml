# Publish artifacts for pipeline

parameters:
  - name: workingDirectory
    type: string

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        isActive=$(sudo systemctl is-active postgresql)
        echo "Postgres status: ${isActive}"
        if [ $isActive = "active" ]; then
          echo "Stoping postgres"
          sudo service postgresql stop
        fi
        echo "##vso[build.updatebuildnumber]"$(pipelineVersion)$(buildDate)""
        echo '$(Build.BuildNumber)'
      workingDirectory: ${{ parameters.workingDirectory }}
    name: stopPostgres
    displayName: 'Stop Postgres if active'
    continueOnError: true

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        docker-compose up -d
      workingDirectory: ${{ parameters.workingDirectory }}
    name: setupTestDb
    displayName: 'Setup Postgres DB'

  - task: Npm@1
    displayName: 'Install dependencies'
    inputs:
      command: 'ci'
      workingDir: ${{ parameters.workingDirectory }}

  - task: DownloadSecureFile@1
    name: devEnvFile
    displayName: 'Download dev env file'
    inputs:
      secureFile: $(dev_env_file)
  
  - task: CopyFiles@2
    displayName: 'Copy env file'
    inputs:
      SourceFolder: '$(Agent.TempDirectory)'
      Contents: '$(dev_env_file)'
      OverWrite: true
      TargetFolder: ${{ parameters.workingDirectory }}

  - task: Npm@1
    displayName: "Set .env file"
    inputs:
      command: custom
      customCommand: 'run dev-env'
      workingDir: ${{ parameters.workingDirectory }}

  - task: Npm@1
    displayName: "Bootstrap Database"
    inputs:
      command: custom
      customCommand: 'run bootstrap-db'
      workingDir: ${{ parameters.workingDirectory }}
      
  - task: Npm@1
    displayName: "Execute tests for catalyst-server"
    name: testCoverage
    condition: eq(variables.executeTests, true)
    inputs:
      command: custom
      customCommand: 'run test:coverage'
      workingDir: ${{ parameters.workingDirectory }}

  - task: Bash@3
    condition: always()
    inputs:
      targetType: 'inline'
      script: |
        docker-compose down
      workingDirectory: ${{ parameters.workingDirectory }}
    name: tearDownTestDb
    displayName: 'Tear down postgres db'
