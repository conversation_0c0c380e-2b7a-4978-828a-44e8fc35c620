# Steps to set the environment up to point to staging

parameters:
  - name: targetFolder
    type: string
  - name: envFile
    type: string

steps:
  - task: DownloadSecureFile@1
    displayName: 'Download staging env file'
    inputs:
      secureFile: ${{ parameters.envFile }}
  
  - task: CopyFiles@2
    displayName: 'Copy env file'
    inputs:
      SourceFolder: '$(Agent.TempDirectory)'
      Contents: ${{ parameters.envFile }}
      OverWrite: true
      TargetFolder: ${{ parameters.targetFolder }}