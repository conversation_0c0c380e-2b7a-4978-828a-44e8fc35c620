# Steps to set the environment up to point to staging

parameters:
  - name: workingDirectory
    type: string
  - name: environment
    type: string
    default: staging
    values:
    - staging
    - test
    - production

steps:
  # USING STAGING AS THE ENVIRONMENT
  - ${{ if eq(parameters.environment, 'staging') }}: 
    - template: stepsGetEnvironmentFile.yml
      parameters:
        targetFolder: ${{ parameters.workingDirectory }}
        envFile: $(staging_env_file)
        
    - task: Npm@1
      displayName: "Set .env file"
      inputs:
        command: custom
        customCommand: 'run staging-env'
        workingDir: ${{ parameters.workingDirectory }}

  # USING TEST AS THE ENVIRONMENT
  - ${{ if eq(parameters.environment, 'test') }}: 
    - template: stepsGetEnvironmentFile.yml
      parameters:
        targetFolder: ${{ parameters.workingDirectory }}
        envFile: $(test_env_file)

    - task: Npm@1
      displayName: "Set .env file"
      inputs:
        command: custom
        customCommand: 'run test-env'
        workingDir: ${{ parameters.workingDirectory}}

  # USING PRODUCTION AS THE ENVIRONMENT
  - ${{ if eq(parameters.environment, 'production') }}: 
    - template: stepsGetEnvironmentFile.yml
      parameters:
        targetFolder: ${{ parameters.workingDirectory }}
        envFile: $(prod_env_file)

    - task: Npm@1
      displayName: "Set .env file"
      inputs:
        command: custom
        customCommand: 'run prod-env'
        workingDir: ${{ parameters.workingDirectory}}

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        echo "Loading .env file..."
        while IFS== read -r line; do
            if [[ "$line" =~ ^[A-Za-z_]+[A-Za-z0-9_]*= ]]; then
                key=$(echo "$line" | cut -d'=' -f1)
                value=$(echo "$line" | cut -d'=' -f2-) # Get the whole value
                if [ $key == "POSTGRES_PASSWORD" ]; then
                    echo "##vso[task.setvariable variable=$key;issecret=true]$value"
                elif [ $key = "POSTGRES_PASSWORD" ]; then
                    echo "##vso[task.setvariable variable=$key;issecret=true]$value"
                elif [ $key = "JWT_SECRET" ]; then
                    echo "##vso[task.setvariable variable=$key;issecret=true]$value"
                elif [ $key = "EP_ENDPOINT" ]; then
                    echo "##vso[task.setvariable variable=$key;issecret=true]$value"
                elif [ $key = "STORAGE_SECRET" ]; then
                    echo "##vso[task.setvariable variable=$key;issecret=true]$value"
                else
                    echo "##vso[task.setvariable variable=$key]$value"
                fi
            fi
        done < .env
      workingDirectory: ${{ parameters.workingDirectory}}
    name: envValues
    displayName: 'Set environment values'
    