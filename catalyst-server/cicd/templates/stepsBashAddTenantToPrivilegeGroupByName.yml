# Steps to set the environment up to point to staging

parameters:
  - name: workingDirectory
    type: string
  - name: tenantHandle
    type: string
  - name: privilegeGroupName
    type: string
    default: 'Reporting Tenant Access'
    values:
    - 'Analyst Access'
    - 'Reporting Tenant Access'
  - name: tenantsToAdd
    type: string

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
          echo "Adding tenant to ${{ parameters.tenantHandle }} privilege group for ${{ parameters.tenantsToAdd }}..."
          echo "NODE_ENV=production npm run dbCli add-to-tenant-privilege-group-by-name ${{ parameters.tenantHandle }} ${{ parameters.privilegeGroupName }} ${{ parameters.tenantsToAdd }}"
          if [ $(deployChanges) = true ]; then
            NODE_ENV=production npm run dbCli add-to-tenant-privilege-group-by-name ${{ parameters.tenantHandle }} "${{ parameters.privilegeGroupName }}" ${{ parameters.tenantsToAdd }}
          fi
      workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Adding to privilege groups ${{ parameters.tenantHandle }}'