# Steps to set the environment up to point to staging

parameters:
  - name: workingDirectory
    type: string
    
  - name: tenantHandle
    type: string

  - name: privilegeGroupName
    type: string
    default: 'Reporting Tenant Access'
    values:
    - 'Analyst Access'
    - 'Reporting Tenant Access'

  - name: tenantsToAdd
    type: string

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        echo "Info: "
        echo "Create privilege group for $(tenantHandle)..."
        echo "We will also associate $(addTenants) with this new group..."
        echo "NODE_ENV=production npm run dbCli tenant-privilege-group ${{ parameters.tenantHandle }} ${{ parameters.privilegeGroupName }} ${{ parameters.tenantHandle }} ${{ parameters.tenantsToAdd }}"
        if [ $(deployChanges) = true ]; then
          NODE_ENV=production npm run dbCli tenant-privilege-group ${{ parameters.tenantHandle }} "${{ parameters.privilegeGroupName }}" ${{ parameters.tenantHandle }} ${{ parameters.tenantsToAdd }}
        fi
      workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Setup new tenant privilege group'