# Steps to set the environment up to point to staging

parameters:
  - name: workingDirectory
    type: string
  - name: tenantHandle
    type: string
  - name: privilegeGroupId
    type: string
  - name: tenantHandlesToRemove
    type: string

steps:
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
          echo "Remove tenants ${{ parameters.tenantHandlesToRemove }} from tenant ${{ parameters.tenantHandle }} privelege groups: ${{ parameters.privilegeGroupId }}..."
          echo "NODE_ENV=production npm run dbCli remove-from-tenant-privilege-group ${{ parameters.tenantHandle }} ${{ parameters.privilegeGroupId }} ${{ parameters.tenantHandlesToRemove }}"
          if [ $(deployChanges) = true ]; then
            NODE_ENV=production npm run dbCli remove-from-tenant-privilege-group ${{ parameters.tenantHandle }} ${{ parameters.privilegeGroupId }} ${{ parameters.tenantHandlesToRemove }}
          fi
      workingDirectory: ${{ parameters.workingDirectory }}
    displayName: 'Remove from privilege group for ${{ parameters.tenantHandle }}'