steps:
  - checkout: self
    clean: true
    fetchTags: true
    persistCredentials: true
    displayName: 'Checkout source branch $(Build.SourceBranchName)'

  - task: UseNode@1
    inputs:
      version: $(nodeVersion)
    displayName: 'Install Node.js'

  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        git fetch --all
        git switch $(Build.SourceBranchName)
        git config user.email "<EMAIL>"
        git config user.name "alanresha"
      workingDirectory: $(System.DefaultWorkingDirectory)
    displayName: 'Fetch branch from GitHub'
    