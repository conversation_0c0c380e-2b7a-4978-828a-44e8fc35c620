# Environment Data Refresh Pipeline
The [Environment Data Refresh](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=9) pipeline is used to pull production data and push it into either staging or test. The proccess works by using pg_dump to extract all production data. It will then clear the environment you target, staging or test, and push the production dump into that environments database.

# Links
- [Environment Data Refresh Pipeline Runs](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=9)
- [Environments](https://dev.azure.com/ACME-General/catalyst-innovation/_environments)
- [Azure Devops Overview](./devops-overview.md)