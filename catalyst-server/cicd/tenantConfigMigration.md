# Tenant Configuration Migration Pipeline
The [Tenant Configuration Migration](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=8) pipeline is used to deliver tenant configurations and theme settings to either staging, test or production. This is a manually executed pipeline. When the pipeline is executed, you are prompted to select which environment you want to push changes to. This pipeline uses the configuration and theme files that are checked into our catalyst-innovation GitHub reposoitory. Changes must be applied to the repository before they can be pushed into any environment using this pipeline.

## Files used
The files used in this pipeline are located under "catalyst-innovation/catalyst-server/config/tenants". The json files located in this folder are pushed into the designated environment using the script located at "catalyst-innovation/catalyst-server/bin/batchUpdateTenantConfigs.sh"

# Links
- [Tenant Configuration Migration Pipeline Runs](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=8)
- [Environments](https://dev.azure.com/ACME-General/catalyst-innovation/_environments)
- [Azure Devops Overview](./devops-overview.md)