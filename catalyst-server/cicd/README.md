# Azure DevOps Overview

![img](./images/overview.png)

This diagram illustrates cloud service interaction with this CICD Pipeline process. Our code base is stored in GitHub and acts as the starting point for triggering the CICD processes. The Azure DevOps project that houses the pipeline, storage and build agent(s) are hosted using the ACME General’s Microsoft Service Agreement. The Azure DevOps instance is not affiliated with ACME’s Government instance directly.

[Azure DevOps Pipelines](https://dev.azure.com/ACME-General/catalyst-innovation/_build)

See [devops-overview.md](./devops-overview.md) file for more detailed information pertaining to pipeline flows.

## Pipeline Triggers
Pipeline triggers are defined in the ACME DevOps environment with each pipeline. Microsoft provides a lot of information in this area, but some of the information is a misleading when dealing with external repositories. The approach we provided has been successful up to this point. Below is a screen shot of where you will find the Azure DevOps configuration. Triggers are also defined in the yml file for each pipeline.

![img](./images/pipeline_trigger.png)

## Pipeline Variables
Microsoft has a few options for providing pipeline variables. In our processes outlined below we use a combination of pipeline specific variables, variable groups and secure files.

- Pipeline Variables: This is a list of pipeline variables used across all pipelines.
    - deployChanges: Default true, used to override the option of delivering the results of the pipeline run. This is mainly used for testing your pipeline changes and is used in specific tasks to prevent either deploying images, pushing up GitHub changes, etc.
- [Variable Groups](https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=VariableGroups): Currently we have two variable groups that are used across each pipeline
    - Global-Variables: Houses variables that can be used in any pipeline for consistency. Example is node_version so each pipeline is using the same instance of node.
    - catalyst-server-environment: This grouping provides variables that pertain to a particular environment. Example is environment env file name or cluster name.

## Pipeline Secure Files
Microsoft provides information pertaining to secure files. They are a good location for storing .env files that contain sensitive data. Once a file is added you will not have the opportunity to open it again, securing the file. Granted they could be boosted from a server when a job runs, but the overall process is pretty secure from anyone accessing it from Azure DevOps.

The processes below use [secure file](https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=SecureFiles) storage to house env files that are used in the pipelines.

## Pipeline Environments
Currently there are 5 [Environments](https://dev.azure.com/ACME-General/catalyst-innovation/_environments) used to assist in monitoring deployment activity.
- catalyst-server-prod
- catalyst-server-staging
- catalyst-server-test
- manual-release
- tenant-configuration-update

You can open the environment to see deployments executed against those environments. Since we are leveraging external services that are private we cannot provide to much information on the deployments.

## Automated CICD Deployment Stages

![img](./images/deployment_stages.png)

The CICD process consists of 3 base concepts, Testing, Building and Deployment. The Testing portion pertains to automated tests that are used as a precursor to identify potential issues pertaining to code changes. The Build portion is the creator of all artifacts that need to be deployed as well as any source code management needed to maintain versioning. The Deployment processes utilize the Build artifacts to update the existing environments, Staging, Test and Production. It also acts as the release cop for approving the deployment to certain environments.

See [devops-overview.md](./devops-overview.md) file for detailed information pertaining to each stage.

# Approvals
Delivery to test or production require approval. The overall CICD process automatically delivers code to the staging environment, but test and production require approval before it is delivered.

## Retry Stage - Test
A notification is delivered to designated users for approval in delivering a pipeline run into test. Once approved the pipeline will deliver the same configuration that was delivered to staging. This is also true for production. The pipeline process must deliver code to test before it can be pushed into production, unless the manual process is executed.

Currently, if approval is not granted within a minute the job ends. The process can be reran by clicking the "Retry Stage" button inside the pipeline that was executed. This will prompt for approval and once approved the pipeline will deliver that version of the code to test.

This illustration highlights the "Retry Stage" button that needs to be pressed inorder to rerun the stage.

![img](./images/retry_stage.png)

## Retry Stage - Production
A notification is delivered to designated users for approval in delivering a pipeline run into production. Once approved the pipeline will deliver the same configuration that was delivered to test and staging. The pipeline process must deliver code to test before it can be pushed into production, unless the manual process is executed.

Currently, if approval is not granted within a minute the job ends. This step is triggered after code is released to test. The process can be reran by clicking the "Retry Stage" button inside the pipeline that was executed. This will prompt for approval and once approved the pipeline will deliver that version of the code to production. This follows the same steps as test and the illustration above.

# Manual Delivery Pipelines

To assist in providing quick response option or on-off requests, we implemented a couple manual pipelines to deliver the product and config/theme information. The pipelines will provide support for rolling back builds or interruptions in the pipeline process. It can provide a consistent approach for delivering tenant updates via support needs.

## Product Deployment

![img](./images/deployment_manual.png)

- A manual deployment process is available for staging, test or Prod. When running this process, you provide the target environment and a version number you would like to deploy. The inputs are feed to the deployment process as arguments.
- The deployment step will pull the code base from GitHub to get the required tools needed for deployment. It will then update the Kubernetes cluster files with the provided version and update the appropriate environment cluster.

## Tenant Config and Theme

![img](./images/deployment_manual_configs.png)

- A manual deployment process is available for updating tenant configuration data. When running this process, you provide the target environment staging, test or Prod. The input is feed to the deployment process as an argument.
- The deployment step will pull the code base from GitHub to get the required tools needed and configuration files for updating the database. It will then execute a script that will perform the updates for each tenant based on the configuration/theme data pulled from GitHub
