# Post Release Procedures
This document outlines the steps needed to be done "post" release. Post release means we have released a IN_Sprint_{some number} branch to production and now we need to wrap up the sprint.

At the end of each sprint/release we should follow the steps below to accomplish the following:
1. Update our main branch with changes we just released to production. This keeps main up-to-date with release changes. Ideally we would have main and any "released" branch locked down post release. At this time we cannot do this.
2. Update the CICD process to use a new branch we will create in the first step. This will keep the CICD process rolling with the next featured branch.
3. Create a release in JIRA to capture what when out in the release for reporting purposes
4. Close out the current sprint we just released and move any open items to the next sprint.
5. Notify our "soldier-innovation" slack channel of the release.

## Merge Code
When we release a branch and need to update main, please follow these steps:
1. git checkout main
2. git fetch origin
3. git merge --squash IN_Sprint_{ Add the sprint number just released } # We use squash to try and keep the tree clean with all the incremental releases
4. git commit -m 'Merge IN_Sprint_{ Add the sprint number just released } # This commits to main or use vscode after cleaning up any merge conflicts
5. git push origin main
6. git fetch origin
7. git checkout -b IN_Sprint_{ Add the next sprint number here } # This should create our next sprint feature branch
8. git push -u origin IN_Sprint_{ Add the next sprint number here }

## Update Azure DevOps CICD Branch Target
After each sprint we need to update the automated triggers that are wired to github to run automated tests for PR's and generate a build for merges.
1. Open a browser and navigate to our [catalyst-innovation CICD Pipeline](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=6)
2. Select the button "Edit"
3. Select the "vertical dots menu" in the right corner next to Run
4. From the menu, select "Triggers". This should land you on the "Triggers" tab for editing the triggers.
5. Under the "Triggers" tab the option under Continuous integration should be selected. In the right side panel, locate "Branch filters"
6. Under "Branch filters" change the two filters to use the sprint branch you created in step 7 of "Merge Code"
7. Next, select the option under "Pull request validation" located in the left panel under Triggers. This will update the branch for running automated tests for PR requests.
8. Under "Branch filters" change the two filters to use the sprint branch you created in step 7 of "Merge Code"
8. Click the drop down arrow next to "Save & queue" and select "Save"
9. Enter a comment like "Updated branch filter from IN_Sprint_37 to IN_Sprint_38"

## JIRA Release
This process helps to created a consolidated look at changes we just delivered to production.
1. Navigate to the "Releases" section inside [Jira](https://acmegeneral.atlassian.net/projects/IN?selectedItem=com.atlassian.jira.jira-projects-plugin%3Arelease-page) for the Innovation project
2. Select the option "Create version" from the right side of the page.
3. Enter the name "catalyst-server-{ Semantic version of the release that just went to production }
4. Add the start date as the day the sprint started and the release date of when the sprint ended.
5. Open the "Backlog" in Jira
6. Select all the sprint items that were included in the release
7. Chose "edit fields" from the tooltip menu that pops up.
8. The "Bulk edit" panel should open on the right
9. Search for "Fix versions" and select it
10. Click out of the search dialog. You should see "Fix versions" listed now.
11. Add the "Fix versions" number of the "Release" you just created.
12. Follow the steps to update the items and don't notify all the groups.

## JIRA Sprint Closeout
For this section, move any remaining open items to the next release. Check that work items have been updated with information needed to understand what changed in the item and how it was tested. Once this is complete, close the sprint and start the next sprint. You can chose a future date for starting the next sprint.

## Notify Community
Create a message to our soldier-innovation slack channel similar to this example:

`Release 1.25.1 has been deployed to production!  :rocket:  Great work team!
Items released in this version can be found here: https://acmegeneral.atlassian.net/projects/IN/versions/10203/tab/release-report-all-issues`

The release report is generated when you create the release.