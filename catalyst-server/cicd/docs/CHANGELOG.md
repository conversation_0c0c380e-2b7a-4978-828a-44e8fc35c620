# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.26.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.10...catalyst-server-1.26.0) (2025-07-03)

### Features

* change event basic setup with user logging as example ([1b31803](https://github.com/ACME-General/catalyst-innovation/commit/1b3180361604ac10f6a30d178f420a56bd204ae1))

## [1.25.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.9...catalyst-server-1.25.10) (2025-06-23)

## [1.25.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.7...catalyst-server-1.25.9) (2025-06-22)

## [1.25.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.7...catalyst-server-1.25.8) (2025-06-12)

## [1.25.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.6...catalyst-server-1.25.7) (2025-06-11)

## [1.25.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.5...catalyst-server-1.25.6) (2025-06-03)

## [1.25.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.5...catalyst-server-1.25.6) (2025-06-03)

## [1.25.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.3...catalyst-server-1.25.4) (2025-05-19)

## [1.25.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.25.0...catalyst-server-1.25.1) (2025-05-14)

### Bug Fixes

* tweaked countCharacterDifferences function to match requirements ([4e5306b](https://github.com/ACME-General/catalyst-innovation/commit/4e5306b715e5486c84623b596d5bfa0c65af172c))

## [1.25.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.24.0...catalyst-server-1.25.0) (2025-05-12)


### Features

* explicit logout toast ([49edf37](https://github.com/ACME-General/catalyst-innovation/commit/49edf37117207652ca1c6bd007a9c543b2f42f1e))
* explicit logout toast ([4705bc3](https://github.com/ACME-General/catalyst-innovation/commit/4705bc346097435b849d90d5279cf8b72f2f09bf))


### Bug Fixes

* adjusted 4 digit country codes ([9279c09](https://github.com/ACME-General/catalyst-innovation/commit/9279c09a0fa0a1d93011910ad401f9e7517c7720))
* password confirmation fixes ([ddbd659](https://github.com/ACME-General/catalyst-innovation/commit/ddbd659c004657acd1b0e6cb4845cf381cd71a59))
* phone number dropdown no longer triggers validation ([46c73c7](https://github.com/ACME-General/catalyst-innovation/commit/46c73c79ba130f8a7b0f65e7b94ff4c6eaab43dd))

## [1.24.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.23.0...catalyst-server-1.24.0) (2025-05-08)


### Features

* enhance phone input handling with country code support and validation ([4f90059](https://github.com/ACME-General/catalyst-innovation/commit/4f90059721943140d911e601ca5afc8d4ba2ad58))
* refactor phone number handling with new utility functions to improve readability and maintainability ([e3956d1](https://github.com/ACME-General/catalyst-innovation/commit/e3956d1cfc6e4f94bf8e7c4025e7968e288894a7))

## [1.23.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.4...catalyst-server-1.23.0) (2025-05-01)


### Features

* added password validation on frontend and adjusted frontend to follow ([8a21e60](https://github.com/ACME-General/catalyst-innovation/commit/8a21e609e5f66b759b9f42f9049d8250f3b81735))
* extracted password logic to new util function ([e299ff7](https://github.com/ACME-General/catalyst-innovation/commit/e299ff704bcf227f9297cc179fdc5d5c83d69148))


### Bug Fixes

* fixed copy of password labels ([20bab0d](https://github.com/ACME-General/catalyst-innovation/commit/20bab0dc13ffc0f6993e850bd4865da5ae0fe6b5))
* moved validation code into parent functions. Made abstraction of password text inputs ([e36f782](https://github.com/ACME-General/catalyst-innovation/commit/e36f782141bc0b1ad6af7143633b470b3682f352))

## [1.22.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.3...catalyst-server-1.22.4) (2025-04-21)

## [1.25.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.24.0...catalyst-server-1.25.0) (2025-05-12)


### Features

* explicit logout toast ([49edf37](https://github.com/ACME-General/catalyst-innovation/commit/49edf37117207652ca1c6bd007a9c543b2f42f1e))
* explicit logout toast ([4705bc3](https://github.com/ACME-General/catalyst-innovation/commit/4705bc346097435b849d90d5279cf8b72f2f09bf))


### Bug Fixes

* adjusted 4 digit country codes ([9279c09](https://github.com/ACME-General/catalyst-innovation/commit/9279c09a0fa0a1d93011910ad401f9e7517c7720))
* password confirmation fixes ([ddbd659](https://github.com/ACME-General/catalyst-innovation/commit/ddbd659c004657acd1b0e6cb4845cf381cd71a59))
* phone number dropdown no longer triggers validation ([46c73c7](https://github.com/ACME-General/catalyst-innovation/commit/46c73c79ba130f8a7b0f65e7b94ff4c6eaab43dd))

## [1.24.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.23.0...catalyst-server-1.24.0) (2025-05-08)


### Features

* enhance phone input handling with country code support and validation ([4f90059](https://github.com/ACME-General/catalyst-innovation/commit/4f90059721943140d911e601ca5afc8d4ba2ad58))
* refactor phone number handling with new utility functions to improve readability and maintainability ([e3956d1](https://github.com/ACME-General/catalyst-innovation/commit/e3956d1cfc6e4f94bf8e7c4025e7968e288894a7))

## [1.23.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.4...catalyst-server-1.23.0) (2025-05-01)


### Features

* added password validation on frontend and adjusted frontend to follow ([8a21e60](https://github.com/ACME-General/catalyst-innovation/commit/8a21e609e5f66b759b9f42f9049d8250f3b81735))
* extracted password logic to new util function ([e299ff7](https://github.com/ACME-General/catalyst-innovation/commit/e299ff704bcf227f9297cc179fdc5d5c83d69148))


### Bug Fixes

* fixed copy of password labels ([20bab0d](https://github.com/ACME-General/catalyst-innovation/commit/20bab0dc13ffc0f6993e850bd4865da5ae0fe6b5))
* moved validation code into parent functions. Made abstraction of password text inputs ([e36f782](https://github.com/ACME-General/catalyst-innovation/commit/e36f782141bc0b1ad6af7143633b470b3682f352))

## [1.22.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.3...catalyst-server-1.22.4) (2025-04-21)

## [1.22.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.2...catalyst-server-1.22.3) (2025-04-15)

## [1.24.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.23.0...catalyst-server-1.24.0) (2025-05-08)


### Features

* enhance phone input handling with country code support and validation ([4f90059](https://github.com/ACME-General/catalyst-innovation/commit/4f90059721943140d911e601ca5afc8d4ba2ad58))
* refactor phone number handling with new utility functions to improve readability and maintainability ([e3956d1](https://github.com/ACME-General/catalyst-innovation/commit/e3956d1cfc6e4f94bf8e7c4025e7968e288894a7))

## [1.23.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.4...catalyst-server-1.23.0) (2025-05-01)


### Features

* added password validation on frontend and adjusted frontend to follow ([8a21e60](https://github.com/ACME-General/catalyst-innovation/commit/8a21e609e5f66b759b9f42f9049d8250f3b81735))
* extracted password logic to new util function ([e299ff7](https://github.com/ACME-General/catalyst-innovation/commit/e299ff704bcf227f9297cc179fdc5d5c83d69148))


### Bug Fixes

* fixed copy of password labels ([20bab0d](https://github.com/ACME-General/catalyst-innovation/commit/20bab0dc13ffc0f6993e850bd4865da5ae0fe6b5))
* moved validation code into parent functions. Made abstraction of password text inputs ([e36f782](https://github.com/ACME-General/catalyst-innovation/commit/e36f782141bc0b1ad6af7143633b470b3682f352))

## [1.22.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.3...catalyst-server-1.22.4) (2025-04-21)

## [1.22.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.2...catalyst-server-1.22.3) (2025-04-15)

## [1.22.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.1...catalyst-server-1.22.2) (2025-04-15)


### Bug Fixes

* changed button for deletion modals ([068d9b9](https://github.com/ACME-General/catalyst-innovation/commit/068d9b937330863ef0d06c6bd16f6d89f6f60759))

## [1.23.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.4...catalyst-server-1.23.0) (2025-05-01)


### Features

* added password validation on frontend and adjusted frontend to follow ([8a21e60](https://github.com/ACME-General/catalyst-innovation/commit/8a21e609e5f66b759b9f42f9049d8250f3b81735))
* extracted password logic to new util function ([e299ff7](https://github.com/ACME-General/catalyst-innovation/commit/e299ff704bcf227f9297cc179fdc5d5c83d69148))


### Bug Fixes

* fixed copy of password labels ([20bab0d](https://github.com/ACME-General/catalyst-innovation/commit/20bab0dc13ffc0f6993e850bd4865da5ae0fe6b5))
* moved validation code into parent functions. Made abstraction of password text inputs ([e36f782](https://github.com/ACME-General/catalyst-innovation/commit/e36f782141bc0b1ad6af7143633b470b3682f352))

## [1.22.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.3...catalyst-server-1.22.4) (2025-04-21)

## [1.22.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.2...catalyst-server-1.22.3) (2025-04-15)

## [1.22.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.1...catalyst-server-1.22.2) (2025-04-15)


### Bug Fixes

* changed button for deletion modals ([068d9b9](https://github.com/ACME-General/catalyst-innovation/commit/068d9b937330863ef0d06c6bd16f6d89f6f60759))

## [1.22.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.0...catalyst-server-1.22.1) (2025-04-14)


### Bug Fixes

* reverted change for server delete entity code. Removed hard delete code from client ([733d058](https://github.com/ACME-General/catalyst-innovation/commit/733d0588820a4e6ffc8de167ab8eaed062236ef0))

## [1.22.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.3...catalyst-server-1.22.4) (2025-04-21)

## [1.22.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.2...catalyst-server-1.22.3) (2025-04-15)

## [1.22.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.1...catalyst-server-1.22.2) (2025-04-15)


### Bug Fixes

* changed button for deletion modals ([068d9b9](https://github.com/ACME-General/catalyst-innovation/commit/068d9b937330863ef0d06c6bd16f6d89f6f60759))

## [1.22.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.0...catalyst-server-1.22.1) (2025-04-14)


### Bug Fixes

* reverted change for server delete entity code. Removed hard delete code from client ([733d058](https://github.com/ACME-General/catalyst-innovation/commit/733d0588820a4e6ffc8de167ab8eaed062236ef0))

## [1.22.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.16...catalyst-server-1.22.0) (2025-04-14)


### Features

* project delete functionality ([d5a0756](https://github.com/ACME-General/catalyst-innovation/commit/d5a0756d3b8dd7b5f3a487260c20077a6d712ab9))


### Bug Fixes

* removed hard-delete capabilities on server ([e1c5b8f](https://github.com/ACME-General/catalyst-innovation/commit/e1c5b8ff08cc5baf7be5ac62c84561c1d13cf372))

## [1.22.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.2...catalyst-server-1.22.3) (2025-04-15)

## [1.22.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.1...catalyst-server-1.22.2) (2025-04-15)


### Bug Fixes

* changed button for deletion modals ([068d9b9](https://github.com/ACME-General/catalyst-innovation/commit/068d9b937330863ef0d06c6bd16f6d89f6f60759))

## [1.22.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.0...catalyst-server-1.22.1) (2025-04-14)


### Bug Fixes

* reverted change for server delete entity code. Removed hard delete code from client ([733d058](https://github.com/ACME-General/catalyst-innovation/commit/733d0588820a4e6ffc8de167ab8eaed062236ef0))

## [1.22.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.16...catalyst-server-1.22.0) (2025-04-14)


### Features

* project delete functionality ([d5a0756](https://github.com/ACME-General/catalyst-innovation/commit/d5a0756d3b8dd7b5f3a487260c20077a6d712ab9))


### Bug Fixes

* removed hard-delete capabilities on server ([e1c5b8f](https://github.com/ACME-General/catalyst-innovation/commit/e1c5b8ff08cc5baf7be5ac62c84561c1d13cf372))

## 1.21.16 (2025-04-07)

## [1.22.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.1...catalyst-server-1.22.2) (2025-04-15)


### Bug Fixes

* changed button for deletion modals ([068d9b9](https://github.com/ACME-General/catalyst-innovation/commit/068d9b937330863ef0d06c6bd16f6d89f6f60759))

## [1.22.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.0...catalyst-server-1.22.1) (2025-04-14)


### Bug Fixes

* reverted change for server delete entity code. Removed hard delete code from client ([733d058](https://github.com/ACME-General/catalyst-innovation/commit/733d0588820a4e6ffc8de167ab8eaed062236ef0))

## [1.22.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.16...catalyst-server-1.22.0) (2025-04-14)


### Features

* project delete functionality ([d5a0756](https://github.com/ACME-General/catalyst-innovation/commit/d5a0756d3b8dd7b5f3a487260c20077a6d712ab9))


### Bug Fixes

* removed hard-delete capabilities on server ([e1c5b8f](https://github.com/ACME-General/catalyst-innovation/commit/e1c5b8ff08cc5baf7be5ac62c84561c1d13cf372))

## 1.21.16 (2025-04-07)

## [1.22.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.22.0...catalyst-server-1.22.1) (2025-04-14)


### Bug Fixes

* reverted change for server delete entity code. Removed hard delete code from client ([733d058](https://github.com/ACME-General/catalyst-innovation/commit/733d0588820a4e6ffc8de167ab8eaed062236ef0))

## [1.22.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.16...catalyst-server-1.22.0) (2025-04-14)


### Features

* project delete functionality ([d5a0756](https://github.com/ACME-General/catalyst-innovation/commit/d5a0756d3b8dd7b5f3a487260c20077a6d712ab9))


### Bug Fixes

* removed hard-delete capabilities on server ([e1c5b8f](https://github.com/ACME-General/catalyst-innovation/commit/e1c5b8ff08cc5baf7be5ac62c84561c1d13cf372))

## 1.21.16 (2025-04-07)

## [1.22.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.16...catalyst-server-1.22.0) (2025-04-14)


### Features

* project delete functionality ([d5a0756](https://github.com/ACME-General/catalyst-innovation/commit/d5a0756d3b8dd7b5f3a487260c20077a6d712ab9))


### Bug Fixes

* removed hard-delete capabilities on server ([e1c5b8f](https://github.com/ACME-General/catalyst-innovation/commit/e1c5b8ff08cc5baf7be5ac62c84561c1d13cf372))

## 1.21.16 (2025-04-07)

## 1.21.16 (2025-04-07)

## [1.21.15](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.14...catalyst-server-1.21.15) (2025-03-26)

## [1.21.14](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.13...catalyst-server-1.21.14) (2025-03-10)

## [1.21.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.12...catalyst-server-1.21.13) (2025-03-06)

## [1.21.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.11...catalyst-server-1.21.12) (2025-03-06)

## [1.21.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.10...catalyst-server-1.21.11) (2025-03-05)

## [1.21.14](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.13...catalyst-server-1.21.14) (2025-03-10)

## [1.21.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.12...catalyst-server-1.21.13) (2025-03-06)

## [1.21.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.11...catalyst-server-1.21.12) (2025-03-06)

## [1.21.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.10...catalyst-server-1.21.11) (2025-03-05)

## [1.21.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.9...catalyst-server-1.21.10) (2025-03-03)

## [1.21.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.12...catalyst-server-1.21.13) (2025-03-06)

## [1.21.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.11...catalyst-server-1.21.12) (2025-03-06)

## [1.21.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.10...catalyst-server-1.21.11) (2025-03-05)

## [1.21.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.9...catalyst-server-1.21.10) (2025-03-03)

## [1.21.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.8...catalyst-server-1.21.9) (2025-02-28)

## [1.21.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.11...catalyst-server-1.21.12) (2025-03-06)

## [1.21.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.10...catalyst-server-1.21.11) (2025-03-05)

## [1.21.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.9...catalyst-server-1.21.10) (2025-03-03)

## [1.21.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.8...catalyst-server-1.21.9) (2025-02-28)

## [1.21.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.7...catalyst-server-1.21.8) (2025-02-27)

## [1.21.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.10...catalyst-server-1.21.11) (2025-03-05)

## [1.21.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.9...catalyst-server-1.21.10) (2025-03-03)

## [1.21.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.8...catalyst-server-1.21.9) (2025-02-28)

## [1.21.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.7...catalyst-server-1.21.8) (2025-02-27)

## [1.21.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.6...catalyst-server-1.21.7) (2025-02-26)

## [1.21.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.9...catalyst-server-1.21.10) (2025-03-03)

## [1.21.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.8...catalyst-server-1.21.9) (2025-02-28)

## [1.21.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.7...catalyst-server-1.21.8) (2025-02-27)

## [1.21.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.6...catalyst-server-1.21.7) (2025-02-26)

## [1.21.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.5...catalyst-server-1.21.6) (2025-02-26)

## [1.21.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.8...catalyst-server-1.21.9) (2025-02-28)

## [1.21.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.7...catalyst-server-1.21.8) (2025-02-27)

## [1.21.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.6...catalyst-server-1.21.7) (2025-02-26)

## [1.21.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.5...catalyst-server-1.21.6) (2025-02-26)

## [1.21.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.4...catalyst-server-1.21.5) (2025-02-19)

## [1.21.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.7...catalyst-server-1.21.8) (2025-02-27)

## [1.21.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.6...catalyst-server-1.21.7) (2025-02-26)

## [1.21.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.5...catalyst-server-1.21.6) (2025-02-26)

## [1.21.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.4...catalyst-server-1.21.5) (2025-02-19)

## [1.21.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.3...catalyst-server-1.21.4) (2025-02-11)


### Bug Fixes

* Adjust pixel ratio for creating image ([ddca611](https://github.com/ACME-General/catalyst-innovation/commit/ddca6112d29a356a5849b97400f3d2f9c8a0548d))

## [1.21.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.6...catalyst-server-1.21.7) (2025-02-26)

## [1.21.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.5...catalyst-server-1.21.6) (2025-02-26)

## [1.21.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.4...catalyst-server-1.21.5) (2025-02-19)

## [1.21.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.3...catalyst-server-1.21.4) (2025-02-11)


### Bug Fixes

* Adjust pixel ratio for creating image ([ddca611](https://github.com/ACME-General/catalyst-innovation/commit/ddca6112d29a356a5849b97400f3d2f9c8a0548d))

## [1.21.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.2...catalyst-server-1.21.3) (2025-02-11)

## [1.21.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.5...catalyst-server-1.21.6) (2025-02-26)

## [1.21.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.4...catalyst-server-1.21.5) (2025-02-19)

## [1.21.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.3...catalyst-server-1.21.4) (2025-02-11)


### Bug Fixes

* Adjust pixel ratio for creating image ([ddca611](https://github.com/ACME-General/catalyst-innovation/commit/ddca6112d29a356a5849b97400f3d2f9c8a0548d))

## [1.21.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.2...catalyst-server-1.21.3) (2025-02-11)

## [1.21.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.1...catalyst-server-1.21.2) (2025-02-06)

## [1.21.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.4...catalyst-server-1.21.5) (2025-02-19)

## [1.21.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.3...catalyst-server-1.21.4) (2025-02-11)


### Bug Fixes

* Adjust pixel ratio for creating image ([ddca611](https://github.com/ACME-General/catalyst-innovation/commit/ddca6112d29a356a5849b97400f3d2f9c8a0548d))

## [1.21.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.2...catalyst-server-1.21.3) (2025-02-11)

## [1.21.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.1...catalyst-server-1.21.2) (2025-02-06)

## [1.21.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.0...catalyst-server-1.21.1) (2025-02-03)

## [1.21.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.3...catalyst-server-1.21.4) (2025-02-11)


### Bug Fixes

* Adjust pixel ratio for creating image ([ddca611](https://github.com/ACME-General/catalyst-innovation/commit/ddca6112d29a356a5849b97400f3d2f9c8a0548d))

## [1.21.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.2...catalyst-server-1.21.3) (2025-02-11)

## [1.21.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.1...catalyst-server-1.21.2) (2025-02-06)

## [1.21.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.0...catalyst-server-1.21.1) (2025-02-03)

## [1.21.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.21.0) (2025-01-27)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.21.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.2...catalyst-server-1.21.3) (2025-02-11)

## [1.21.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.1...catalyst-server-1.21.2) (2025-02-06)

## [1.21.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.0...catalyst-server-1.21.1) (2025-02-03)

## [1.21.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.21.0) (2025-01-27)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.21.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.1...catalyst-server-1.21.2) (2025-02-06)

## [1.21.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.0...catalyst-server-1.21.1) (2025-02-03)

## [1.21.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.21.0) (2025-01-27)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.21.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.21.0...catalyst-server-1.21.1) (2025-02-03)

## [1.21.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.21.0) (2025-01-27)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.5.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.1...catalyst-server-1.5.2) (2024-07-23)


### Features

* IN-1101 added tenant 'content' update routine to dbCli ([70fb9bb](https://github.com/ACME-General/catalyst-innovation/commit/70fb9bb8ac966be98375932ce9b294b341c5009c))
* IN-1101 added tenant 'content' update routine to dbCli ([9f41036](https://github.com/ACME-General/catalyst-innovation/commit/9f4103635c36f1a9267583ebb32efdd23af9bcaf))


### Bug Fixes

* IN-1102 stopped grabbing 'newest' key, now grabbing serverVersion ([81048c5](https://github.com/ACME-General/catalyst-innovation/commit/81048c55fcaca461674a15c39f04b1b0222cd9ed))

## [1.21.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.21.0) (2025-01-27)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.5.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.1...catalyst-server-1.5.2) (2024-07-23)


### Features

* IN-1101 added tenant 'content' update routine to dbCli ([70fb9bb](https://github.com/ACME-General/catalyst-innovation/commit/70fb9bb8ac966be98375932ce9b294b341c5009c))
* IN-1101 added tenant 'content' update routine to dbCli ([9f41036](https://github.com/ACME-General/catalyst-innovation/commit/9f4103635c36f1a9267583ebb32efdd23af9bcaf))


### Bug Fixes

* IN-1102 stopped grabbing 'newest' key, now grabbing serverVersion ([81048c5](https://github.com/ACME-General/catalyst-innovation/commit/81048c55fcaca461674a15c39f04b1b0222cd9ed))

## [1.5.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.0...catalyst-server-1.5.1) (2024-07-23)


### Bug Fixes

* added protection for null content json ([8ca2d18](https://github.com/ACME-General/catalyst-innovation/commit/8ca2d18a07a75004cb8f8796a435e84d62af021d))

## [1.20.23](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.22...catalyst-server-1.20.23) (2025-01-22)

## [1.20.22](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.21...catalyst-server-1.20.22) (2025-01-17)


### Bug Fixes

* added padding to export to work for zoomed screens ([cee1cf5](https://github.com/ACME-General/catalyst-innovation/commit/cee1cf518be61eecda119e4e0fabb58dcef46e9f))

## [1.20.21](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.20...catalyst-server-1.20.21) (2025-01-16)

## [1.20.20](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.19...catalyst-server-1.20.20) (2025-01-15)


### Bug Fixes

* IN-1175 -- fixed analytics export fix ([5c0721a](https://github.com/ACME-General/catalyst-innovation/commit/5c0721ab4dc3587d196543c00ede6dd2063f9e89))

## [1.20.19](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.18...catalyst-server-1.20.19) (2025-01-14)

## [1.20.22](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.21...catalyst-server-1.20.22) (2025-01-17)


### Bug Fixes

* added padding to export to work for zoomed screens ([cee1cf5](https://github.com/ACME-General/catalyst-innovation/commit/cee1cf518be61eecda119e4e0fabb58dcef46e9f))

## [1.20.21](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.20...catalyst-server-1.20.21) (2025-01-16)

## [1.20.20](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.19...catalyst-server-1.20.20) (2025-01-15)


### Bug Fixes

* IN-1175 -- fixed analytics export fix ([5c0721a](https://github.com/ACME-General/catalyst-innovation/commit/5c0721ab4dc3587d196543c00ede6dd2063f9e89))

## [1.20.19](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.18...catalyst-server-1.20.19) (2025-01-14)

## [1.20.18](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.17...catalyst-server-1.20.18) (2024-12-24)

## [1.20.21](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.20...catalyst-server-1.20.21) (2025-01-16)

## [1.20.20](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.19...catalyst-server-1.20.20) (2025-01-15)


### Bug Fixes

* IN-1175 -- fixed analytics export fix ([5c0721a](https://github.com/ACME-General/catalyst-innovation/commit/5c0721ab4dc3587d196543c00ede6dd2063f9e89))

## [1.20.19](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.18...catalyst-server-1.20.19) (2025-01-14)

## [1.20.18](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.17...catalyst-server-1.20.18) (2024-12-24)

## [1.20.17](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.16...catalyst-server-1.20.17) (2024-12-24)

## [1.20.20](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.19...catalyst-server-1.20.20) (2025-01-15)


### Bug Fixes

* IN-1175 -- fixed analytics export fix ([5c0721a](https://github.com/ACME-General/catalyst-innovation/commit/5c0721ab4dc3587d196543c00ede6dd2063f9e89))

## [1.20.19](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.18...catalyst-server-1.20.19) (2025-01-14)

## [1.20.18](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.17...catalyst-server-1.20.18) (2024-12-24)

## [1.20.17](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.16...catalyst-server-1.20.17) (2024-12-24)

## [1.20.16](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.15...catalyst-server-1.20.16) (2024-12-17)

## [1.20.19](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.18...catalyst-server-1.20.19) (2025-01-14)

## [1.20.18](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.17...catalyst-server-1.20.18) (2024-12-24)

## [1.20.17](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.16...catalyst-server-1.20.17) (2024-12-24)

## [1.20.16](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.15...catalyst-server-1.20.16) (2024-12-17)

## [1.20.15](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.14...catalyst-server-1.20.15) (2024-12-16)

## [1.20.18](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.17...catalyst-server-1.20.18) (2024-12-24)

## [1.20.17](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.16...catalyst-server-1.20.17) (2024-12-24)

## [1.20.16](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.15...catalyst-server-1.20.16) (2024-12-17)

## [1.20.15](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.14...catalyst-server-1.20.15) (2024-12-16)

## [1.20.14](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.13...catalyst-server-1.20.14) (2024-12-13)

## [1.20.17](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.16...catalyst-server-1.20.17) (2024-12-24)

## [1.20.16](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.15...catalyst-server-1.20.16) (2024-12-17)

## [1.20.15](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.14...catalyst-server-1.20.15) (2024-12-16)

## [1.20.14](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.13...catalyst-server-1.20.14) (2024-12-13)

## [1.20.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.12...catalyst-server-1.20.13) (2024-12-12)

## [1.20.16](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.15...catalyst-server-1.20.16) (2024-12-17)

## [1.20.15](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.14...catalyst-server-1.20.15) (2024-12-16)

## [1.20.14](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.13...catalyst-server-1.20.14) (2024-12-13)

## [1.20.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.12...catalyst-server-1.20.13) (2024-12-12)

## [1.20.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.11...catalyst-server-1.20.12) (2024-12-11)

## [1.20.15](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.14...catalyst-server-1.20.15) (2024-12-16)

## [1.20.14](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.13...catalyst-server-1.20.14) (2024-12-13)

## [1.20.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.12...catalyst-server-1.20.13) (2024-12-12)

## [1.20.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.11...catalyst-server-1.20.12) (2024-12-11)

## [1.20.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.10...catalyst-server-1.20.11) (2024-12-09)

## [1.20.14](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.13...catalyst-server-1.20.14) (2024-12-13)

## [1.20.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.12...catalyst-server-1.20.13) (2024-12-12)

## [1.20.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.11...catalyst-server-1.20.12) (2024-12-11)

## [1.20.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.10...catalyst-server-1.20.11) (2024-12-09)

## [1.20.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.9...catalyst-server-1.20.10) (2024-12-05)

## [1.20.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.12...catalyst-server-1.20.13) (2024-12-12)

## [1.20.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.11...catalyst-server-1.20.12) (2024-12-11)

## [1.20.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.10...catalyst-server-1.20.11) (2024-12-09)

## [1.20.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.9...catalyst-server-1.20.10) (2024-12-05)

## [1.20.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.8...catalyst-server-1.20.9) (2024-12-05)

## [1.20.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.11...catalyst-server-1.20.12) (2024-12-11)

## [1.20.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.10...catalyst-server-1.20.11) (2024-12-09)

## [1.20.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.9...catalyst-server-1.20.10) (2024-12-05)

## [1.20.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.8...catalyst-server-1.20.9) (2024-12-05)

## [1.20.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.7...catalyst-server-1.20.8) (2024-12-02)

## [1.20.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.10...catalyst-server-1.20.11) (2024-12-09)

## [1.20.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.9...catalyst-server-1.20.10) (2024-12-05)

## [1.20.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.8...catalyst-server-1.20.9) (2024-12-05)

## [1.20.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.7...catalyst-server-1.20.8) (2024-12-02)

## [1.20.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.6...catalyst-server-1.20.7) (2024-12-02)

## [1.20.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.9...catalyst-server-1.20.10) (2024-12-05)

## [1.20.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.8...catalyst-server-1.20.9) (2024-12-05)

## [1.20.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.7...catalyst-server-1.20.8) (2024-12-02)

## [1.20.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.6...catalyst-server-1.20.7) (2024-12-02)

## [1.20.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.5...catalyst-server-1.20.6) (2024-11-27)

## [1.20.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.8...catalyst-server-1.20.9) (2024-12-05)

## [1.20.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.7...catalyst-server-1.20.8) (2024-12-02)

## [1.20.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.6...catalyst-server-1.20.7) (2024-12-02)

## [1.20.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.5...catalyst-server-1.20.6) (2024-11-27)

## [1.20.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.4...catalyst-server-1.20.5) (2024-11-25)

## [1.20.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.7...catalyst-server-1.20.8) (2024-12-02)

## [1.20.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.6...catalyst-server-1.20.7) (2024-12-02)

## [1.20.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.5...catalyst-server-1.20.6) (2024-11-27)

## [1.20.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.4...catalyst-server-1.20.5) (2024-11-25)

## [1.20.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.3...catalyst-server-1.20.4) (2024-11-22)


### Bug Fixes

* moved certain filterInfo operations into store. removed useEffects and local observables ([4e520a7](https://github.com/ACME-General/catalyst-innovation/commit/4e520a76fad819031e4c8daf3b3187f0231ff6c0))
* removed UI semantics from filterInfoStore ([a0eb73b](https://github.com/ACME-General/catalyst-innovation/commit/a0eb73b34e3f1d95c7b763d616d6f59a05cdb030))

## [1.20.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.6...catalyst-server-1.20.7) (2024-12-02)

## [1.20.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.5...catalyst-server-1.20.6) (2024-11-27)

## [1.20.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.4...catalyst-server-1.20.5) (2024-11-25)

## [1.20.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.3...catalyst-server-1.20.4) (2024-11-22)


### Bug Fixes

* moved certain filterInfo operations into store. removed useEffects and local observables ([4e520a7](https://github.com/ACME-General/catalyst-innovation/commit/4e520a76fad819031e4c8daf3b3187f0231ff6c0))
* removed UI semantics from filterInfoStore ([a0eb73b](https://github.com/ACME-General/catalyst-innovation/commit/a0eb73b34e3f1d95c7b763d616d6f59a05cdb030))

## [1.20.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.2...catalyst-server-1.20.3) (2024-11-21)

## [1.20.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.5...catalyst-server-1.20.6) (2024-11-27)

## [1.20.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.4...catalyst-server-1.20.5) (2024-11-25)

## [1.20.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.3...catalyst-server-1.20.4) (2024-11-22)


### Bug Fixes

* moved certain filterInfo operations into store. removed useEffects and local observables ([4e520a7](https://github.com/ACME-General/catalyst-innovation/commit/4e520a76fad819031e4c8daf3b3187f0231ff6c0))
* removed UI semantics from filterInfoStore ([a0eb73b](https://github.com/ACME-General/catalyst-innovation/commit/a0eb73b34e3f1d95c7b763d616d6f59a05cdb030))

## [1.20.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.2...catalyst-server-1.20.3) (2024-11-21)

## [1.20.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.1...catalyst-server-1.20.2) (2024-11-20)

## [1.20.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.4...catalyst-server-1.20.5) (2024-11-25)

## [1.20.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.3...catalyst-server-1.20.4) (2024-11-22)


### Bug Fixes

* moved certain filterInfo operations into store. removed useEffects and local observables ([4e520a7](https://github.com/ACME-General/catalyst-innovation/commit/4e520a76fad819031e4c8daf3b3187f0231ff6c0))
* removed UI semantics from filterInfoStore ([a0eb73b](https://github.com/ACME-General/catalyst-innovation/commit/a0eb73b34e3f1d95c7b763d616d6f59a05cdb030))

## [1.20.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.2...catalyst-server-1.20.3) (2024-11-21)

## [1.20.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.1...catalyst-server-1.20.2) (2024-11-20)

## [1.20.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.0...catalyst-server-1.20.1) (2024-11-20)

## [1.20.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.3...catalyst-server-1.20.4) (2024-11-22)


### Bug Fixes

* moved certain filterInfo operations into store. removed useEffects and local observables ([4e520a7](https://github.com/ACME-General/catalyst-innovation/commit/4e520a76fad819031e4c8daf3b3187f0231ff6c0))
* removed UI semantics from filterInfoStore ([a0eb73b](https://github.com/ACME-General/catalyst-innovation/commit/a0eb73b34e3f1d95c7b763d616d6f59a05cdb030))

## [1.20.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.2...catalyst-server-1.20.3) (2024-11-21)

## [1.20.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.1...catalyst-server-1.20.2) (2024-11-20)

## [1.20.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.0...catalyst-server-1.20.1) (2024-11-20)

## [1.20.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.5...catalyst-server-1.20.0) (2024-11-18)


### Features

* Add tenant path Route and add missing dependency ([8cfe350](https://github.com/ACME-General/catalyst-innovation/commit/8cfe35092774112a65908a41b1fb5c0f089d83f4))

## [1.20.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.2...catalyst-server-1.20.3) (2024-11-21)

## [1.20.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.1...catalyst-server-1.20.2) (2024-11-20)

## [1.20.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.0...catalyst-server-1.20.1) (2024-11-20)

## [1.20.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.5...catalyst-server-1.20.0) (2024-11-18)


### Features

* Add tenant path Route and add missing dependency ([8cfe350](https://github.com/ACME-General/catalyst-innovation/commit/8cfe35092774112a65908a41b1fb5c0f089d83f4))

## [1.19.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.4...catalyst-server-1.19.5) (2024-11-15)

## [1.20.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.1...catalyst-server-1.20.2) (2024-11-20)

## [1.20.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.0...catalyst-server-1.20.1) (2024-11-20)

## [1.20.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.5...catalyst-server-1.20.0) (2024-11-18)


### Features

* Add tenant path Route and add missing dependency ([8cfe350](https://github.com/ACME-General/catalyst-innovation/commit/8cfe35092774112a65908a41b1fb5c0f089d83f4))

## [1.19.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.4...catalyst-server-1.19.5) (2024-11-15)

## [1.19.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.3...catalyst-server-1.19.4) (2024-11-15)

## [1.20.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.20.0...catalyst-server-1.20.1) (2024-11-20)

## [1.20.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.5...catalyst-server-1.20.0) (2024-11-18)


### Features

* Add tenant path Route and add missing dependency ([8cfe350](https://github.com/ACME-General/catalyst-innovation/commit/8cfe35092774112a65908a41b1fb5c0f089d83f4))

## [1.19.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.4...catalyst-server-1.19.5) (2024-11-15)

## [1.19.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.3...catalyst-server-1.19.4) (2024-11-15)

## [1.19.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.2...catalyst-server-1.19.3) (2024-11-15)

## [1.20.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.5...catalyst-server-1.20.0) (2024-11-18)


### Features

* Add tenant path Route and add missing dependency ([8cfe350](https://github.com/ACME-General/catalyst-innovation/commit/8cfe35092774112a65908a41b1fb5c0f089d83f4))

## [1.19.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.4...catalyst-server-1.19.5) (2024-11-15)

## [1.19.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.3...catalyst-server-1.19.4) (2024-11-15)

## [1.19.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.2...catalyst-server-1.19.3) (2024-11-15)

## [1.19.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.1...catalyst-server-1.19.2) (2024-11-14)

## [1.19.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.4...catalyst-server-1.19.5) (2024-11-15)

## [1.19.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.3...catalyst-server-1.19.4) (2024-11-15)

## [1.19.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.2...catalyst-server-1.19.3) (2024-11-15)

## [1.19.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.1...catalyst-server-1.19.2) (2024-11-14)

## [1.19.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.0...catalyst-server-1.19.1) (2024-11-12)

## [1.19.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.3...catalyst-server-1.19.4) (2024-11-15)

## [1.19.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.2...catalyst-server-1.19.3) (2024-11-15)

## [1.19.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.1...catalyst-server-1.19.2) (2024-11-14)

## [1.19.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.0...catalyst-server-1.19.1) (2024-11-12)

## [1.19.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.13...catalyst-server-1.19.0) (2024-11-12)


### Features

* Include staging route ([#169](https://github.com/ACME-General/catalyst-innovation/issues/169)) ([540d75a](https://github.com/ACME-General/catalyst-innovation/commit/540d75a3cd18bffa012a14846535ac2b8b670747))

## [1.19.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.2...catalyst-server-1.19.3) (2024-11-15)

## [1.19.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.1...catalyst-server-1.19.2) (2024-11-14)

## [1.19.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.0...catalyst-server-1.19.1) (2024-11-12)

## [1.19.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.13...catalyst-server-1.19.0) (2024-11-12)


### Features

* Include staging route ([#169](https://github.com/ACME-General/catalyst-innovation/issues/169)) ([540d75a](https://github.com/ACME-General/catalyst-innovation/commit/540d75a3cd18bffa012a14846535ac2b8b670747))

## [1.18.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.12...catalyst-server-1.18.13) (2024-11-08)

## [1.19.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.1...catalyst-server-1.19.2) (2024-11-14)

## [1.19.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.0...catalyst-server-1.19.1) (2024-11-12)

## [1.19.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.13...catalyst-server-1.19.0) (2024-11-12)


### Features

* Include staging route ([#169](https://github.com/ACME-General/catalyst-innovation/issues/169)) ([540d75a](https://github.com/ACME-General/catalyst-innovation/commit/540d75a3cd18bffa012a14846535ac2b8b670747))

## [1.18.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.12...catalyst-server-1.18.13) (2024-11-08)

## [1.18.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.11...catalyst-server-1.18.12) (2024-11-08)


### Bug Fixes

* event filter on table not updating properly ([15b66d5](https://github.com/ACME-General/catalyst-innovation/commit/15b66d58a3e9b1cc0288dcb480f335f71500300c))

## [1.19.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.19.0...catalyst-server-1.19.1) (2024-11-12)

## [1.19.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.13...catalyst-server-1.19.0) (2024-11-12)


### Features

* Include staging route ([#169](https://github.com/ACME-General/catalyst-innovation/issues/169)) ([540d75a](https://github.com/ACME-General/catalyst-innovation/commit/540d75a3cd18bffa012a14846535ac2b8b670747))

## [1.18.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.12...catalyst-server-1.18.13) (2024-11-08)

## [1.18.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.11...catalyst-server-1.18.12) (2024-11-08)


### Bug Fixes

* event filter on table not updating properly ([15b66d5](https://github.com/ACME-General/catalyst-innovation/commit/15b66d58a3e9b1cc0288dcb480f335f71500300c))

## [1.18.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.10...catalyst-server-1.18.11) (2024-11-08)


### Bug Fixes

* fixed unassigned and 'none' issue within campaign filter ([ab447ec](https://github.com/ACME-General/catalyst-innovation/commit/ab447ec42556c85474313468c9c9e96faddb35f4))

## [1.19.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.13...catalyst-server-1.19.0) (2024-11-12)


### Features

* Include staging route ([#169](https://github.com/ACME-General/catalyst-innovation/issues/169)) ([540d75a](https://github.com/ACME-General/catalyst-innovation/commit/540d75a3cd18bffa012a14846535ac2b8b670747))

## [1.18.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.12...catalyst-server-1.18.13) (2024-11-08)

## [1.18.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.11...catalyst-server-1.18.12) (2024-11-08)


### Bug Fixes

* event filter on table not updating properly ([15b66d5](https://github.com/ACME-General/catalyst-innovation/commit/15b66d58a3e9b1cc0288dcb480f335f71500300c))

## [1.18.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.10...catalyst-server-1.18.11) (2024-11-08)


### Bug Fixes

* fixed unassigned and 'none' issue within campaign filter ([ab447ec](https://github.com/ACME-General/catalyst-innovation/commit/ab447ec42556c85474313468c9c9e96faddb35f4))

## [1.18.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.9...catalyst-server-1.18.10) (2024-11-05)

## [1.18.13](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.12...catalyst-server-1.18.13) (2024-11-08)

## [1.18.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.11...catalyst-server-1.18.12) (2024-11-08)


### Bug Fixes

* event filter on table not updating properly ([15b66d5](https://github.com/ACME-General/catalyst-innovation/commit/15b66d58a3e9b1cc0288dcb480f335f71500300c))

## [1.18.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.10...catalyst-server-1.18.11) (2024-11-08)


### Bug Fixes

* fixed unassigned and 'none' issue within campaign filter ([ab447ec](https://github.com/ACME-General/catalyst-innovation/commit/ab447ec42556c85474313468c9c9e96faddb35f4))

## [1.18.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.9...catalyst-server-1.18.10) (2024-11-05)

## [1.18.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.8...catalyst-server-1.18.9) (2024-11-05)

## [1.18.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.11...catalyst-server-1.18.12) (2024-11-08)


### Bug Fixes

* event filter on table not updating properly ([15b66d5](https://github.com/ACME-General/catalyst-innovation/commit/15b66d58a3e9b1cc0288dcb480f335f71500300c))

## [1.18.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.10...catalyst-server-1.18.11) (2024-11-08)


### Bug Fixes

* fixed unassigned and 'none' issue within campaign filter ([ab447ec](https://github.com/ACME-General/catalyst-innovation/commit/ab447ec42556c85474313468c9c9e96faddb35f4))

## [1.18.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.9...catalyst-server-1.18.10) (2024-11-05)

## [1.18.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.8...catalyst-server-1.18.9) (2024-11-05)

## [1.18.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.7...catalyst-server-1.18.8) (2024-11-04)


### Bug Fixes

* Fix issue with private opportunities not showing on curation page ([#166](https://github.com/ACME-General/catalyst-innovation/issues/166)) ([555ee52](https://github.com/ACME-General/catalyst-innovation/commit/555ee52b084d5d81c6570d15e2558537de0da49d))

## [1.18.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.10...catalyst-server-1.18.11) (2024-11-08)


### Bug Fixes

* fixed unassigned and 'none' issue within campaign filter ([ab447ec](https://github.com/ACME-General/catalyst-innovation/commit/ab447ec42556c85474313468c9c9e96faddb35f4))

## [1.18.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.9...catalyst-server-1.18.10) (2024-11-05)

## [1.18.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.8...catalyst-server-1.18.9) (2024-11-05)

## [1.18.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.7...catalyst-server-1.18.8) (2024-11-04)


### Bug Fixes

* Fix issue with private opportunities not showing on curation page ([#166](https://github.com/ACME-General/catalyst-innovation/issues/166)) ([555ee52](https://github.com/ACME-General/catalyst-innovation/commit/555ee52b084d5d81c6570d15e2558537de0da49d))

## [1.18.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.6...catalyst-server-1.18.7) (2024-10-31)

## [1.18.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.9...catalyst-server-1.18.10) (2024-11-05)

## [1.18.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.8...catalyst-server-1.18.9) (2024-11-05)

## [1.18.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.7...catalyst-server-1.18.8) (2024-11-04)


### Bug Fixes

* Fix issue with private opportunities not showing on curation page ([#166](https://github.com/ACME-General/catalyst-innovation/issues/166)) ([555ee52](https://github.com/ACME-General/catalyst-innovation/commit/555ee52b084d5d81c6570d15e2558537de0da49d))

## [1.18.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.6...catalyst-server-1.18.7) (2024-10-31)

## [1.18.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.5...catalyst-server-1.18.6) (2024-10-30)


### Bug Fixes

* Update resource loading for analytics ([718b14f](https://github.com/ACME-General/catalyst-innovation/commit/718b14f43ee67d1a403c1dd7f1945c4d8f24813e))

## [1.18.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.8...catalyst-server-1.18.9) (2024-11-05)

## [1.18.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.7...catalyst-server-1.18.8) (2024-11-04)


### Bug Fixes

* Fix issue with private opportunities not showing on curation page ([#166](https://github.com/ACME-General/catalyst-innovation/issues/166)) ([555ee52](https://github.com/ACME-General/catalyst-innovation/commit/555ee52b084d5d81c6570d15e2558537de0da49d))

## [1.18.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.6...catalyst-server-1.18.7) (2024-10-31)

## [1.18.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.5...catalyst-server-1.18.6) (2024-10-30)


### Bug Fixes

* Update resource loading for analytics ([718b14f](https://github.com/ACME-General/catalyst-innovation/commit/718b14f43ee67d1a403c1dd7f1945c4d8f24813e))

## [1.18.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.4...catalyst-server-1.18.5) (2024-10-30)


### Bug Fixes

* Bug fix for events for analytics curation ([#165](https://github.com/ACME-General/catalyst-innovation/issues/165)) ([0c2b7b4](https://github.com/ACME-General/catalyst-innovation/commit/0c2b7b4026ab75f2795b9bcfa6b81ee8e1a2e96c))

## [1.18.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.7...catalyst-server-1.18.8) (2024-11-04)


### Bug Fixes

* Fix issue with private opportunities not showing on curation page ([#166](https://github.com/ACME-General/catalyst-innovation/issues/166)) ([555ee52](https://github.com/ACME-General/catalyst-innovation/commit/555ee52b084d5d81c6570d15e2558537de0da49d))

## [1.18.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.6...catalyst-server-1.18.7) (2024-10-31)

## [1.18.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.5...catalyst-server-1.18.6) (2024-10-30)


### Bug Fixes

* Update resource loading for analytics ([718b14f](https://github.com/ACME-General/catalyst-innovation/commit/718b14f43ee67d1a403c1dd7f1945c4d8f24813e))

## [1.18.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.4...catalyst-server-1.18.5) (2024-10-30)


### Bug Fixes

* Bug fix for events for analytics curation ([#165](https://github.com/ACME-General/catalyst-innovation/issues/165)) ([0c2b7b4](https://github.com/ACME-General/catalyst-innovation/commit/0c2b7b4026ab75f2795b9bcfa6b81ee8e1a2e96c))

## [1.18.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.3...catalyst-server-1.18.4) (2024-10-29)


### Bug Fixes

* reverted changes ([a861d45](https://github.com/ACME-General/catalyst-innovation/commit/a861d45c3200786b2b598cdd91e0ea292d010236))

## [1.18.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.6...catalyst-server-1.18.7) (2024-10-31)

## [1.18.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.5...catalyst-server-1.18.6) (2024-10-30)


### Bug Fixes

* Update resource loading for analytics ([718b14f](https://github.com/ACME-General/catalyst-innovation/commit/718b14f43ee67d1a403c1dd7f1945c4d8f24813e))

## [1.18.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.4...catalyst-server-1.18.5) (2024-10-30)


### Bug Fixes

* Bug fix for events for analytics curation ([#165](https://github.com/ACME-General/catalyst-innovation/issues/165)) ([0c2b7b4](https://github.com/ACME-General/catalyst-innovation/commit/0c2b7b4026ab75f2795b9bcfa6b81ee8e1a2e96c))

## [1.18.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.3...catalyst-server-1.18.4) (2024-10-29)


### Bug Fixes

* reverted changes ([a861d45](https://github.com/ACME-General/catalyst-innovation/commit/a861d45c3200786b2b598cdd91e0ea292d010236))

## [1.18.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.2...catalyst-server-1.18.3) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([3a25941](https://github.com/ACME-General/catalyst-innovation/commit/3a25941a1a069f4d57311eea863dadfa63beddca))

## [1.18.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.5...catalyst-server-1.18.6) (2024-10-30)


### Bug Fixes

* Update resource loading for analytics ([718b14f](https://github.com/ACME-General/catalyst-innovation/commit/718b14f43ee67d1a403c1dd7f1945c4d8f24813e))

## [1.18.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.4...catalyst-server-1.18.5) (2024-10-30)


### Bug Fixes

* Bug fix for events for analytics curation ([#165](https://github.com/ACME-General/catalyst-innovation/issues/165)) ([0c2b7b4](https://github.com/ACME-General/catalyst-innovation/commit/0c2b7b4026ab75f2795b9bcfa6b81ee8e1a2e96c))

## [1.18.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.3...catalyst-server-1.18.4) (2024-10-29)


### Bug Fixes

* reverted changes ([a861d45](https://github.com/ACME-General/catalyst-innovation/commit/a861d45c3200786b2b598cdd91e0ea292d010236))

## [1.18.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.2...catalyst-server-1.18.3) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([3a25941](https://github.com/ACME-General/catalyst-innovation/commit/3a25941a1a069f4d57311eea863dadfa63beddca))

## [1.18.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.1...catalyst-server-1.18.2) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([ec1d729](https://github.com/ACME-General/catalyst-innovation/commit/ec1d72972d1229cfccce1df3348dcb317b4bc52e))

## [1.18.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.4...catalyst-server-1.18.5) (2024-10-30)


### Bug Fixes

* Bug fix for events for analytics curation ([#165](https://github.com/ACME-General/catalyst-innovation/issues/165)) ([0c2b7b4](https://github.com/ACME-General/catalyst-innovation/commit/0c2b7b4026ab75f2795b9bcfa6b81ee8e1a2e96c))

## [1.18.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.3...catalyst-server-1.18.4) (2024-10-29)


### Bug Fixes

* reverted changes ([a861d45](https://github.com/ACME-General/catalyst-innovation/commit/a861d45c3200786b2b598cdd91e0ea292d010236))

## [1.18.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.2...catalyst-server-1.18.3) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([3a25941](https://github.com/ACME-General/catalyst-innovation/commit/3a25941a1a069f4d57311eea863dadfa63beddca))

## [1.18.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.1...catalyst-server-1.18.2) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([ec1d729](https://github.com/ACME-General/catalyst-innovation/commit/ec1d72972d1229cfccce1df3348dcb317b4bc52e))

## [1.18.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.0...catalyst-server-1.18.1) (2024-10-28)

## [1.18.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.3...catalyst-server-1.18.4) (2024-10-29)


### Bug Fixes

* reverted changes ([a861d45](https://github.com/ACME-General/catalyst-innovation/commit/a861d45c3200786b2b598cdd91e0ea292d010236))

## [1.18.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.2...catalyst-server-1.18.3) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([3a25941](https://github.com/ACME-General/catalyst-innovation/commit/3a25941a1a069f4d57311eea863dadfa63beddca))

## [1.18.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.1...catalyst-server-1.18.2) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([ec1d729](https://github.com/ACME-General/catalyst-innovation/commit/ec1d72972d1229cfccce1df3348dcb317b4bc52e))

## [1.18.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.0...catalyst-server-1.18.1) (2024-10-28)

## [1.18.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.18.0) (2024-10-28)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.18.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.2...catalyst-server-1.18.3) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([3a25941](https://github.com/ACME-General/catalyst-innovation/commit/3a25941a1a069f4d57311eea863dadfa63beddca))

## [1.18.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.1...catalyst-server-1.18.2) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([ec1d729](https://github.com/ACME-General/catalyst-innovation/commit/ec1d72972d1229cfccce1df3348dcb317b4bc52e))

## [1.18.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.0...catalyst-server-1.18.1) (2024-10-28)

## [1.18.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.18.0) (2024-10-28)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.18.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.1...catalyst-server-1.18.2) (2024-10-29)


### Bug Fixes

* fixing events list dropdown bug in prod ([ec1d729](https://github.com/ACME-General/catalyst-innovation/commit/ec1d72972d1229cfccce1df3348dcb317b4bc52e))

## [1.18.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.0...catalyst-server-1.18.1) (2024-10-28)

## [1.18.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.18.0) (2024-10-28)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.18.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.18.0...catalyst-server-1.18.1) (2024-10-28)

## [1.18.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.18.0) (2024-10-28)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.5.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.1...catalyst-server-1.5.2) (2024-07-23)


### Features

* IN-1101 added tenant 'content' update routine to dbCli ([70fb9bb](https://github.com/ACME-General/catalyst-innovation/commit/70fb9bb8ac966be98375932ce9b294b341c5009c))
* IN-1101 added tenant 'content' update routine to dbCli ([9f41036](https://github.com/ACME-General/catalyst-innovation/commit/9f4103635c36f1a9267583ebb32efdd23af9bcaf))


### Bug Fixes

* IN-1102 stopped grabbing 'newest' key, now grabbing serverVersion ([81048c5](https://github.com/ACME-General/catalyst-innovation/commit/81048c55fcaca461674a15c39f04b1b0222cd9ed))

## [1.18.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.18.0) (2024-10-28)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.5.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.1...catalyst-server-1.5.2) (2024-07-23)


### Features

* IN-1101 added tenant 'content' update routine to dbCli ([70fb9bb](https://github.com/ACME-General/catalyst-innovation/commit/70fb9bb8ac966be98375932ce9b294b341c5009c))
* IN-1101 added tenant 'content' update routine to dbCli ([9f41036](https://github.com/ACME-General/catalyst-innovation/commit/9f4103635c36f1a9267583ebb32efdd23af9bcaf))


### Bug Fixes

* IN-1102 stopped grabbing 'newest' key, now grabbing serverVersion ([81048c5](https://github.com/ACME-General/catalyst-innovation/commit/81048c55fcaca461674a15c39f04b1b0222cd9ed))

## [1.5.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.0...catalyst-server-1.5.1) (2024-07-23)


### Bug Fixes

* added protection for null content json ([8ca2d18](https://github.com/ACME-General/catalyst-innovation/commit/8ca2d18a07a75004cb8f8796a435e84d62af021d))

## [1.17.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.11...catalyst-server-1.17.12) (2024-10-23)

## [1.17.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.10...catalyst-server-1.17.11) (2024-10-23)

## [1.17.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.9...catalyst-server-1.17.10) (2024-10-18)

## [1.17.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.8...catalyst-server-1.17.9) (2024-10-18)


### Bug Fixes

* fixed null issue with tenant.meta.config ([ae511a6](https://github.com/ACME-General/catalyst-innovation/commit/ae511a6f374b30874c3be14e2aac3a888a613402))

## [1.17.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.7...catalyst-server-1.17.8) (2024-10-18)


### Bug Fixes

* IN-1157 fixed semantics with links, searching issues ([8a52aef](https://github.com/ACME-General/catalyst-innovation/commit/8a52aefffd32b63ac293d116e6aa84dec95da5e5))

## [1.17.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.10...catalyst-server-1.17.11) (2024-10-23)

## [1.17.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.9...catalyst-server-1.17.10) (2024-10-18)

## [1.17.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.8...catalyst-server-1.17.9) (2024-10-18)


### Bug Fixes

* fixed null issue with tenant.meta.config ([ae511a6](https://github.com/ACME-General/catalyst-innovation/commit/ae511a6f374b30874c3be14e2aac3a888a613402))

## [1.17.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.7...catalyst-server-1.17.8) (2024-10-18)


### Bug Fixes

* IN-1157 fixed semantics with links, searching issues ([8a52aef](https://github.com/ACME-General/catalyst-innovation/commit/8a52aefffd32b63ac293d116e6aa84dec95da5e5))

## [1.17.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.6...catalyst-server-1.17.7) (2024-10-18)

## [1.17.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.9...catalyst-server-1.17.10) (2024-10-18)

## [1.17.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.8...catalyst-server-1.17.9) (2024-10-18)


### Bug Fixes

* fixed null issue with tenant.meta.config ([ae511a6](https://github.com/ACME-General/catalyst-innovation/commit/ae511a6f374b30874c3be14e2aac3a888a613402))

## [1.17.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.7...catalyst-server-1.17.8) (2024-10-18)


### Bug Fixes

* IN-1157 fixed semantics with links, searching issues ([8a52aef](https://github.com/ACME-General/catalyst-innovation/commit/8a52aefffd32b63ac293d116e6aa84dec95da5e5))

## [1.17.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.6...catalyst-server-1.17.7) (2024-10-18)

## [1.17.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.5...catalyst-server-1.17.6) (2024-10-17)

## [1.17.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.8...catalyst-server-1.17.9) (2024-10-18)


### Bug Fixes

* fixed null issue with tenant.meta.config ([ae511a6](https://github.com/ACME-General/catalyst-innovation/commit/ae511a6f374b30874c3be14e2aac3a888a613402))

## [1.17.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.7...catalyst-server-1.17.8) (2024-10-18)


### Bug Fixes

* IN-1157 fixed semantics with links, searching issues ([8a52aef](https://github.com/ACME-General/catalyst-innovation/commit/8a52aefffd32b63ac293d116e6aa84dec95da5e5))

## [1.17.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.6...catalyst-server-1.17.7) (2024-10-18)

## [1.17.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.5...catalyst-server-1.17.6) (2024-10-17)

## [1.17.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.4...catalyst-server-1.17.5) (2024-10-15)


### Bug Fixes

* tenantSearchStore searchValue now runs as an action ([754570d](https://github.com/ACME-General/catalyst-innovation/commit/754570da63c75469ba5c5d3ca3f6afe40eefe0c9))

## [1.17.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.7...catalyst-server-1.17.8) (2024-10-18)


### Bug Fixes

* IN-1157 fixed semantics with links, searching issues ([8a52aef](https://github.com/ACME-General/catalyst-innovation/commit/8a52aefffd32b63ac293d116e6aa84dec95da5e5))

## [1.17.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.6...catalyst-server-1.17.7) (2024-10-18)

## [1.17.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.5...catalyst-server-1.17.6) (2024-10-17)

## [1.17.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.4...catalyst-server-1.17.5) (2024-10-15)


### Bug Fixes

* tenantSearchStore searchValue now runs as an action ([754570d](https://github.com/ACME-General/catalyst-innovation/commit/754570da63c75469ba5c5d3ca3f6afe40eefe0c9))

## [1.17.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.3...catalyst-server-1.17.4) (2024-10-15)

## [1.17.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.6...catalyst-server-1.17.7) (2024-10-18)

## [1.17.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.5...catalyst-server-1.17.6) (2024-10-17)

## [1.17.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.4...catalyst-server-1.17.5) (2024-10-15)


### Bug Fixes

* tenantSearchStore searchValue now runs as an action ([754570d](https://github.com/ACME-General/catalyst-innovation/commit/754570da63c75469ba5c5d3ca3f6afe40eefe0c9))

## [1.17.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.3...catalyst-server-1.17.4) (2024-10-15)

## [1.17.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.2...catalyst-server-1.17.3) (2024-10-15)

## [1.17.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.5...catalyst-server-1.17.6) (2024-10-17)

## [1.17.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.4...catalyst-server-1.17.5) (2024-10-15)


### Bug Fixes

* tenantSearchStore searchValue now runs as an action ([754570d](https://github.com/ACME-General/catalyst-innovation/commit/754570da63c75469ba5c5d3ca3f6afe40eefe0c9))

## [1.17.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.3...catalyst-server-1.17.4) (2024-10-15)

## [1.17.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.2...catalyst-server-1.17.3) (2024-10-15)

## [1.17.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.1...catalyst-server-1.17.2) (2024-10-14)

## [1.17.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.4...catalyst-server-1.17.5) (2024-10-15)


### Bug Fixes

* tenantSearchStore searchValue now runs as an action ([754570d](https://github.com/ACME-General/catalyst-innovation/commit/754570da63c75469ba5c5d3ca3f6afe40eefe0c9))

## [1.17.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.3...catalyst-server-1.17.4) (2024-10-15)

## [1.17.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.2...catalyst-server-1.17.3) (2024-10-15)

## [1.17.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.1...catalyst-server-1.17.2) (2024-10-14)

## [1.17.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.0...catalyst-server-1.17.1) (2024-10-11)

## [1.17.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.3...catalyst-server-1.17.4) (2024-10-15)

## [1.17.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.2...catalyst-server-1.17.3) (2024-10-15)

## [1.17.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.1...catalyst-server-1.17.2) (2024-10-14)

## [1.17.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.0...catalyst-server-1.17.1) (2024-10-11)

## [1.17.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.12...catalyst-server-1.17.0) (2024-10-10)


### Features

* IN-1153 restyled directory page ([#155](https://github.com/ACME-General/catalyst-innovation/issues/155)) ([d38be6c](https://github.com/ACME-General/catalyst-innovation/commit/d38be6c7e74ccc46910905cad7ecb4959fbc71a4))

## [1.17.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.2...catalyst-server-1.17.3) (2024-10-15)

## [1.17.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.1...catalyst-server-1.17.2) (2024-10-14)

## [1.17.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.0...catalyst-server-1.17.1) (2024-10-11)

## [1.17.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.12...catalyst-server-1.17.0) (2024-10-10)


### Features

* IN-1153 restyled directory page ([#155](https://github.com/ACME-General/catalyst-innovation/issues/155)) ([d38be6c](https://github.com/ACME-General/catalyst-innovation/commit/d38be6c7e74ccc46910905cad7ecb4959fbc71a4))

## [1.16.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.11...catalyst-server-1.16.12) (2024-10-03)

## [1.17.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.1...catalyst-server-1.17.2) (2024-10-14)

## [1.17.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.0...catalyst-server-1.17.1) (2024-10-11)

## [1.17.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.12...catalyst-server-1.17.0) (2024-10-10)


### Features

* IN-1153 restyled directory page ([#155](https://github.com/ACME-General/catalyst-innovation/issues/155)) ([d38be6c](https://github.com/ACME-General/catalyst-innovation/commit/d38be6c7e74ccc46910905cad7ecb4959fbc71a4))

## [1.16.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.11...catalyst-server-1.16.12) (2024-10-03)

## [1.16.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.10...catalyst-server-1.16.11) (2024-10-02)

## [1.17.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.17.0...catalyst-server-1.17.1) (2024-10-11)

## [1.17.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.12...catalyst-server-1.17.0) (2024-10-10)


### Features

* IN-1153 restyled directory page ([#155](https://github.com/ACME-General/catalyst-innovation/issues/155)) ([d38be6c](https://github.com/ACME-General/catalyst-innovation/commit/d38be6c7e74ccc46910905cad7ecb4959fbc71a4))

## [1.16.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.11...catalyst-server-1.16.12) (2024-10-03)

## [1.16.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.10...catalyst-server-1.16.11) (2024-10-02)

## [1.16.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.9...catalyst-server-1.16.10) (2024-10-02)

## [1.17.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.12...catalyst-server-1.17.0) (2024-10-10)


### Features

* IN-1153 restyled directory page ([#155](https://github.com/ACME-General/catalyst-innovation/issues/155)) ([d38be6c](https://github.com/ACME-General/catalyst-innovation/commit/d38be6c7e74ccc46910905cad7ecb4959fbc71a4))

## [1.16.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.11...catalyst-server-1.16.12) (2024-10-03)

## [1.16.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.10...catalyst-server-1.16.11) (2024-10-02)

## [1.16.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.9...catalyst-server-1.16.10) (2024-10-02)

## [1.16.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.8...catalyst-server-1.16.9) (2024-10-01)

## [1.16.12](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.11...catalyst-server-1.16.12) (2024-10-03)

## [1.16.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.10...catalyst-server-1.16.11) (2024-10-02)

## [1.16.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.9...catalyst-server-1.16.10) (2024-10-02)

## [1.16.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.8...catalyst-server-1.16.9) (2024-10-01)

## [1.16.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.7...catalyst-server-1.16.8) (2024-10-01)

## [1.16.11](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.10...catalyst-server-1.16.11) (2024-10-02)

## [1.16.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.9...catalyst-server-1.16.10) (2024-10-02)

## [1.16.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.8...catalyst-server-1.16.9) (2024-10-01)

## [1.16.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.7...catalyst-server-1.16.8) (2024-10-01)

## [1.16.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.6...catalyst-server-1.16.7) (2024-09-27)

## [1.16.10](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.9...catalyst-server-1.16.10) (2024-10-02)

## [1.16.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.8...catalyst-server-1.16.9) (2024-10-01)

## [1.16.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.7...catalyst-server-1.16.8) (2024-10-01)

## [1.16.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.6...catalyst-server-1.16.7) (2024-09-27)

## [1.16.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.5...catalyst-server-1.16.6) (2024-09-27)

## [1.16.9](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.8...catalyst-server-1.16.9) (2024-10-01)

## [1.16.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.7...catalyst-server-1.16.8) (2024-10-01)

## [1.16.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.6...catalyst-server-1.16.7) (2024-09-27)

## [1.16.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.5...catalyst-server-1.16.6) (2024-09-27)

## [1.16.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.4...catalyst-server-1.16.5) (2024-09-24)

## [1.16.8](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.7...catalyst-server-1.16.8) (2024-10-01)

## [1.16.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.6...catalyst-server-1.16.7) (2024-09-27)

## [1.16.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.5...catalyst-server-1.16.6) (2024-09-27)

## [1.16.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.4...catalyst-server-1.16.5) (2024-09-24)

## [1.16.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.3...catalyst-server-1.16.4) (2024-09-24)

## [1.16.7](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.6...catalyst-server-1.16.7) (2024-09-27)

## [1.16.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.5...catalyst-server-1.16.6) (2024-09-27)

## [1.16.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.4...catalyst-server-1.16.5) (2024-09-24)

## [1.16.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.3...catalyst-server-1.16.4) (2024-09-24)

## [1.16.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.2...catalyst-server-1.16.3) (2024-09-24)

## [1.16.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.5...catalyst-server-1.16.6) (2024-09-27)

## [1.16.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.4...catalyst-server-1.16.5) (2024-09-24)

## [1.16.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.3...catalyst-server-1.16.4) (2024-09-24)

## [1.16.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.2...catalyst-server-1.16.3) (2024-09-24)

## [1.16.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.1...catalyst-server-1.16.2) (2024-09-24)

## [1.16.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.4...catalyst-server-1.16.5) (2024-09-24)

## [1.16.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.3...catalyst-server-1.16.4) (2024-09-24)

## [1.16.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.2...catalyst-server-1.16.3) (2024-09-24)

## [1.16.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.1...catalyst-server-1.16.2) (2024-09-24)

## [1.16.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.0...catalyst-server-1.16.1) (2024-09-23)

## [1.16.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.3...catalyst-server-1.16.4) (2024-09-24)

## [1.16.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.2...catalyst-server-1.16.3) (2024-09-24)

## [1.16.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.1...catalyst-server-1.16.2) (2024-09-24)

## [1.16.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.0...catalyst-server-1.16.1) (2024-09-23)

## [1.16.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.16.0) (2024-09-21)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.16.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.2...catalyst-server-1.16.3) (2024-09-24)

## [1.16.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.1...catalyst-server-1.16.2) (2024-09-24)

## [1.16.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.0...catalyst-server-1.16.1) (2024-09-23)

## [1.16.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.16.0) (2024-09-21)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.16.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.1...catalyst-server-1.16.2) (2024-09-24)

## [1.16.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.0...catalyst-server-1.16.1) (2024-09-23)

## [1.16.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.16.0) (2024-09-21)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.16.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.16.0...catalyst-server-1.16.1) (2024-09-23)

## [1.16.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.16.0) (2024-09-21)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.5.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.1...catalyst-server-1.5.2) (2024-07-23)


### Features

* IN-1101 added tenant 'content' update routine to dbCli ([70fb9bb](https://github.com/ACME-General/catalyst-innovation/commit/70fb9bb8ac966be98375932ce9b294b341c5009c))
* IN-1101 added tenant 'content' update routine to dbCli ([9f41036](https://github.com/ACME-General/catalyst-innovation/commit/9f4103635c36f1a9267583ebb32efdd23af9bcaf))


### Bug Fixes

* IN-1102 stopped grabbing 'newest' key, now grabbing serverVersion ([81048c5](https://github.com/ACME-General/catalyst-innovation/commit/81048c55fcaca461674a15c39f04b1b0222cd9ed))

## [1.16.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.16.0) (2024-09-21)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))

## [1.5.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.3...catalyst-server-1.5.4) (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))
* IN-1102 added more protection for when content does not exist ([7f57748](https://github.com/ACME-General/catalyst-innovation/commit/7f57748e418fd73a8760145e55aa3ea638372a99))

## [1.5.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.2...catalyst-server-1.5.3) (2024-07-23)

## [1.5.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.1...catalyst-server-1.5.2) (2024-07-23)


### Features

* IN-1101 added tenant 'content' update routine to dbCli ([70fb9bb](https://github.com/ACME-General/catalyst-innovation/commit/70fb9bb8ac966be98375932ce9b294b341c5009c))
* IN-1101 added tenant 'content' update routine to dbCli ([9f41036](https://github.com/ACME-General/catalyst-innovation/commit/9f4103635c36f1a9267583ebb32efdd23af9bcaf))


### Bug Fixes

* IN-1102 stopped grabbing 'newest' key, now grabbing serverVersion ([81048c5](https://github.com/ACME-General/catalyst-innovation/commit/81048c55fcaca461674a15c39f04b1b0222cd9ed))

## [1.5.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.0...catalyst-server-1.5.1) (2024-07-23)


### Bug Fixes

* added protection for null content json ([8ca2d18](https://github.com/ACME-General/catalyst-innovation/commit/8ca2d18a07a75004cb8f8796a435e84d62af021d))

## [1.15.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.5...catalyst-server-1.15.6) (2024-09-18)


### Bug Fixes

* Fix reporting calculations bug for showing events on rollup ([35fa166](https://github.com/ACME-General/catalyst-innovation/commit/35fa16604b4779c5ba0d0798d837b64c9891da06))

## [1.15.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.4...catalyst-server-1.15.5) (2024-09-18)


### Bug Fixes

* Address issue for filterOtherPrivateOpps not being init ([180c2ed](https://github.com/ACME-General/catalyst-innovation/commit/180c2ed4910ebdfb5bb2610fa7d0646257a83d5e))

## [1.15.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.3...catalyst-server-1.15.4) (2024-09-18)


### Bug Fixes

* re-flipped filter flags ([fa0a0c6](https://github.com/ACME-General/catalyst-innovation/commit/fa0a0c6f2df869af4042264a473c3cdc986b8380))

## [1.15.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.2...catalyst-server-1.15.3) (2024-09-18)


### Bug Fixes

* Flip entity default value to true ([f589c73](https://github.com/ACME-General/catalyst-innovation/commit/f589c73a40057350df9435f7513cc7621869ad2b))
* removed expected result for flag. This will be fixed with better tests surrounding private ([60edb45](https://github.com/ACME-General/catalyst-innovation/commit/60edb45983cc18fe5466a5c15e0a1cf0c0ff3a59))

## [1.15.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.1...catalyst-server-1.15.2) (2024-09-18)


### Bug Fixes

* flipped filterOtherPrivateOpportunities flag ([0e49ac8](https://github.com/ACME-General/catalyst-innovation/commit/0e49ac8c10f136453fded31c35a6dc39a5a80136))

## [1.15.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.4...catalyst-server-1.15.5) (2024-09-18)


### Bug Fixes

* Address issue for filterOtherPrivateOpps not being init ([180c2ed](https://github.com/ACME-General/catalyst-innovation/commit/180c2ed4910ebdfb5bb2610fa7d0646257a83d5e))

## [1.15.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.3...catalyst-server-1.15.4) (2024-09-18)


### Bug Fixes

* re-flipped filter flags ([fa0a0c6](https://github.com/ACME-General/catalyst-innovation/commit/fa0a0c6f2df869af4042264a473c3cdc986b8380))

## [1.15.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.2...catalyst-server-1.15.3) (2024-09-18)


### Bug Fixes

* Flip entity default value to true ([f589c73](https://github.com/ACME-General/catalyst-innovation/commit/f589c73a40057350df9435f7513cc7621869ad2b))
* removed expected result for flag. This will be fixed with better tests surrounding private ([60edb45](https://github.com/ACME-General/catalyst-innovation/commit/60edb45983cc18fe5466a5c15e0a1cf0c0ff3a59))

## [1.15.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.1...catalyst-server-1.15.2) (2024-09-18)


### Bug Fixes

* flipped filterOtherPrivateOpportunities flag ([0e49ac8](https://github.com/ACME-General/catalyst-innovation/commit/0e49ac8c10f136453fded31c35a6dc39a5a80136))

## [1.15.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.0...catalyst-server-1.15.1) (2024-09-17)

## [1.15.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.3...catalyst-server-1.15.4) (2024-09-18)


### Bug Fixes

* re-flipped filter flags ([fa0a0c6](https://github.com/ACME-General/catalyst-innovation/commit/fa0a0c6f2df869af4042264a473c3cdc986b8380))

## [1.15.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.2...catalyst-server-1.15.3) (2024-09-18)


### Bug Fixes

* Flip entity default value to true ([f589c73](https://github.com/ACME-General/catalyst-innovation/commit/f589c73a40057350df9435f7513cc7621869ad2b))
* removed expected result for flag. This will be fixed with better tests surrounding private ([60edb45](https://github.com/ACME-General/catalyst-innovation/commit/60edb45983cc18fe5466a5c15e0a1cf0c0ff3a59))

## [1.15.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.1...catalyst-server-1.15.2) (2024-09-18)


### Bug Fixes

* flipped filterOtherPrivateOpportunities flag ([0e49ac8](https://github.com/ACME-General/catalyst-innovation/commit/0e49ac8c10f136453fded31c35a6dc39a5a80136))

## [1.15.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.0...catalyst-server-1.15.1) (2024-09-17)

## [1.15.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.13.0...catalyst-server-1.15.0) (2024-09-16)


### Features

* release notes and analytics private tooltip ([e891f9e](https://github.com/ACME-General/catalyst-innovation/commit/e891f9ebacb011cf035c3ece907ff7b87d79157b))


### Bug Fixes

* moved visibility columns to different positions ([52fb10f](https://github.com/ACME-General/catalyst-innovation/commit/52fb10facc87ce8caadc12ab6eac8d1b517f963b))

## [1.15.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.2...catalyst-server-1.15.3) (2024-09-18)


### Bug Fixes

* Flip entity default value to true ([f589c73](https://github.com/ACME-General/catalyst-innovation/commit/f589c73a40057350df9435f7513cc7621869ad2b))
* removed expected result for flag. This will be fixed with better tests surrounding private ([60edb45](https://github.com/ACME-General/catalyst-innovation/commit/60edb45983cc18fe5466a5c15e0a1cf0c0ff3a59))

## [1.15.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.1...catalyst-server-1.15.2) (2024-09-18)


### Bug Fixes

* flipped filterOtherPrivateOpportunities flag ([0e49ac8](https://github.com/ACME-General/catalyst-innovation/commit/0e49ac8c10f136453fded31c35a6dc39a5a80136))

## [1.15.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.0...catalyst-server-1.15.1) (2024-09-17)

## [1.15.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.13.0...catalyst-server-1.15.0) (2024-09-16)


### Features

* release notes and analytics private tooltip ([e891f9e](https://github.com/ACME-General/catalyst-innovation/commit/e891f9ebacb011cf035c3ece907ff7b87d79157b))


### Bug Fixes

* moved visibility columns to different positions ([52fb10f](https://github.com/ACME-General/catalyst-innovation/commit/52fb10facc87ce8caadc12ab6eac8d1b517f963b))

## [1.13.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.1...catalyst-server-1.13.0) (2024-09-14)


### Features

* private filtering for analytics page when flag is on ([000707c](https://github.com/ACME-General/catalyst-innovation/commit/000707ce61a3710fb67f5aad0a180a12840224f0))

## [1.15.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.1...catalyst-server-1.15.2) (2024-09-18)


### Bug Fixes

* flipped filterOtherPrivateOpportunities flag ([0e49ac8](https://github.com/ACME-General/catalyst-innovation/commit/0e49ac8c10f136453fded31c35a6dc39a5a80136))

## [1.15.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.0...catalyst-server-1.15.1) (2024-09-17)

## [1.15.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.13.0...catalyst-server-1.15.0) (2024-09-16)


### Features

* release notes and analytics private tooltip ([e891f9e](https://github.com/ACME-General/catalyst-innovation/commit/e891f9ebacb011cf035c3ece907ff7b87d79157b))


### Bug Fixes

* moved visibility columns to different positions ([52fb10f](https://github.com/ACME-General/catalyst-innovation/commit/52fb10facc87ce8caadc12ab6eac8d1b517f963b))

## [1.13.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.1...catalyst-server-1.13.0) (2024-09-14)


### Features

* private filtering for analytics page when flag is on ([000707c](https://github.com/ACME-General/catalyst-innovation/commit/000707ce61a3710fb67f5aad0a180a12840224f0))

## [1.12.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.0...catalyst-server-1.12.1) (2024-09-13)

## [1.15.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.15.0...catalyst-server-1.15.1) (2024-09-17)

## [1.15.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.13.0...catalyst-server-1.15.0) (2024-09-16)


### Features

* release notes and analytics private tooltip ([e891f9e](https://github.com/ACME-General/catalyst-innovation/commit/e891f9ebacb011cf035c3ece907ff7b87d79157b))


### Bug Fixes

* moved visibility columns to different positions ([52fb10f](https://github.com/ACME-General/catalyst-innovation/commit/52fb10facc87ce8caadc12ab6eac8d1b517f963b))

## [1.13.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.1...catalyst-server-1.13.0) (2024-09-14)


### Features

* private filtering for analytics page when flag is on ([000707c](https://github.com/ACME-General/catalyst-innovation/commit/000707ce61a3710fb67f5aad0a180a12840224f0))

## [1.12.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.0...catalyst-server-1.12.1) (2024-09-13)

## [1.12.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.3...catalyst-server-1.12.0) (2024-09-13)


### Features

* Contextual icon adjusted to private column ([b832428](https://github.com/ACME-General/catalyst-innovation/commit/b8324283ed24de1c92977e40e8be24218a5eea9d))

## [1.15.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.13.0...catalyst-server-1.15.0) (2024-09-16)


### Features

* release notes and analytics private tooltip ([e891f9e](https://github.com/ACME-General/catalyst-innovation/commit/e891f9ebacb011cf035c3ece907ff7b87d79157b))


### Bug Fixes

* moved visibility columns to different positions ([52fb10f](https://github.com/ACME-General/catalyst-innovation/commit/52fb10facc87ce8caadc12ab6eac8d1b517f963b))

## [1.13.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.1...catalyst-server-1.13.0) (2024-09-14)


### Features

* private filtering for analytics page when flag is on ([000707c](https://github.com/ACME-General/catalyst-innovation/commit/000707ce61a3710fb67f5aad0a180a12840224f0))

## [1.12.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.0...catalyst-server-1.12.1) (2024-09-13)

## [1.12.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.3...catalyst-server-1.12.0) (2024-09-13)


### Features

* Contextual icon adjusted to private column ([b832428](https://github.com/ACME-General/catalyst-innovation/commit/b8324283ed24de1c92977e40e8be24218a5eea9d))

## [1.11.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.2...catalyst-server-1.11.3) (2024-09-13)

## [1.14.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.13.0...catalyst-server-1.14.0) (2024-09-16)


### Features

* release notes and analytics private tooltip ([e891f9e](https://github.com/ACME-General/catalyst-innovation/commit/e891f9ebacb011cf035c3ece907ff7b87d79157b))


### Bug Fixes

* moved visibility columns to different positions ([52fb10f](https://github.com/ACME-General/catalyst-innovation/commit/52fb10facc87ce8caadc12ab6eac8d1b517f963b))

## [1.13.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.1...catalyst-server-1.13.0) (2024-09-14)


### Features

* private filtering for analytics page when flag is on ([000707c](https://github.com/ACME-General/catalyst-innovation/commit/000707ce61a3710fb67f5aad0a180a12840224f0))

## [1.12.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.0...catalyst-server-1.12.1) (2024-09-13)

## [1.12.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.3...catalyst-server-1.12.0) (2024-09-13)


### Features

* Contextual icon adjusted to private column ([b832428](https://github.com/ACME-General/catalyst-innovation/commit/b8324283ed24de1c92977e40e8be24218a5eea9d))

## [1.11.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.2...catalyst-server-1.11.3) (2024-09-13)

## [1.13.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.1...catalyst-server-1.13.0) (2024-09-14)


### Features

* private filtering for analytics page when flag is on ([000707c](https://github.com/ACME-General/catalyst-innovation/commit/000707ce61a3710fb67f5aad0a180a12840224f0))

## [1.12.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.0...catalyst-server-1.12.1) (2024-09-13)

## [1.12.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.3...catalyst-server-1.12.0) (2024-09-13)


### Features

* Contextual icon adjusted to private column ([b832428](https://github.com/ACME-General/catalyst-innovation/commit/b8324283ed24de1c92977e40e8be24218a5eea9d))

## [1.11.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.2...catalyst-server-1.11.3) (2024-09-13)

## [1.11.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.1...catalyst-server-1.11.2) (2024-09-13)

## [1.12.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.12.0...catalyst-server-1.12.1) (2024-09-13)

## [1.12.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.3...catalyst-server-1.12.0) (2024-09-13)


### Features

* Contextual icon adjusted to private column ([b832428](https://github.com/ACME-General/catalyst-innovation/commit/b8324283ed24de1c92977e40e8be24218a5eea9d))

## [1.11.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.2...catalyst-server-1.11.3) (2024-09-13)

## [1.11.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.1...catalyst-server-1.11.2) (2024-09-13)

## [1.11.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.0...catalyst-server-1.11.1) (2024-09-13)

## [1.12.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.3...catalyst-server-1.12.0) (2024-09-13)


### Features

* Contextual icon adjusted to private column ([b832428](https://github.com/ACME-General/catalyst-innovation/commit/b8324283ed24de1c92977e40e8be24218a5eea9d))

## [1.11.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.2...catalyst-server-1.11.3) (2024-09-13)

## [1.11.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.1...catalyst-server-1.11.2) (2024-09-13)

## [1.11.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.0...catalyst-server-1.11.1) (2024-09-13)

## [1.11.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.5...catalyst-server-1.11.0) (2024-09-12)


### Features

* Add flag for tenant to vew all private opportunities ([041833a](https://github.com/ACME-General/catalyst-innovation/commit/041833a64b6521e02b4a781ce4504222140493c4))
* flipped boolean checking filterChildPrivateOpportunities and removed logs ([7b2bc6b](https://github.com/ACME-General/catalyst-innovation/commit/7b2bc6b0b67a0b451ce14427b2f3c4bd01729be3))
* handling the filterChildPrivateOpportunitys flag ([6cb0b6d](https://github.com/ACME-General/catalyst-innovation/commit/6cb0b6d14b5bc55d15f05431ed083929310ce2c1))
* Private flag added to various pages. Column Added to tenant metas. ([ab52928](https://github.com/ACME-General/catalyst-innovation/commit/ab529285ac473ee5b25d71a7afefe0005b9d6e9d))
* Remove only flag in test ([c556ef6](https://github.com/ACME-General/catalyst-innovation/commit/c556ef687f40d16c5683bc0048719183a1e82664))
* Remove serverConfig.json from pathfinder ([e1c60f7](https://github.com/ACME-General/catalyst-innovation/commit/e1c60f79faffac41965d1a56d245ed67678d1768))
* Update controllers ([b9ac5e9](https://github.com/ACME-General/catalyst-innovation/commit/b9ac5e924f29b9365283345f2c4d6e5324092629))
* update tooling for utilizing fetch for private opportunities ([05bec4d](https://github.com/ACME-General/catalyst-innovation/commit/05bec4d5f3c475452dc86052bafdca96b2875c58))
* Update visibility usage in controllers ([33cee52](https://github.com/ACME-General/catalyst-innovation/commit/33cee52ed7f136670798ff52c46ae7b078c22f41))


### Bug Fixes

* typo with 'filterOtherPrivateOpportunities' ([6211412](https://github.com/ACME-General/catalyst-innovation/commit/621141264516b61a0a7ba283c7349b8a2bc41dde))

## [1.11.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.2...catalyst-server-1.11.3) (2024-09-13)

## [1.11.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.1...catalyst-server-1.11.2) (2024-09-13)

## [1.11.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.0...catalyst-server-1.11.1) (2024-09-13)

## [1.11.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.5...catalyst-server-1.11.0) (2024-09-12)


### Features

* Add flag for tenant to vew all private opportunities ([041833a](https://github.com/ACME-General/catalyst-innovation/commit/041833a64b6521e02b4a781ce4504222140493c4))
* flipped boolean checking filterChildPrivateOpportunities and removed logs ([7b2bc6b](https://github.com/ACME-General/catalyst-innovation/commit/7b2bc6b0b67a0b451ce14427b2f3c4bd01729be3))
* handling the filterChildPrivateOpportunitys flag ([6cb0b6d](https://github.com/ACME-General/catalyst-innovation/commit/6cb0b6d14b5bc55d15f05431ed083929310ce2c1))
* Private flag added to various pages. Column Added to tenant metas. ([ab52928](https://github.com/ACME-General/catalyst-innovation/commit/ab529285ac473ee5b25d71a7afefe0005b9d6e9d))
* Remove only flag in test ([c556ef6](https://github.com/ACME-General/catalyst-innovation/commit/c556ef687f40d16c5683bc0048719183a1e82664))
* Remove serverConfig.json from pathfinder ([e1c60f7](https://github.com/ACME-General/catalyst-innovation/commit/e1c60f79faffac41965d1a56d245ed67678d1768))
* Update controllers ([b9ac5e9](https://github.com/ACME-General/catalyst-innovation/commit/b9ac5e924f29b9365283345f2c4d6e5324092629))
* update tooling for utilizing fetch for private opportunities ([05bec4d](https://github.com/ACME-General/catalyst-innovation/commit/05bec4d5f3c475452dc86052bafdca96b2875c58))
* Update visibility usage in controllers ([33cee52](https://github.com/ACME-General/catalyst-innovation/commit/33cee52ed7f136670798ff52c46ae7b078c22f41))


### Bug Fixes

* typo with 'filterOtherPrivateOpportunities' ([6211412](https://github.com/ACME-General/catalyst-innovation/commit/621141264516b61a0a7ba283c7349b8a2bc41dde))

## [1.9.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.4...catalyst-server-1.9.5) (2024-09-12)

## [1.11.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.1...catalyst-server-1.11.2) (2024-09-13)

## [1.11.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.0...catalyst-server-1.11.1) (2024-09-13)

## [1.11.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.5...catalyst-server-1.11.0) (2024-09-12)


### Features

* Add flag for tenant to vew all private opportunities ([041833a](https://github.com/ACME-General/catalyst-innovation/commit/041833a64b6521e02b4a781ce4504222140493c4))
* flipped boolean checking filterChildPrivateOpportunities and removed logs ([7b2bc6b](https://github.com/ACME-General/catalyst-innovation/commit/7b2bc6b0b67a0b451ce14427b2f3c4bd01729be3))
* handling the filterChildPrivateOpportunitys flag ([6cb0b6d](https://github.com/ACME-General/catalyst-innovation/commit/6cb0b6d14b5bc55d15f05431ed083929310ce2c1))
* Private flag added to various pages. Column Added to tenant metas. ([ab52928](https://github.com/ACME-General/catalyst-innovation/commit/ab529285ac473ee5b25d71a7afefe0005b9d6e9d))
* Remove only flag in test ([c556ef6](https://github.com/ACME-General/catalyst-innovation/commit/c556ef687f40d16c5683bc0048719183a1e82664))
* Remove serverConfig.json from pathfinder ([e1c60f7](https://github.com/ACME-General/catalyst-innovation/commit/e1c60f79faffac41965d1a56d245ed67678d1768))
* Update controllers ([b9ac5e9](https://github.com/ACME-General/catalyst-innovation/commit/b9ac5e924f29b9365283345f2c4d6e5324092629))
* update tooling for utilizing fetch for private opportunities ([05bec4d](https://github.com/ACME-General/catalyst-innovation/commit/05bec4d5f3c475452dc86052bafdca96b2875c58))
* Update visibility usage in controllers ([33cee52](https://github.com/ACME-General/catalyst-innovation/commit/33cee52ed7f136670798ff52c46ae7b078c22f41))


### Bug Fixes

* typo with 'filterOtherPrivateOpportunities' ([6211412](https://github.com/ACME-General/catalyst-innovation/commit/621141264516b61a0a7ba283c7349b8a2bc41dde))

## [1.9.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.4...catalyst-server-1.9.5) (2024-09-12)

## [1.9.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.3...catalyst-server-1.9.4) (2024-09-10)

## [1.11.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.11.0...catalyst-server-1.11.1) (2024-09-13)

## [1.11.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.5...catalyst-server-1.11.0) (2024-09-12)


### Features

* Add flag for tenant to vew all private opportunities ([041833a](https://github.com/ACME-General/catalyst-innovation/commit/041833a64b6521e02b4a781ce4504222140493c4))
* flipped boolean checking filterChildPrivateOpportunities and removed logs ([7b2bc6b](https://github.com/ACME-General/catalyst-innovation/commit/7b2bc6b0b67a0b451ce14427b2f3c4bd01729be3))
* handling the filterChildPrivateOpportunitys flag ([6cb0b6d](https://github.com/ACME-General/catalyst-innovation/commit/6cb0b6d14b5bc55d15f05431ed083929310ce2c1))
* Private flag added to various pages. Column Added to tenant metas. ([ab52928](https://github.com/ACME-General/catalyst-innovation/commit/ab529285ac473ee5b25d71a7afefe0005b9d6e9d))
* Remove only flag in test ([c556ef6](https://github.com/ACME-General/catalyst-innovation/commit/c556ef687f40d16c5683bc0048719183a1e82664))
* Remove serverConfig.json from pathfinder ([e1c60f7](https://github.com/ACME-General/catalyst-innovation/commit/e1c60f79faffac41965d1a56d245ed67678d1768))
* Update controllers ([b9ac5e9](https://github.com/ACME-General/catalyst-innovation/commit/b9ac5e924f29b9365283345f2c4d6e5324092629))
* update tooling for utilizing fetch for private opportunities ([05bec4d](https://github.com/ACME-General/catalyst-innovation/commit/05bec4d5f3c475452dc86052bafdca96b2875c58))
* Update visibility usage in controllers ([33cee52](https://github.com/ACME-General/catalyst-innovation/commit/33cee52ed7f136670798ff52c46ae7b078c22f41))


### Bug Fixes

* typo with 'filterOtherPrivateOpportunities' ([6211412](https://github.com/ACME-General/catalyst-innovation/commit/621141264516b61a0a7ba283c7349b8a2bc41dde))

## [1.9.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.4...catalyst-server-1.9.5) (2024-09-12)

## [1.9.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.3...catalyst-server-1.9.4) (2024-09-10)

## [1.9.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.2...catalyst-server-1.9.3) (2024-09-09)

## [1.11.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.5...catalyst-server-1.11.0) (2024-09-12)


### Features

* Add flag for tenant to vew all private opportunities ([041833a](https://github.com/ACME-General/catalyst-innovation/commit/041833a64b6521e02b4a781ce4504222140493c4))
* flipped boolean checking filterChildPrivateOpportunities and removed logs ([7b2bc6b](https://github.com/ACME-General/catalyst-innovation/commit/7b2bc6b0b67a0b451ce14427b2f3c4bd01729be3))
* handling the filterChildPrivateOpportunitys flag ([6cb0b6d](https://github.com/ACME-General/catalyst-innovation/commit/6cb0b6d14b5bc55d15f05431ed083929310ce2c1))
* Private flag added to various pages. Column Added to tenant metas. ([ab52928](https://github.com/ACME-General/catalyst-innovation/commit/ab529285ac473ee5b25d71a7afefe0005b9d6e9d))
* Remove only flag in test ([c556ef6](https://github.com/ACME-General/catalyst-innovation/commit/c556ef687f40d16c5683bc0048719183a1e82664))
* Remove serverConfig.json from pathfinder ([e1c60f7](https://github.com/ACME-General/catalyst-innovation/commit/e1c60f79faffac41965d1a56d245ed67678d1768))
* Update controllers ([b9ac5e9](https://github.com/ACME-General/catalyst-innovation/commit/b9ac5e924f29b9365283345f2c4d6e5324092629))
* update tooling for utilizing fetch for private opportunities ([05bec4d](https://github.com/ACME-General/catalyst-innovation/commit/05bec4d5f3c475452dc86052bafdca96b2875c58))
* Update visibility usage in controllers ([33cee52](https://github.com/ACME-General/catalyst-innovation/commit/33cee52ed7f136670798ff52c46ae7b078c22f41))


### Bug Fixes

* typo with 'filterOtherPrivateOpportunities' ([6211412](https://github.com/ACME-General/catalyst-innovation/commit/621141264516b61a0a7ba283c7349b8a2bc41dde))

## [1.9.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.4...catalyst-server-1.9.5) (2024-09-12)

## [1.9.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.3...catalyst-server-1.9.4) (2024-09-10)

## [1.9.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.2...catalyst-server-1.9.3) (2024-09-09)

## [1.9.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.1...catalyst-server-1.9.2) (2024-09-06)

## [1.10.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.5...catalyst-server-1.10.0) (2024-09-12)


### Features

* Add flag for tenant to vew all private opportunities ([041833a](https://github.com/ACME-General/catalyst-innovation/commit/041833a64b6521e02b4a781ce4504222140493c4))
* flipped boolean checking filterChildPrivateOpportunities and removed logs ([7b2bc6b](https://github.com/ACME-General/catalyst-innovation/commit/7b2bc6b0b67a0b451ce14427b2f3c4bd01729be3))
* handling the filterChildPrivateOpportunitys flag ([6cb0b6d](https://github.com/ACME-General/catalyst-innovation/commit/6cb0b6d14b5bc55d15f05431ed083929310ce2c1))
* Private flag added to various pages. Column Added to tenant metas. ([ab52928](https://github.com/ACME-General/catalyst-innovation/commit/ab529285ac473ee5b25d71a7afefe0005b9d6e9d))
* Remove only flag in test ([c556ef6](https://github.com/ACME-General/catalyst-innovation/commit/c556ef687f40d16c5683bc0048719183a1e82664))
* Remove serverConfig.json from pathfinder ([e1c60f7](https://github.com/ACME-General/catalyst-innovation/commit/e1c60f79faffac41965d1a56d245ed67678d1768))
* Update controllers ([b9ac5e9](https://github.com/ACME-General/catalyst-innovation/commit/b9ac5e924f29b9365283345f2c4d6e5324092629))
* update tooling for utilizing fetch for private opportunities ([05bec4d](https://github.com/ACME-General/catalyst-innovation/commit/05bec4d5f3c475452dc86052bafdca96b2875c58))
* Update visibility usage in controllers ([33cee52](https://github.com/ACME-General/catalyst-innovation/commit/33cee52ed7f136670798ff52c46ae7b078c22f41))

## [1.9.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.4...catalyst-server-1.9.5) (2024-09-12)

## [1.9.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.3...catalyst-server-1.9.4) (2024-09-10)

## [1.9.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.2...catalyst-server-1.9.3) (2024-09-09)

## [1.9.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.1...catalyst-server-1.9.2) (2024-09-06)

## [1.9.5](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.4...catalyst-server-1.9.5) (2024-09-12)

## [1.9.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.3...catalyst-server-1.9.4) (2024-09-10)

## [1.9.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.2...catalyst-server-1.9.3) (2024-09-09)

## [1.9.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.1...catalyst-server-1.9.2) (2024-09-06)

## [1.9.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.0...catalyst-server-1.9.1) (2024-09-05)

## [1.9.4](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.3...catalyst-server-1.9.4) (2024-09-10)

## [1.9.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.2...catalyst-server-1.9.3) (2024-09-09)

## [1.9.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.1...catalyst-server-1.9.2) (2024-09-06)

## [1.9.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.0...catalyst-server-1.9.1) (2024-09-05)

## [1.9.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.2...catalyst-server-1.9.0) (2024-09-05)


### Features

* IN-1121 - Adding visibility property and new tests for opportunities ([#133](https://github.com/ACME-General/catalyst-innovation/issues/133)) ([e75cdd5](https://github.com/ACME-General/catalyst-innovation/commit/e75cdd598ba5b0d59ed4a083982f3b2c3d4c0e8d))

## [1.9.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.2...catalyst-server-1.9.3) (2024-09-09)

## [1.9.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.1...catalyst-server-1.9.2) (2024-09-06)

## [1.9.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.0...catalyst-server-1.9.1) (2024-09-05)

## [1.9.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.2...catalyst-server-1.9.0) (2024-09-05)


### Features

* IN-1121 - Adding visibility property and new tests for opportunities ([#133](https://github.com/ACME-General/catalyst-innovation/issues/133)) ([e75cdd5](https://github.com/ACME-General/catalyst-innovation/commit/e75cdd598ba5b0d59ed4a083982f3b2c3d4c0e8d))

## [1.8.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.1...catalyst-server-1.8.2) (2024-08-28)

## [1.9.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.1...catalyst-server-1.9.2) (2024-09-06)

## [1.9.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.0...catalyst-server-1.9.1) (2024-09-05)

## [1.9.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.2...catalyst-server-1.9.0) (2024-09-05)


### Features

* IN-1121 - Adding visibility property and new tests for opportunities ([#133](https://github.com/ACME-General/catalyst-innovation/issues/133)) ([e75cdd5](https://github.com/ACME-General/catalyst-innovation/commit/e75cdd598ba5b0d59ed4a083982f3b2c3d4c0e8d))

## [1.8.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.1...catalyst-server-1.8.2) (2024-08-28)

## [1.8.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.0...catalyst-server-1.8.1) (2024-08-26)


### Bug Fixes

* IN-1114 added a flattened userprivileges to UserStore ([cc4cdc7](https://github.com/ACME-General/catalyst-innovation/commit/cc4cdc7ce4d2adac99233df9123afb9ade2cd4d9))

## [1.9.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.9.0...catalyst-server-1.9.1) (2024-09-05)

## [1.9.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.2...catalyst-server-1.9.0) (2024-09-05)


### Features

* IN-1121 - Adding visibility property and new tests for opportunities ([#133](https://github.com/ACME-General/catalyst-innovation/issues/133)) ([e75cdd5](https://github.com/ACME-General/catalyst-innovation/commit/e75cdd598ba5b0d59ed4a083982f3b2c3d4c0e8d))

## [1.8.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.1...catalyst-server-1.8.2) (2024-08-28)

## [1.8.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.0...catalyst-server-1.8.1) (2024-08-26)


### Bug Fixes

* IN-1114 added a flattened userprivileges to UserStore ([cc4cdc7](https://github.com/ACME-General/catalyst-innovation/commit/cc4cdc7ce4d2adac99233df9123afb9ade2cd4d9))

## [1.8.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.7.0...catalyst-server-1.8.0) (2024-08-26)


### Features

* IN-1082 created richer textbox component for web ([0c6fb54](https://github.com/ACME-General/catalyst-innovation/commit/0c6fb54ae475bf862aa2db687c446ecd7af80c2c))
* IN-1082 created richer textbox component for web ([8b961ac](https://github.com/ACME-General/catalyst-innovation/commit/8b961ace805e65ab39f2bd833a23e67247d2b8f5))

## [1.9.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.2...catalyst-server-1.9.0) (2024-09-05)


### Features

* IN-1121 - Adding visibility property and new tests for opportunities ([#133](https://github.com/ACME-General/catalyst-innovation/issues/133)) ([e75cdd5](https://github.com/ACME-General/catalyst-innovation/commit/e75cdd598ba5b0d59ed4a083982f3b2c3d4c0e8d))

## [1.8.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.1...catalyst-server-1.8.2) (2024-08-28)

## [1.8.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.0...catalyst-server-1.8.1) (2024-08-26)


### Bug Fixes

* IN-1114 added a flattened userprivileges to UserStore ([cc4cdc7](https://github.com/ACME-General/catalyst-innovation/commit/cc4cdc7ce4d2adac99233df9123afb9ade2cd4d9))

## [1.8.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.7.0...catalyst-server-1.8.0) (2024-08-26)


### Features

* IN-1082 created richer textbox component for web ([0c6fb54](https://github.com/ACME-General/catalyst-innovation/commit/0c6fb54ae475bf862aa2db687c446ecd7af80c2c))
* IN-1082 created richer textbox component for web ([8b961ac](https://github.com/ACME-General/catalyst-innovation/commit/8b961ace805e65ab39f2bd833a23e67247d2b8f5))

## [1.7.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.3...catalyst-server-1.7.0) (2024-08-19)


### Features

* IN-1103 IN-1108 IN-1109 IN-1110 IN-1111 IN-1115 add new tenants ([41349a0](https://github.com/ACME-General/catalyst-innovation/commit/41349a096f65870945f56b6f1946ff1f353eff6f))

## [1.8.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.1...catalyst-server-1.8.2) (2024-08-28)

## [1.8.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.0...catalyst-server-1.8.1) (2024-08-26)


### Bug Fixes

* IN-1114 added a flattened userprivileges to UserStore ([cc4cdc7](https://github.com/ACME-General/catalyst-innovation/commit/cc4cdc7ce4d2adac99233df9123afb9ade2cd4d9))

## [1.8.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.7.0...catalyst-server-1.8.0) (2024-08-26)


### Features

* IN-1082 created richer textbox component for web ([0c6fb54](https://github.com/ACME-General/catalyst-innovation/commit/0c6fb54ae475bf862aa2db687c446ecd7af80c2c))
* IN-1082 created richer textbox component for web ([8b961ac](https://github.com/ACME-General/catalyst-innovation/commit/8b961ace805e65ab39f2bd833a23e67247d2b8f5))

## [1.7.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.3...catalyst-server-1.7.0) (2024-08-19)


### Features

* IN-1103 IN-1108 IN-1109 IN-1110 IN-1111 IN-1115 add new tenants ([41349a0](https://github.com/ACME-General/catalyst-innovation/commit/41349a096f65870945f56b6f1946ff1f353eff6f))

## [1.6.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.2...catalyst-server-1.6.3) (2024-08-14)

## [1.5.4-patch.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.0...catalyst-server-1.5.4-patch.1) (2024-08-06)


### Bug Fixes

* bug where opp list was pulling all associated tenants ([f431457](https://github.com/ACME-General/catalyst-innovation/commit/f431457215554c7fede57f7f3e1a8d652559392b))

## [1.8.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.8.0...catalyst-server-1.8.1) (2024-08-26)


### Bug Fixes

* IN-1114 added a flattened userprivileges to UserStore ([cc4cdc7](https://github.com/ACME-General/catalyst-innovation/commit/cc4cdc7ce4d2adac99233df9123afb9ade2cd4d9))

## [1.8.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.7.0...catalyst-server-1.8.0) (2024-08-26)


### Features

* IN-1082 created richer textbox component for web ([0c6fb54](https://github.com/ACME-General/catalyst-innovation/commit/0c6fb54ae475bf862aa2db687c446ecd7af80c2c))
* IN-1082 created richer textbox component for web ([8b961ac](https://github.com/ACME-General/catalyst-innovation/commit/8b961ace805e65ab39f2bd833a23e67247d2b8f5))

## [1.7.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.3...catalyst-server-1.7.0) (2024-08-19)


### Features

* IN-1103 IN-1108 IN-1109 IN-1110 IN-1111 IN-1115 add new tenants ([41349a0](https://github.com/ACME-General/catalyst-innovation/commit/41349a096f65870945f56b6f1946ff1f353eff6f))

## [1.6.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.2...catalyst-server-1.6.3) (2024-08-14)

## [1.5.4-patch.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.0...catalyst-server-1.5.4-patch.1) (2024-08-06)


### Bug Fixes

* bug where opp list was pulling all associated tenants ([f431457](https://github.com/ACME-General/catalyst-innovation/commit/f431457215554c7fede57f7f3e1a8d652559392b))

## [1.6.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.1...catalyst-server-1.6.2) (2024-08-13)


### Features

* IN-1113 allow admin to make 'unverified' users, new users ([6360f59](https://github.com/ACME-General/catalyst-innovation/commit/6360f593aeb39dbaa809fe2cab9ee1610a436abd))

## [1.8.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.7.0...catalyst-server-1.8.0) (2024-08-26)


### Features

* IN-1082 created richer textbox component for web ([0c6fb54](https://github.com/ACME-General/catalyst-innovation/commit/0c6fb54ae475bf862aa2db687c446ecd7af80c2c))
* IN-1082 created richer textbox component for web ([8b961ac](https://github.com/ACME-General/catalyst-innovation/commit/8b961ace805e65ab39f2bd833a23e67247d2b8f5))

## [1.7.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.3...catalyst-server-1.7.0) (2024-08-19)


### Features

* IN-1103 IN-1108 IN-1109 IN-1110 IN-1111 IN-1115 add new tenants ([41349a0](https://github.com/ACME-General/catalyst-innovation/commit/41349a096f65870945f56b6f1946ff1f353eff6f))

## [1.6.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.2...catalyst-server-1.6.3) (2024-08-14)

## [1.5.4-patch.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.0...catalyst-server-1.5.4-patch.1) (2024-08-06)


### Bug Fixes

* bug where opp list was pulling all associated tenants ([f431457](https://github.com/ACME-General/catalyst-innovation/commit/f431457215554c7fede57f7f3e1a8d652559392b))

## [1.6.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.1...catalyst-server-1.6.2) (2024-08-13)


### Features

* IN-1113 allow admin to make 'unverified' users, new users ([6360f59](https://github.com/ACME-General/catalyst-innovation/commit/6360f593aeb39dbaa809fe2cab9ee1610a436abd))

## [1.6.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4-patch.1...catalyst-server-1.6.1) (2024-08-13)


### Features

* IN-1106 update user privilege groups and expose Analyst role. ([#129](https://github.com/ACME-General/catalyst-innovation/issues/129)) ([160cf35](https://github.com/ACME-General/catalyst-innovation/commit/160cf3515302c1992239a09fc9bc1f9e2e99d673))
* IN-1112 add copy to centcom config [skip ci] ([85b7952](https://github.com/ACME-General/catalyst-innovation/commit/85b79524aa239358f4516ea0599bb8ae1b19e8e3))

## [1.6.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.6...catalyst-server-1.6.0) (2024-08-06)


### Features

* IN-1104 add 1st ID ([1b42f0f](https://github.com/ACME-General/catalyst-innovation/commit/1b42f0f64fbd83b5487c5451e98be5658e96fb1b))

## [1.5.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.5.6) (2024-08-01)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))
* IN-1103 IN-1104 add setaf and bigredone ([6ee1f89](https://github.com/ACME-General/catalyst-innovation/commit/6ee1f899779a58a69376d526b899d42308ea60b9))
* IN-1105 how to add new tenant docs ([c57a4b7](https://github.com/ACME-General/catalyst-innovation/commit/c57a4b7e59e4c84bec8f4b0b302d1ee0d85db5c9))


### Bug Fixes

* dbCli help message fix ([eb97661](https://github.com/ACME-General/catalyst-innovation/commit/eb97661499db94a8bbe197daf62c643d059c3480))

## [1.7.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.3...catalyst-server-1.7.0) (2024-08-19)


### Features

* IN-1103 IN-1108 IN-1109 IN-1110 IN-1111 IN-1115 add new tenants ([41349a0](https://github.com/ACME-General/catalyst-innovation/commit/41349a096f65870945f56b6f1946ff1f353eff6f))

## [1.6.3](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.2...catalyst-server-1.6.3) (2024-08-14)

## [1.5.4-patch.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.0...catalyst-server-1.5.4-patch.1) (2024-08-06)


### Bug Fixes

* bug where opp list was pulling all associated tenants ([f431457](https://github.com/ACME-General/catalyst-innovation/commit/f431457215554c7fede57f7f3e1a8d652559392b))

## [1.6.2](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.1...catalyst-server-1.6.2) (2024-08-13)


### Features

* IN-1113 allow admin to make 'unverified' users, new users ([6360f59](https://github.com/ACME-General/catalyst-innovation/commit/6360f593aeb39dbaa809fe2cab9ee1610a436abd))

## [1.6.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4-patch.1...catalyst-server-1.6.1) (2024-08-13)


### Features

* IN-1106 update user privilege groups and expose Analyst role. ([#129](https://github.com/ACME-General/catalyst-innovation/issues/129)) ([160cf35](https://github.com/ACME-General/catalyst-innovation/commit/160cf3515302c1992239a09fc9bc1f9e2e99d673))
* IN-1112 add copy to centcom config [skip ci] ([85b7952](https://github.com/ACME-General/catalyst-innovation/commit/85b79524aa239358f4516ea0599bb8ae1b19e8e3))

## [1.6.0](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.6...catalyst-server-1.6.0) (2024-08-06)


### Features

* IN-1104 add 1st ID ([1b42f0f](https://github.com/ACME-General/catalyst-innovation/commit/1b42f0f64fbd83b5487c5451e98be5658e96fb1b))

## [1.5.6](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.5.4...catalyst-server-1.5.6) (2024-08-01)


### Features

* IN-1101 fixed readme text ([3305b2e](https://github.com/ACME-General/catalyst-innovation/commit/3305b2e511e1294a0d79acd5110d2604d7cd008f))
* IN-1103 IN-1104 add setaf and bigredone ([6ee1f89](https://github.com/ACME-General/catalyst-innovation/commit/6ee1f899779a58a69376d526b899d42308ea60b9))
* IN-1105 how to add new tenant docs ([c57a4b7](https://github.com/ACME-General/catalyst-innovation/commit/c57a4b7e59e4c84bec8f4b0b302d1ee0d85db5c9))


### Bug Fixes

* dbCli help message fix ([eb97661](https://github.com/ACME-General/catalyst-innovation/commit/eb97661499db94a8bbe197daf62c643d059c3480))

## [1.5.4-patch.1](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.6.0...catalyst-server-1.5.4-patch.1) (2024-08-06)


### Bug Fixes

* bug where opp list was pulling all associated tenants ([f431457](https://github.com/ACME-General/catalyst-innovation/commit/f431457215554c7fede57f7f3e1a8d652559392b))

## 1.6.3 (2024-08-14)

## 1.6.2 (2024-08-13)

## 1.6.1 (2024-08-13)

## 1.6.0 (2024-08-06)


### Features

* IN-1104 add 1st ID ([1b42f0f](https://github.com/ACME-General/catalyst-innovation/commit/1b42f0f64fbd83b5487c5451e98be5658e96fb1b))

## 1.5.6 (2024-08-01)

## 1.5.5 (2024-07-24)

## 1.5.4 (2024-07-23)


### Bug Fixes

* IN-1102 added more protection for when content does not exist ([9f0e1ba](https://github.com/ACME-General/catalyst-innovation/commit/9f0e1ba21f5ea8175b0feee43c93e2a6f53372dd))

## 1.5.3 (2024-07-23)

## 1.5.2 (2024-07-23)

## 1.5.1 (2024-07-23)

## 1.5.0 (2024-07-23)


### Features

* IN-1102 Move release notes to server-side tenantMeta.content.releaseNotes ([4f439ae](https://github.com/ACME-General/catalyst-innovation/commit/4f439aef94084ff40c5a0ddf4187e4454579804d))

## 1.4.0 (2024-07-22)


### Features

* IN-1101 move release notes to server-side tenantMeta.content.releaseNotes ([3f30a9f](https://github.com/ACME-General/catalyst-innovation/commit/3f30a9feca71a064c09da5f988ea6341213671b8))

## 1.3.30 (2024-07-22)

## 1.3.29 (2024-07-22)


### Bug Fixes

* fixed test ([05b9f14](https://github.com/ACME-General/catalyst-innovation/commit/05b9f143e160f231a48a46de42b64aff786b79e0))

## 1.3.28 (2024-07-17)

## 1.3.27 (2024-07-17)

## 1.3.26 (2024-07-17)

## 1.3.25 (2024-07-17)

## 1.3.24 (2024-07-16)

## 1.3.23 (2024-07-15)

## 1.3.22 (2024-07-15)

## 1.3.21 (2024-07-15)


### Bug Fixes

* IN-1087 fixing empty scope issue in multi-tenants ([f19cb2f](https://github.com/ACME-General/catalyst-innovation/commit/f19cb2f1b8e7740ceb4e779f105764899a0158d3))

## 1.3.20 (2024-07-12)

## 1.3.19 (2024-07-11)

## 1.3.18 (2024-07-11)

## 1.3.17 (2024-07-10)

## 1.3.16 (2024-07-09)

## 1.3.15 (2024-07-08)

## 1.3.14 (2024-07-08)

## 1.3.13 (2024-07-05)

## 1.3.12 (2024-07-05)

## 1.3.11 (2024-07-04)

## 1.3.10 (2024-07-03)

## 1.3.9 (2024-07-03)

## 1.3.8 (2024-07-03)

## 1.3.7 (2024-07-03)


### Bug Fixes

* IN-1074 build ([1ebe490](https://github.com/ACME-General/catalyst-innovation/commit/1ebe4902459b74c2f4c0df8cd545576700de2706))

## 1.3.6 (2024-07-02)


### Bug Fixes

* empty data issue resolved for pie charts ([182fe9c](https://github.com/ACME-General/catalyst-innovation/commit/182fe9ca64d59ef98e9f849979056a9d04dddbdc))

## 1.3.5 (2024-07-01)

## 1.3.4 (2024-06-27)

## 1.3.3 (2024-06-26)

## 1.3.2 (2024-06-24)

## 1.3.1 (2024-06-24)

## 1.3.0 (2024-06-24)


### Features

* download to jpeg capability ([17eaba5](https://github.com/ACME-General/catalyst-innovation/commit/17eaba5d9220f2fbbe68fac3f3eeb4d7d0e317b3))

## 1.2.31 (2024-06-21)

## 1.2.30 (2024-06-20)

## 1.2.29 (2024-06-20)

## 1.2.28 (2024-06-18)


### Bug Fixes

* Update config text4 for 3mdtf tenant per tenant request. ([4365494](https://github.com/ACME-General/catalyst-innovation/commit/4365494c70f4263327e6b968019a5cf7365545de))

## 1.2.27 (2024-06-18)

## 1.2.26 (2024-06-17)

## [1.2.25](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.2.24...catalyst-server-1.2.25) (2024-06-13)

## 1.2.24 (2024-06-13)

## 1.2.24 (2024-06-13)

## 1.2.22 (2024-06-13)

## 1.2.21 (2024-06-10)

## [1.2.20](https://github.com/ACME-General/catalyst-innovation/compare/catalyst-server-1.2.19...catalyst-server-1.2.20) (2024-06-08)

## 1.2.19 (2024-06-07)

## 1.2.19 (2024-06-07)

## 1.2.18 (2024-06-06)

## 1.2.17 (2024-06-06)

## 1.2.16 (2024-06-06)

## 1.2.15 (2024-06-05)

## 1.2.14 (2024-06-05)
