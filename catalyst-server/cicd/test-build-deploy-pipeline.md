# CICD - TEST, Build and Deploy Cluster Update
The [CICD - TEST, Build and Deploy Cluster Update](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=6) pipeline is used to test, build and deploy development changes to test, staging and production. When the pipeline is triggered it always executese our automated tests and if code is merged, builds then deploys the changes to our staging environment. The process then waits for approval before pushing to test or prod.

## Pipeline Triggers
The steps in [test-build-deploy-pipeline.yml](./test-build-deploy-pipeline.md) are triggered by two types of GitHub events. The triggers are defined in the pipeline using Microsofts [Triggers](https://dev.azure.com/ACME-General/catalyst-innovation/_apps/hub/ms.vss-ciworkflow.build-ci-hub?_a=edit-build-definition&id=6&view=Tab_Triggers)

The event triggers are fired if:
1. Pull Request is created against any branch starting with "IN_Sprint_"
2. Code is merged into any branch that starts with the name "IN_Sprint_"

## Approvals
If changes are merged into a branch that begins with "IN_Sprint_", those changes are automatically deployed to staging. For these changes to be delivered to test and production, appovals must be provided. Once the pipeline finishes delivering to staging and email is triggered to the designated approvers.

The approvals are setup using [Azure DevOps Environments](https://dev.azure.com/ACME-General/catalyst-innovation/_environments). This pipeline uses three environments:
- [catalyst-server-staging](https://dev.azure.com/ACME-General/catalyst-innovation/_environments/6?view=deployments)
- [catalyst-server-test](https://dev.azure.com/ACME-General/catalyst-innovation/_environments/8?view=deployments)
- [catalyst-server-prod](https://dev.azure.com/ACME-General/catalyst-innovation/_environments/7?view=deployments)

Each environment has a [Approvals and checks](https://dev.azure.com/ACME-General/catalyst-innovation/_environments/8?view=checks) section you can use to setup the appoval process.

Currently our approval process waits 5 min for the approver to approve the release. After the 5 min, the process is skipped. Approvals are granted inside the pipeline "Run" by clicking the "Review" button that appears on screen. When "Review" button is pressed a slide bar appears to walk you through this process.

### Rerun Approval
You can rerun the approval process to deliver the changes to test or prod by opening the pipeline [Runs](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=6).

1. Chosing the run you want deliver. Each "Run" is associated with either a pull request or merge. Pull requests are excluded from the approval process.
2. After you chose and open your "Run", you should see a "Retry Stage" button for either "Release to test" or "Release to production"
3. Press the "Retry Stage" button. You will get a prompt asking if you are sure you want to rerun the stage. Click yes
4. A new email notification will be sent to the approver
5. If you are an administrator, you can approve the job after step 3. A button will appear on screen that says "Review". Click the "Review" button and approve the request.

# Links
- [CICD - TEST, Build and Deploy Cluster Update Pipeline Runs](https://dev.azure.com/ACME-General/catalyst-innovation/_build?definitionId=6)
- [Environments](https://dev.azure.com/ACME-General/catalyst-innovation/_environments)
- [Microsoft Pipeline Approval Checks](https://learn.microsoft.com/en-us/azure/devops/pipelines/process/approvals?view=azure-devops&tabs=check-pass)
- [Azure Devops Overview](./devops-overview.md)