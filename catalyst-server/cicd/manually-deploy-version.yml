# This yml file is used by Azure DevOps to manually deliver updates to environments based
# on the image you version you provide. 
#
# The deployment includes pushing updated cluster information to Kubernetes containing
# which image to deploy. It will only deploy images that have already been created and
# stored in our container registry.
#
# This manual process is available to assist in scenario's where rollback is necessary and
# an automated approach is not realistic.
#
# Created by: <PERSON>sha

parameters:
- name: image
  displayName: Redeploy Semantic Version
  type: string
  default: "1.2.25"

- name: environment
  displayName: Deploy to environment
  type: string
  default: "staging"
  values:
  - staging
  - test
  - prod

trigger: none

variables:
  - name: buildDate
    value: $(date '+%Y%m%d')

name: $(buildDate).$(Rev:.r)

stages:
  - stage: manualRelease
    displayName: 'Manually deploy previously built version to environment'
    jobs:
      - deployment: deploy
        displayName: 'Deploy version to environment'
        environment: manual-release
        continueOnError: false
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                  name: 'checkoutBranch'
                  displayName: 'Checkout source branch $(Build.SourceBranchName)'

                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      IMAGE_VERSION=${{ parameters.image }}
                      ENVIRONMENT=${{ parameters.environment }}
                      SERVER_YML=catalyst-server-${ENVIRONMENT}.yml
                      SERVER_API_YML=catalyst-api-server-${ENVIRONMENT}.yml
                      echo "##vso[task.setvariable variable=ENV_FILE;isoutput=true]$(staging_env_file)"
                      echo "##vso[task.setvariable variable=IMAGE_VERSION;isoutput=true]$IMAGE_VERSION"
                      echo "##vso[task.setvariable variable=SERVER_YML;isoutput=true]$SERVER_YML"
                      echo "##vso[task.setvariable variable=SERVER_API_YML;isoutput=true]$SERVER_API_YML"
                      echo "##vso[task.setvariable variable=SERVER_CLUSTER;isoutput=true]$(default_cluster)"
                      echo "##vso[task.setvariable variable=ENVIRONMENT;isoutput=true]$ENVIRONMENT"
                      echo "##vso[task.setvariable variable=SECRET_NAME;isoutput=true]$(staging_secret_name)"
                      echo "##vso[build.updatebuildnumber]"${ENVIRONMENT}-${IMAGE_VERSION}_$(buildDate)""
                      if [ $ENVIRONMENT == "test" ]; then
                        echo "##vso[task.setvariable variable=ENV_FILE;isoutput=true]$(test_env_file)"
                        echo "##vso[task.setvariable variable=SECRET_NAME;isoutput=true]$(test_secret_name)"
                      elif [ $ENVIRONMENT == "prod" ]; then
                        echo "##vso[task.setvariable variable=ENV_FILE;isoutput=true]$(prod_env_file)"
                        echo "##vso[task.setvariable variable=SERVER_CLUSTER;isoutput=true]$(prod_cluster)"
                        echo "##vso[task.setvariable variable=SECRET_NAME;isoutput=true]$(prod_secret_name)"
                      fi
                  name: envValues
                  displayName: 'Set environment run values'
                
                - task: DownloadSecureFile@1
                  name: productionEnvFile
                  condition: ${{ eq(parameters.environment, 'prod') }}
                  displayName: 'Download production env file'
                  inputs:
                    secureFile: $(prod_env_file)

                - task: DownloadSecureFile@1
                  name: testEnvFile
                  condition: ${{ eq(parameters.environment, 'test' ) }}
                  displayName: 'Download test env file'
                  inputs:
                    secureFile: $(test_env_file)

                - task: DownloadSecureFile@1
                  name: stagingEnvFile
                  condition: ${{ eq(parameters.environment, 'staging') }}
                  displayName: 'Download staging env file'
                  inputs:
                    secureFile: $(staging_env_file)

                - task: CopyFiles@2
                  displayName: 'Copy env file to deploy'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(envValues.ENV_FILE)'
                    OverWrite: true
                    TargetFolder: '$(System.DefaultWorkingDirectory)/catalyst-server/deploy/'

                - task: UseNode@1
                  inputs:
                    version: $(nodeVersion)
                  displayName: 'Install Node.js'

                # NOTE: Uses deployChanges environment variable to determine if the changes should be applied to the cluster
                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      echo 'Deploying Version to $(envValues.ENVIRONMENT): ENV_FILE: $(envValues.ENV_FILE) IMAGE: $(envValues.IMAGE_VERSION) API_FILE: $(envValues.SERVER_API_YML) SERVER_FILE: $(envValues.SERVER_YML) CLUSTER: $(envValues.SERVER_CLUSTER)'
                      if [ $(envValues.ENVIRONMENT) == "prod" ]; then
                        kubectl config use-context $(prod_cluster)
                      else
                        kubectl config use-context $(default_cluster)
                      fi
                      kubectl config get-contexts
                      if [ $(deployChanges) = true ]; then
                        kubectl --context $(envValues.SERVER_CLUSTER) delete secret $(envValues.SECRET_NAME)
                        kubectl --context $(envValues.SERVER_CLUSTER) create secret generic $(envValues.SECRET_NAME) --from-env-file=$(envValues.ENV_FILE)
                        echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_YML)
                        sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_YML)
                        echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_API_YML)
                        sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_API_YML)
                        echo kubectl --context $(envValues.SERVER_CLUSTER) apply -f ./$(envValues.SERVER_YML)
                        kubectl --context $(envValues.SERVER_CLUSTER) apply -f ./$(envValues.SERVER_YML)
                        echo kubectl --context $(envValues.SERVER_CLUSTER) apply -f ./$(envValues.SERVER_API_YML)
                        kubectl --context $(envValues.SERVER_CLUSTER) apply -f ./$(envValues.SERVER_API_YML)
                      else
                        echo "secret name $(envValues.SECRET_NAME) and env file: $(envValues.ENV_FILE)"
                        echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_YML)
                        sed -i "s/catalystinnovation.azurecr.us\\/catalyst-server:.*$/catalystinnovation.azurecr.us\\/catalyst-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_YML)
                        echo sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_API_YML)
                        sed -i "s/catalystinnovation.azurecr.us\\/catalyst-api-server:.*$/catalystinnovation.azurecr.us\\/catalyst-api-server:$(envValues.IMAGE_VERSION)/g" ./$(envValues.SERVER_API_YML)
                      fi
                    workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server/deploy
                    failOnStderr: true
                  name: deployClusterUpdate
                  displayName: 'Configure and Apply cluster update'

                - task: CopyFiles@2
                  inputs:
                    sourceFolder: '$(System.DefaultWorkingDirectory)/catalyst-server/deploy'
                    contents: |
                      catalyst-*.yml
                    targetFolder: '$(Build.ArtifactStagingDirectory)'
                    CleanTargetFolder: true
                  displayName: 'Copy files for deployment'

                - task: PublishPipelineArtifact@1
                  inputs:
                    artifactName: catalyst-server-manual-deploy
                    targetPath: '$(Build.ArtifactStagingDirectory)'
                    publishLocation: 'pipeline'
                  displayName: 'Deployment artifacts'
