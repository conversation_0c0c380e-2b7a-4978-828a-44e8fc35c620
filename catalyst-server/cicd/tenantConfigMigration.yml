# This yml file is used by Azure DevOps to update our environments tenant configuration data.
# This is a manual process of execution and will push config files located in this project into
# the targeted environment database.
#
# Created by: <PERSON>sha

parameters:
- name: environment
  displayName: Deploy to environment
  type: string
  default: "staging"
  values:
  - staging
  - test
  - prod

variables:
  envStaging: staging
  envTest: test
  envProduction: prod
  buildDate: $(date '+%Y%m%d')

trigger: none

name: $(buildDate).$(Rev:.r)

stages:
  - stage: manualUpdate
    displayName: 'Updating selected environment database'
    jobs:
      - deployment: updateEnvironmentConfigTheme
        displayName: 'Update env tenant config and theme'
        environment: tenant-configuration-update
        continueOnError: false
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                  name: 'checkoutBranch'
                  displayName: 'Checkout source branch $(Build.SourceBranchName)'

                - task: UseNode@1
                  inputs:
                    version: $(nodeVersion)
                  displayName: 'Install Node.js'

                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      ENVIRONMENT=${{ parameters.environment }}
                      echo "##vso[task.setvariable variable=ENV_FILE;isoutput=true]$(staging_env_file)"
                      echo "##vso[task.setvariable variable=ENVIRONMENT;isoutput=true]$ENVIRONMENT"
                      if [ $ENVIRONMENT == $(envTest) ]; then
                        echo "##vso[task.setvariable variable=ENV_FILE;isoutput=true]$(test_env_file)"
                      elif [ $ENVIRONMENT == $(envProduction) ]; then
                        echo "##vso[task.setvariable variable=ENV_FILE;isoutput=true]$(prod_env_file)"
                      fi
                      echo "##vso[build.updatebuildnumber]"${ENVIRONMENT}-$(buildDate)""
                  name: envValues
                  displayName: 'Set environment values'
                
                - task: DownloadSecureFile@1
                  name: productionEnvFile
                  condition: ${{ eq(parameters.environment, 'prod') }}
                  displayName: 'Download production env file'
                  inputs:
                    secureFile: $(prod_env_file)

                - task: DownloadSecureFile@1
                  name: testEnvFile
                  condition: ${{ eq(parameters.environment, 'test') }}
                  displayName: 'Download test env file'
                  inputs:
                    secureFile: $(test_env_file)

                - task: DownloadSecureFile@1
                  name: stagingEnvFile
                  condition: ${{ eq(parameters.environment, 'staging') }}
                  displayName: 'Download staging env file'
                  inputs:
                    secureFile: $(staging_env_file)

                - task: CopyFiles@2
                  displayName: 'Copy env file to catalyst-server folder'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(envValues.ENV_FILE)'
                    OverWrite: true
                    TargetFolder: '$(System.DefaultWorkingDirectory)/catalyst-server/'

                - task: Npm@1
                  displayName: "Set prod env file"
                  condition: ${{ eq(parameters.environment, 'prod') }}
                  inputs:
                    command: custom
                    customCommand: 'run prod-env'
                    workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

                - task: Npm@1
                  displayName: "Set test env file"
                  condition: ${{ eq(parameters.environment, 'test') }}
                  inputs:
                    command: custom
                    customCommand: 'run test-env'
                    workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

                - task: Npm@1
                  displayName: "Set staging env file"
                  condition: ${{ eq(parameters.environment, 'staging') }}
                  inputs:
                    command: custom
                    customCommand: 'run staging-env'
                    workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

                - task: Npm@1
                  displayName: "Install catalyst-server dependencies"
                  inputs:
                    command: ci
                    workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

                - template: ./templates/stepsBashUpdateAllTenantConfigs.yml

                - task: CopyFiles@2
                  inputs:
                    sourceFolder: '$(System.DefaultWorkingDirectory)/catalyst-server/'
                    contents: |
                      bin/*.sh
                      config/**/*.json
                    targetFolder: '$(Build.ArtifactStagingDirectory)'
                    CleanTargetFolder: true
                  displayName: 'Copy files used'

                - task: PublishPipelineArtifact@1
                  inputs:
                    artifactName: catalyst-server-configs
                    targetPath: '$(Build.ArtifactStagingDirectory)'
                    publishLocation: 'pipeline'
                  displayName: 'Deployment artifacts'
