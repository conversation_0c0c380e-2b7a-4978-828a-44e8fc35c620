# Node.js

# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

variables:
  - name: pipelineVersion
    ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
      value: 'PR_'
    ${{ else }}:
      value: ''
  - name: buildDate
    value: $(date '+%Y%m%d') # $(date '+%Y%m%d_%H.%M.%S')

name: $(pipelineVersion)$(buildDate)
# name: $(pipeline_version)_$(Date:yyyyMMdd)$(Rev:.r)

stages:
  - stage: Test
    jobs:
      - job: A
        displayName: 'Clean, Install dependencies and build all projects.'
        continueOnError: 'false'
        workspace:
          clean: all
        steps:
          - checkout: self
            clean: true
            fetchTags: true
            persistCredentials: true
            name: 'checkoutBranch'
            displayName: 'Checkout source branch $(Build.SourceBranchName)'

          - task: UseNode@1
            inputs:
              version: $(nodeVersion)
            displayName: 'Install Node.js'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                kubectl config use-context $(prod_cluster)
                kubectl config get-contexts
                PACKAGE_VERSION=$(node -p "require('./package.json').version")
                echo "package_version: $PACKAGE_VERSION"
                echo "BUILD_DEF=$(Build.DefinitionName)"
                echo "BUILD_VER=$(Build.DefinitionVersion)"
                echo "JOBNAME=$(System.JobDisplayName)"
                echo "buildDate: $(buildDate)"
                echo "##vso[build.updatebuildnumber]"${PACKAGE_VERSION}_$(buildDate)""
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: contextSwitchProd
            displayName: 'Swith to prod context'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                kubectl config use-context $(default_cluster)
                kubectl config get-contexts
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: contextSwitchDefault
            displayName: 'Swith to default context'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                isActive=$(sudo systemctl is-active postgresql)
                echo "Postgres status: ${isActive}"
                if [ $isActive = "active" ]; then
                  echo "Stoping postgres"
                  sudo service postgresql stop
                fi
                echo "Prune all previous built images"
                docker image prune -af
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: stopPostgres
            displayName: 'Stop Postgres if active'
            continueOnError: true

          # - task: Bash@3
          #   inputs:
          #     targetType: 'inline'
          #     script: |
          #       docker-compose up -d
          #     workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
          #   name: setupTestDb
          #   displayName: 'Setup Postgres DB'

          # - task: Bash@3
          #   condition: always()
          #   inputs:
          #     targetType: 'inline'
          #     script: |
          #       docker-compose down
          #     workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
          #   name: tearDownTestDb
          #   displayName: 'Tear down postgres db'

          - task: Bash@3
            condition: eq(variables.deployChanges, true)
            inputs:
              targetType: 'inline'
              script: |
                echo "I should only run when deployChanges is true: $(deployChanges)"
            name: truethTest
            displayName: 'condition check: deployChanges is true'

          - task: Bash@3
            condition: ne(variables.deployChanges, true)
            inputs:
              targetType: 'inline'
              script: |
                echo "I should only run when deployChanges is false: $(deployChanges)"
            name: falsyTest
            displayName: 'condition check: deployChanges is false'
            
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                if [ $(deployChanges) = true ]; then
                 echo "My variable was: $(deployChanges)"
                else
                  echo "My variable was not true: $(deployChanges)"
                fi
            name: buildCatalystServerImages
            displayName: 'Build and Push Catalyst Server and API images'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "##vso[task.setvariable variable=myOutputVar;isoutput=true]this is from job A"
                git fetch --all
                echo "fetched all"
                git switch $(Build.SourceBranchName)
                echo "Switched branch"
                git status
                echo "git status"
                git config -l
                git pull
                git add ./migrations/*.ts
                git commit -an -m "Commit build changes [skip ci]"
                git merge origin/$(Build.SourceBranchName) -m "Merge published tag changes [skip ci]"
                git status
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: passOutput
            displayName: 'Push git changes'

          - bash: |
              echo "Use inside JobA: $(passOutput.myOutputVar)"
            name: useInside

      - job: B
        dependsOn: A
        variables:
          myVarFromJobA: $[ dependencies.A.outputs['passOutput.myOutputVar'] ]
        steps:
          - bash: |
              echo "JobB: $(myVarFromJobA)"
              echo "##vso[task.setvariable variable=myOutputVar;isoutput=true]this is from job B"
              echo "##vso[task.setvariable variable=Var2;isoutput=true]$(myVarFromJobA)"
              echo "JobB after output setter: $(myVarFromJobA)"
            name: passOutB
  - stage: Build
    dependsOn: Test
    jobs:
    - job: B1
      variables:
        myStageAVar: $[stageDependencies.Test.A.outputs['passOutput.myOutputVar']]
        myStageBVar: $[stageDependencies.Test.B.outputs['passOutB.myOutputVar']]
        myStageBVar2: $[stageDependencies.Test.B.outputs['passOutB.Var2']]
      steps:
        - checkout: none

        - bash: |
            echo "JobB1: $(myStageAVar)"
            echo "JobB1: $(myStageBVar)"
            echo "JobB1: $(myStageBVar2)"
