{
  "compilerOptions": {
    "module": "commonjs",
    "target": "ES2021",
    "lib": ["ES2021"],
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "strict": true,
    "moduleResolution": "node",
    "removeComments": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "forceConsistentCasingInFileNames": true,
    "esModuleInterop": true,
    // where to look for types
    "typeRoots": ["src/core/utils/types", "node_modules/@types"],
    // where to write the output
    "outDir": "./dist",
    "skipLibCheck": true,
  }
}