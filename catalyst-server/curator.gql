# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

type Query {
  """Get the ApplicationMeta"""
  getApplicationMeta(id: String!): ApplicationMeta

  """Get the PrivilegeGroup"""
  getPrivilegeGroup(name: String, id: String): PrivilegeGroup

  """Get a page of matching TenantAliases"""
  queryTenantAliases(searchSortInput: SearchSortInput, pagingInput: PagingInput): TenantAliasPage!

  """Get the TenantAlias"""
  getTenantAlias(handle: String, id: String): TenantAlias

  """Get the Tenant"""
  getTenant(handleOrAlias: String, id: String): Tenant

  """Get the TenantInfo"""
  getTenantInfo(handleOrAlias: String): TenantInfo

  """Get a page of matching Tenants"""
  queryTenants(searchSortInput: SearchSortInput, pagingInput: PagingInput): TenantPage!

  """Returns matching Tenants and Tenant Aliases"""
  findTenantInfoByName(name: String!): [TenantInfo!]!

  """Get the current User account"""
  getCurrentUser: User

  """Get the Attachment Location"""
  getAttachmentLocation(id: String!): Location!

  """Get a page of matching Categories"""
  queryCategories(searchSortInput: SearchSortInput, pagingInput: PagingInput): CategoryPage!

  """Get the Category"""
  getCategory(name: String, id: String): Category

  """Get a page of matching CurationEvents"""
  queryCurationEvents(searchSortInput: SearchSortInput, pagingInput: PagingInput): CurationEventPage!

  """Get the CurationEvent"""
  getCurationEvent(id: String!): CurationEvent

  """Get an ExistingSolution by ID"""
  getExistingSolution(id: String!): ExistingSolution

  """Get all ExistingSolutions for an Opportunity"""
  getExistingSolutionsByOpportunity(opportunityId: String!): [ExistingSolution!]!

  """Paginated search across Users and Opportunity Owners"""
  searchOwners(searchSortInput: SearchSortInput, pagingInput: PagingInput): SearchOwnerPage!

  """Get all Opportunities"""
  getOpportunities(scope: Scope): [Opportunity!]! @deprecated(reason: "User queryOpportunities() instead")

  """Get a page of matching Opportunities"""
  queryOpportunities(scope: Scope, searchSortInput: SearchSortInput, pagingInput: PagingInput): OpportunityPage!

  """Perform a calculation on Opportunities"""
  opportunityCalculation(scope: Scope, searchSortInput: SearchSortInput, calculation: Calculation!): CalcResponse!

  """Get the Opportunity"""
  getOpportunity(id: String!): Opportunity

  """Get a page of matching Projects"""
  queryProjects(searchSortInput: SearchSortInput, pagingInput: PagingInput): ProjectPage!

  """Get the Project"""
  getProject(id: String!): Project

  """Create a report"""
  calculation(scope: Scope, searchSortInput: SearchSortInput, entityName: String!, calculation: Calculation!): CalcResponse!

  """Create a report"""
  report(scope: Scope, reportInput: ReportInput!): ReportResponse!

  """Get a page of matching Stakeholders"""
  queryStakeholders(searchSortInput: SearchSortInput, pagingInput: PagingInput): StakeholderPage!

  """Get the Stakeholder"""
  getStakeholder(org: String, name: String, id: String): Stakeholder

  """Retrieves Users for this Tenant"""
  getUsers: [User!]! @deprecated(reason: "User queryUsers() instead")

  """Get a page of matching Users"""
  queryUsers(searchSortInput: SearchSortInput, pagingInput: PagingInput): UserPage!

  """Retrieve a User"""
  getUser(id: String!): User
}

type ApplicationMeta {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  curationMeta: JSONObject
}

"""
The javascript `Date` as string. Type represents date and time as the ISO Date string.
"""
scalar DateTime

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type PrivilegeGroup {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  privileges: [Privilege!]
  users: [User!]
}

type Privilege {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  resourceId: String
  resourceType: ResourceType!
}

"""The type of resource targeted"""
enum ResourceType {
  TENANT
}

type User {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  emailAddress: String!
  status: VerifiedStatus!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  options: UserOptions!
  appMeta: ApplicationMeta
  submissions: [Submission!]!
  curationEvent: [CurationEvent!]!
  roles: [Role!]!
  privilegeGroups: [PrivilegeGroup!]!
}

"""User or opportunity's verification status"""
enum VerifiedStatus {
  UNVERIFIED
  VERIFIED
}

type UserOptions {
  lastUsedServerVersion: String
  cookieAcceptance: Boolean
  submissionEmailOptOut: Boolean
  optOutOtherContact: Boolean
  optOutTeamUpdates: Boolean
  optOutAll: Boolean
}

type Submission {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  title: String!
  statement: String!
  context: String!
  benefits: String
  solutionConcepts: String
  campaign: String
  function: String
  user: User
  opportunities: [Opportunity!]!
}

type Opportunity {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  lastCurated: DateTime
  title: String!
  statement: String!
  context: String!
  status: OpportunityStatus!
  visibility: OpportunityVisibility!
  isTiCLOE: Boolean!
  org1: String
  org2: String
  org3: String
  org4: String
  benefits: String
  solutionConcepts: String
  additionalNotes: String
  campaign: String
  campaignNotes: String
  function: String
  statusNotes: String
  priority: Int
  priorityNotes: String
  solutionPathway: String
  solutionPathwayDetails: String
  permission: String
  attachmentNotes: String
  initiatives: String
  endorsements: String
  armyModernizationPriority: String
  echelonApplicability: String
  transitionInContactLineOfEffort: String
  operationalRules: String
  capabilityArea: String
  feasibilitySummary: String
  materielSolutionType: String
  DOTMLPFPPChange: [String!]
  capabilitySponsor: String
  armyCapabilityManager: String
  existingArmyRequirement: Boolean
  tenant: Tenant
  user: User
  owners: [OpportunityOwner!]!
  attachments: [Attachment!]!
  existingSolutions: [ExistingSolution!]!
  links: [Link!]!
  submissions: [Submission!]!
  projects: [Project!]!
  opportunities: [Opportunity!]!
  categories: [Category!]!
  stakeholders: [Stakeholder!]!
  ownedOpportunities: [RelatedOpportunity!]!
  owningOpportunities: [RelatedOpportunity!]!
  relatedOpportunityCount: Int
  linkedOpportunityCount: Int
  childOpportunityCount: Int
  parentOpportunityCount: Int
  curationInfo: CurationInfo
}

"""Status of the Opportunity"""
enum OpportunityStatus {
  PENDING
  APPROVED
  ARCHIVED
  DELETED
}

"""Sets the visibility status of an opportunity"""
enum OpportunityVisibility {
  PRIVATE
  ALL
}

type Tenant {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  handle: String!
  label: String
  meta: TenantMeta
}

type TenantMeta {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  config: JSONObject
  theme: JSONObject
  content: JSONObject
  filterOtherPrivateOpportunities: Boolean!
  serverConfiguration: JSONObject
}

type OpportunityOwner {
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime
  owner: Owner!
  opportunity: Opportunity!
  addedAt: DateTime!
  madePreviousAt: DateTime
  status: OpportunityOwnerStatus!
  removedAt: DateTime
  isRemoved: Boolean!
}

type Owner {
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime
  emailAddress: String!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  organizationRole: String
}

"""The status of the opportunity owner"""
enum OpportunityOwnerStatus {
  CURRENT
  PREVIOUS
  INITIAL
}

type Attachment {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  displayName: String
  notes: String
  encoding: String
  mimetype: String
  createdBy: User
}

type ExistingSolution {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  source: String!
  title: String
  organization: String
  needsModification: Boolean!
  opportunity: Opportunity!
}

type Link {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  url: String!
  notes: String
  createdBy: User
}

type Project {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  lastCurated: DateTime
  title: String!
  status: ProjectStatus!
  summary: String
  background: String
  startDate: DateTime
  endDate: DateTime
  goals: String
  type: String
  otherType: String
  statusNotes: String
  tenant: Tenant
  creator: User
  attachments: [Attachment!]!
  opportunities: [Opportunity!]!
  categories: [Category!]!
  projectStakeholders: [ProjectStakeholder!]!
  curationInfo: CurationInfo
}

"""The status of the project"""
enum ProjectStatus {
  PENDING
  ACTIVE
  ARCHIVED
  COMPLETED
  DELETED
}

type Category {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String!
  opportunities: [Opportunity!]!
  projects: [Project!]!
}

type ProjectStakeholder {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  project: Project!
  stakeholder: Stakeholder!
  type: ProjectStakeholderType!
}

type Stakeholder {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: String
  org: String
  opportunities: [Opportunity!]!
}

"""The type of project stakeholder"""
enum ProjectStakeholderType {
  DIVISION
  PERFORMER
  TRANSITION
}

type CurationInfo {
  users: [User!]!
  lastCurated: DateTime
}

type RelatedOpportunity {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  source: Opportunity!
  target: Opportunity!
  type: RelatedOpportunityType!
}

"""The type of related opportunity"""
enum RelatedOpportunityType {
  LINKED
  CHILD
}

type CurationEvent {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  user: User
  type: CurationEventType!
  entityType: EntityType!
  entityId: String
  data: CurationEventData
}

"""The type of curation event"""
enum CurationEventType {
  CREATE
  UPDATE
  DELETE
}

"""The type of entity targeted"""
enum EntityType {
  OPPORTUNITY
  USER
  PROJECT
  LINK
  ATTACHMENT
}

type CurationEventData {
  fields: [String!]
}

type Role {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  name: RoleNames!
}

"""User role"""
enum RoleNames {
  CURATOR
  ADMIN
  ANALYST
}

type TenantAliasPage {
  results: [TenantAlias!]!
  pageInfo: PageInfo!
}

type TenantAlias {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime
  handle: String!
  name: String
  label: String
  meta: TenantMeta
}

type PageInfo {
  hasNext: Boolean!
  hasPrevious: Boolean!
  lastCursor: String!
  lastPageSize: Int!
  retrievedCount: Int!
  totalCount: Int!
}

input SearchSortInput {
  searchFields: [SearchField!]
  jsonSearchGroups: [JSONObject!]
  sortFields: [SortField!]
}

input SearchField {
  fieldNames: [String!]!
  operator: SearchOperator = MATCH
  searchValue: AnyScalar
}

"""The search operation to apply to a field value"""
enum SearchOperator {
  EQ
  NE
  MATCH
  GT
  LT
  GTE
  LTE
  IN
  NIN
}

"""
Any scalar value (int, string, boolean, <date iso string>, or primitive array)
"""
scalar AnyScalar

input SortField {
  fieldName: String!
  ascending: Boolean = true
}

"""Controls list paging"""
input PagingInput {
  """The number of rows to request, per page.  Defaults to 100. Max 500"""
  pageSize: Int

  """How many results to skip or the 'zero-based' starting index"""
  cursor: String
}

type TenantInfo {
  tenantId: String!
  name: String!
  handle: String!
  label: String
  meta: TenantMeta
  serverVersion: String!
}

type TenantPage {
  results: [Tenant!]!
  pageInfo: PageInfo!
}

type Location {
  location: String!
}

type CategoryPage {
  results: [Category!]!
  pageInfo: PageInfo!
}

type CurationEventPage {
  results: [CurationEvent!]!
  pageInfo: PageInfo!
}

type SearchOwnerPage {
  results: [SearchOwnerResult!]!
  pageInfo: PageInfo!
}

type SearchOwnerResult {
  firstName: String!
  lastName: String!
  emailAddress: String!
  phone: String
  org1: String
  org2: String
  org3: String
  org4: String
  altContact: String
  organizationRole: String
  source: String
  id: String
}

input Scope {
  resources: [Resource!]!
}

input Resource {
  resourceId: String!
  resourceType: ResourceType!
}

type OpportunityPage {
  results: [Opportunity!]!
  pageInfo: PageInfo!
}

type CalcResponse {
  operationResults: [OperationResult!]!
}

type OperationResult {
  result: AnyScalar
}

input Calculation {
  operations: [FieldOperations!]!
  distinct: Boolean
}

input FieldOperations {
  fieldName: String
  operator: CalcOperator
}

"""The type of query calculation"""
enum CalcOperator {
  COUNT
  SUM
  AVG
}

type ProjectPage {
  results: [Project!]!
  pageInfo: PageInfo!
}

type ReportResponse {
  reports: [Report!]!
}

type Report {
  name: String!
  label: String!
  data: JSONObject
}

input ReportInput {
  queries: [ReportQuery!]!
}

input ReportQuery {
  reportName: String!
  label: String
  searchSortInput: SearchSortInput
}

type StakeholderPage {
  results: [Stakeholder!]!
  pageInfo: PageInfo!
}

type UserPage {
  results: [User!]!
  pageInfo: PageInfo!
}

type Mutation {
  """Create a new ApplicationMeta"""
  createApplicationMeta(input: ApplicationMetaInput!): ApplicationMeta!

  """Update an existing ApplicationMeta"""
  updateApplicationMeta(id: String!, input: ApplicationMetaInput!): ApplicationMeta!

  """Delete an existing ApplicationMeta"""
  deleteApplicationMeta(id: String!): Boolean!

  """Create a new PrivilegeGroup"""
  createPrivilegeGroup(input: CreatePrivilegeGroupInput!): PrivilegeGroup!

  """Update an existing PrivilegeGroup"""
  updatePrivilegeGroup(links: UpdatePrivilegeGroupLinks, id: String!, input: UpdatePrivilegeGroupInput!): PrivilegeGroup!

  """Delete an existing PrivilegeGroup"""
  deletePrivilegeGroup(id: String!): Boolean!

  """Create a new TenantAlias"""
  createTenantAlias(input: CreateTenantAliasInput!): TenantAlias!

  """Update an existing TenantAlias"""
  updateTenantAlias(id: String!, input: UpdateTenantAliasInput!): TenantAlias!

  """Delete an existing TenantAlias"""
  deleteTenantAlias(id: String!): Boolean!

  """Create a new Tenant"""
  createTenant(adminPass: String!, input: CreateTenantInput!): Tenant!

  """Update an existing Tenant"""
  updateTenant(id: String!, input: UpdateTenantInput!): Tenant!

  """Delete an existing Tenant"""
  deleteTenant(id: String!): Boolean!

  """(Unauthenticated) Register a new user with a password"""
  register(input: RegisterUserInput!): AuthResponse!

  """Authenticate a User and receive a token"""
  login(tenantHandle: String!, password: String!, userName: String!): AuthResponse!

  """Authenticate a User and receive a token"""
  renew: AuthResponse!

  """Update the current User account"""
  updateCurrentUser(links: UpdateCurrentUserLinks, input: UpdateCurrentUserInput!): User!

  """Delete the current User account"""
  deleteCurrentUser: Boolean!
  addAttachment(links: AttachmentLinks!, input: Upload!): Attachment!

  """Update an existing Attachment"""
  updateAttachment(input: UpdateAttachmentInput!): Attachment!

  """Delete the Attachment"""
  deleteAttachment(id: String!): Boolean!

  """Create a new Category"""
  createCategory(input: CreateCategoryInput!): Category!

  """Update an existing Category"""
  updateCategory(id: String!, input: UpdateCategoryInput!): Category!

  """Delete an existing Category"""
  deleteCategory(id: String!): Boolean!

  """Delete an existing CurationEvent"""
  deleteCurationEvent(id: String!): Boolean!

  """Create a new ExistingSolution"""
  createExistingSolution(links: ExistingSolutionLinks!, input: CreateExistingSolutionInput!): ExistingSolution!

  """Update an existing ExistingSolution"""
  updateExistingSolution(input: UpdateExistingSolutionInput!, id: String!): ExistingSolution!

  """Delete an existing ExistingSolution"""
  deleteExistingSolution(id: String!): Boolean!

  """
  Adds a new "CURRENT" Opportunity Owner, and sets previous owners to "PREVIOUS" status
  """
  assignOwner(input: AssignOpportunityOwnerInput!): OpportunityOwner!

  """Toggle isRemoved on an existing OpportunityOwner"""
  toggleRemovedOwner(id: String!): OpportunityOwner!

  """Create a new Opportunity"""
  createOpportunity(links: CreateOpportunityLinks!, input: CreateOpportunityInput!): Opportunity!

  """Update an existing Opportunity"""
  updateOpportunity(links: UpdateOpportunityLinks, id: String!, input: UpdateOpportunityInput!): Opportunity!

  """Delete an existing Opportunity"""
  deleteOpportunity(id: String!): Boolean!

  """Create a new Project from an Opportunity"""
  createProjectFromOpportunity(input: CreateProjectFromOpportunityInput!): Project!

  """Create a new Project"""
  createProject(links: CreateProjectLinks, input: CreateProjectInput!): Project!

  """Update an existing Project"""
  updateProject(links: UpdateProjectLinks, id: String!, input: UpdateProjectInput!): Project!

  """Delete an existing Project"""
  deleteProject(id: String!): Boolean!

  """Create a new Stakeholder"""
  createStakeholder(input: CreateStakeholderInput!): Stakeholder!

  """Update an existing Stakeholder"""
  updateStakeholder(id: String!, input: UpdateStakeholderInput!): Stakeholder!

  """Delete an existing Stakeholder"""
  deleteStakeholder(id: String!): Boolean!

  """(Admin) Create a new User"""
  createUser(links: UserLinks, input: CreateUserInput!): User!

  """(Admin) Update an existing User"""
  updateUser(links: UserLinks, id: String!, input: UpdateUserInput!): User!

  """(Admin) Delete an existing User"""
  deleteUser(id: String!): Boolean!
  addLink(links: LinkLinks!, input: AddLinkInput!): Link!

  """Update an existing Link"""
  updateLink(input: UpdateLinkInput!): Link!

  """Delete the Link"""
  deleteLink(id: String!): Boolean!
}

input ApplicationMetaInput {
  curationMeta: JSONObject
}

input CreatePrivilegeGroupInput {
  name: String
}

input UpdatePrivilegeGroupLinks {
  privileges: [UpdateLinks!]
}

input UpdateLinks {
  operator: UpdateOperator!
  ids: [String!]!
}

"""The update operation to apply to a field value"""
enum UpdateOperator {
  ADD
  RM
  SET
}

input UpdatePrivilegeGroupInput {
  name: String
}

input CreateTenantAliasInput {
  handle: String!
  name: String
  config: JSONObject
  theme: JSONObject
  content: JSONObject
}

input UpdateTenantAliasInput {
  handle: String
  name: String
  config: JSONObject
  theme: JSONObject
  content: JSONObject
}

input CreateTenantInput {
  name: String!
  handle: String!
  label: String
  config: JSONObject
  theme: JSONObject
  content: JSONObject
}

input UpdateTenantInput {
  name: String
  handle: String
  label: String
  config: JSONObject
  theme: JSONObject
  content: JSONObject
}

type AuthResponse {
  user: User!
  token: String!
  expiresAt: DateTime!
}

input RegisterUserInput {
  emailAddress: String!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  options: UserOptionsInput
  tenantHandle: String!
  password: String!
}

input UserOptionsInput {
  lastUsedServerVersion: String
  cookieAcceptance: Boolean
  submissionEmailOptOut: Boolean
  optOutOtherContact: Boolean
  optOutTeamUpdates: Boolean
  optOutAll: Boolean
}

input UpdateCurrentUserLinks {
  appMetaId: String
}

input UpdateCurrentUserInput {
  password: String
  firstName: String
  lastName: String
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  options: UserOptionsInput
}

input AttachmentLinks {
  opportunityId: String
  projectId: String
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

input UpdateAttachmentInput {
  id: String!
  displayName: String
  notes: String
}

input CreateCategoryInput {
  name: String!
}

input UpdateCategoryInput {
  name: String!
}

input ExistingSolutionLinks {
  opportunityId: String!
}

input CreateExistingSolutionInput {
  source: String!
  title: String
  organization: String
  needsModification: Boolean
}

input UpdateExistingSolutionInput {
  source: String
  title: String
  organization: String
  needsModification: Boolean
}

input AssignOpportunityOwnerInput {
  opportunityId: String!
  emailAddress: String!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  organizationRole: String
}

input CreateOpportunityLinks {
  userId: String!
}

input CreateOpportunityInput {
  title: String!
  statement: String!
  context: String!
  benefits: String
  status: OpportunityStatus
  solutionConcepts: String
  additionalNotes: String
  campaign: String
  function: String
  campaignNotes: String
  statusNotes: String
  priority: Int
  priorityNotes: String
  solutionPathway: String
  solutionPathwayDetails: String
  permission: String
  attachmentNotes: String
  armyModernizationPriority: String
  echelonApplicability: String
  isTiCLOE: Boolean
  transitionInContactLineOfEffort: String
  operationalRules: String
  capabilityArea: String
  initiatives: String
  endorsements: String
  visibility: OpportunityVisibility
  DOTMLPFPPChange: [String!]
  feasibilitySummary: String
  materielSolutionType: String
}

input UpdateOpportunityLinks {
  userId: String
  categories: [UpdateLinks!]
  stakeholders: [UpdateLinks!]
  opportunities: [UpdateLinks!]
  relatedOpportunities: [RelatedOpportunityUpdateLinks!]
}

input RelatedOpportunityUpdateLinks {
  operator: UpdateOperator!
  items: [RelatedOpportunityUpdateItem!]!
}

input RelatedOpportunityUpdateItem {
  id: String!
  type: RelatedOpportunityType!
}

input UpdateOpportunityInput {
  title: String
  statement: String
  context: String
  benefits: String
  status: OpportunityStatus
  solutionConcepts: String
  additionalNotes: String
  campaign: String
  function: String
  campaignNotes: String
  statusNotes: String
  priority: Int
  priorityNotes: String
  solutionPathway: String
  solutionPathwayDetails: String
  permission: String
  attachmentNotes: String
  initiatives: String
  endorsements: String
  armyModernizationPriority: String
  echelonApplicability: String
  capabilityArea: String
  isTiCLOE: Boolean
  transitionInContactLineOfEffort: String
  operationalRules: String
  DOTMLPFPPChange: [String!]
  feasibilitySummary: String
  materielSolutionType: String
  visibility: OpportunityVisibility
}

input CreateProjectFromOpportunityInput {
  title: String!
  opportunityId: String!
  includeProblemSolution: Boolean
  includeCategories: Boolean
  includeAttachments: Boolean
}

input CreateProjectLinks {
  creatorId: String
}

input CreateProjectInput {
  title: String!
  status: ProjectStatus
  summary: String
  background: String
  startDate: DateTime
  endDate: DateTime
  goals: String
  type: String
  otherType: String
  statusNotes: String
}

input UpdateProjectLinks {
  creatorId: String
  categories: [UpdateLinks!]
  opportunities: [UpdateLinks!]
  projectStakeholders: [ProjectStakeHolderUpdateLinks!]
}

input ProjectStakeHolderUpdateLinks {
  operator: UpdateOperator!
  items: [ProjectStakeHolderUpdateItem!]!
}

input ProjectStakeHolderUpdateItem {
  id: String!
  type: ProjectStakeholderType!
}

input UpdateProjectInput {
  title: String
  status: ProjectStatus
  summary: String
  background: String
  startDate: DateTime
  endDate: DateTime
  goals: String
  type: String
  otherType: String
  statusNotes: String
}

input CreateStakeholderInput {
  name: String
  org: String
}

input UpdateStakeholderInput {
  name: String
  org: String
}

input UserLinks {
  roleNames: [RoleNames!]
  privilegeGroups: [UpdateLinks!]
}

input CreateUserInput {
  emailAddress: String!
  password: String!
  firstName: String!
  lastName: String!
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  status: VerifiedStatus
  options: UserOptionsInput
}

input UpdateUserInput {
  password: String
  firstName: String
  lastName: String
  org1: String
  org2: String
  org3: String
  org4: String
  phone: String
  altContact: String
  status: VerifiedStatus
  options: UserOptionsInput
}

input LinkLinks {
  opportunityId: String
  projectId: String
}

input AddLinkInput {
  name: String!
  url: String!
  notes: String
}

input UpdateLinkInput {
  id: String!
  name: String
  url: String
  notes: String
}
